import asyncio
import json
import sys
from pydantic import BaseModel
from typing import Optional

# Import the necessary modules
sys.path.append('/home/<USER>/projects/records-api')
from records.services import schemas
from records.utils import json_utils


# Create a simple test model
class TestClient(BaseModel):
    name: str
    email: Optional[str] = None
    age: Optional[int] = None


async def test_validation():
    # Test case 1: Missing required field
    test_data = {
        # Missing 'name' field
        "email": "<EMAIL>",
        "age": 30
    }

    # Create a simple schema
    schema = {
        "type": "object",
        "properties": {
            "name": {"type": "string"},
            "email": {"type": "string", "format": "email"},
            "age": {"type": "integer", "minimum": 18}
        },
        "required": ["name"]
    }

    # Test validation
    validation_error = json_utils.validate_schema(test_data, schema, raise_exception=False)

    if validation_error:
        result = json_utils.parse_validation_error(validation_error)
        print("Test Case 1 (Missing required field):")
        print(json.dumps(result, indent=2))
        print("\n")

    # Test case 2: Wrong type
    test_data = {
        "name": "<PERSON> Doe",
        "email": "<EMAIL>",
        "age": "thirty"  # Should be an integer
    }

    validation_error = json_utils.validate_schema(test_data, schema, raise_exception=False)

    if validation_error:
        result = json_utils.parse_validation_error(validation_error)
        print("Test Case 2 (Wrong type):")
        print(json.dumps(result, indent=2))
        print("\n")

    # Test case 3: Value below minimum
    test_data = {
        "name": "John Doe",
        "email": "<EMAIL>",
        "age": 15  # Below minimum of 18
    }

    validation_error = json_utils.validate_schema(test_data, schema, raise_exception=False)

    if validation_error:
        result = json_utils.parse_validation_error(validation_error)
        print("Test Case 3 (Value below minimum):")
        print(json.dumps(result, indent=2))
        print("\n")

    # Test case 4: Invalid email format
    # Note: jsonschema doesn't validate email format by default without additional libraries
    # So we'll test a different format validation - pattern
    pattern_schema = {
        "type": "object",
        "properties": {
            "name": {"type": "string"},
            "email": {"type": "string", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"},
            "age": {"type": "integer", "minimum": 18}
        },
        "required": ["name"]
    }

    test_data = {
        "name": "John Doe",
        "email": "not-an-email",  # Invalid email format
        "age": 25
    }

    validation_error = json_utils.validate_schema(test_data, pattern_schema, raise_exception=False)

    if validation_error:
        result = json_utils.parse_validation_error(validation_error)
        print("Test Case 4 (Invalid email format):")
        print(json.dumps(result, indent=2))
        print("\n")

    # Test case 5: Valid data
    test_data = {
        "name": "John Doe",
        "email": "<EMAIL>",
        "age": 25
    }

    validation_error = json_utils.validate_schema(test_data, schema, raise_exception=False)

    if validation_error:
        result = json_utils.parse_validation_error(validation_error)
        print("Test Case 5 (Valid data):")
        print(json.dumps(result, indent=2))
    else:
        print("Test Case 5 (Valid data): Validation passed!")


if __name__ == "__main__":
    asyncio.run(test_validation())
