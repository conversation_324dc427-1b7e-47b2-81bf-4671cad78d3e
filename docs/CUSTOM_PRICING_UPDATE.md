# Custom Pricing Update for Client Subscriptions

## Overview
Updated the client subscription system to support manager-controlled custom pricing, giving full flexibility to set negotiated prices for each client.

## Changes Made

### 1. Updated API Models
**ClientSubscriptionCreate** and **ClientSubscriptionUpdate** now include:
- `total_price: Optional[float]` - Custom price agreed with client

### 2. New Pricing Logic

#### When Creating Client Subscription:
```python
# Option 1: Custom price (manager sets negotiated price)
{
    "subscription_id": 1,
    "total_price": 175.50,  # Custom negotiated price
    "notes": "Special rate for long-term client"
}

# Option 2: Default price (uses subscription base price)
{
    "subscription_id": 1,
    # total_price not specified - will use subscription.price
    "notes": "Standard subscription pricing"
}
```

#### When Updating Client Subscription:
```python
# Manager can update the price at any time
{
    "total_price": 200.00,  # New negotiated price
    "notes": "Price updated after contract renewal"
}
```

### 3. Key Features

✅ **Manager Control**: Full control over subscription pricing
✅ **Flexible Pricing**: Set any price regardless of service composition  
✅ **Default Fallback**: Uses subscription base price if custom price not set
✅ **Service Customization**: Individual services can still be customized (for reference)
✅ **Discount Support**: discount_amount and discount_percent still available

### 4. What Changed in Code

#### API Layer (`records/api/client_subscriptions.py`):
- Added `total_price` field to create/update models
- Updated creation logic to use custom price or fallback to subscription price
- Removed automatic price recalculation based on services
- Added comments explaining manager-controlled pricing

#### Database Layer:
- No database schema changes needed
- Added comments to `calculate_subscription_total_price` function

### 5. Business Logic

1. **Manager sets custom price** → Uses that exact price
2. **No custom price provided** → Uses subscription's base price  
3. **Service customization** → Doesn't affect total price (for reference only)
4. **Individual service prices** → Used for reporting/reference, not billing

### 6. Example Workflow

```bash
# 1. Create subscription with custom price
POST /api/v1/clients/123/subscriptions
{
    "subscription_id": 1,
    "total_price": 150.00,
    "start_date": "2024-01-01T00:00:00Z",
    "notes": "Negotiated rate for enterprise client"
}

# 2. Update price after renegotiation  
PUT /api/v1/clients/123/subscriptions/456
{
    "total_price": 175.00,
    "notes": "Price increased after contract renewal"
}

# 3. Customize services (doesn't affect price)
PUT /api/v1/clients/123/subscriptions/456/services/service-1
{
    "is_active": false,
    "notes": "Client doesn't need this service"
}
```

### 7. Benefits

- **Flexibility**: Managers can set any price based on negotiations
- **Simplicity**: No complex automatic calculations
- **Control**: Full pricing control in manager's hands
- **Transparency**: Clear separation between service composition and billing
- **Scalability**: Easy to handle special pricing arrangements

## Testing

Run the test to verify functionality:
```bash
python test_custom_pricing.py
```

The system now fully supports manager-controlled custom pricing for client subscriptions!
