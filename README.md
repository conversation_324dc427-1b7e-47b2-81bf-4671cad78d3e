# Records API

A comprehensive CRM-like system for managing client records with advanced document processing and data extraction capabilities. The system integrates with IRA (Intelligent Record Analysis) services to automatically process uploaded documents, extract structured data, and maintain detailed client information including business registrations, bank accounts, services, and compliance tracking.

## Features

- **Client Management**: Complete client lifecycle management with detailed business information
- **Document Processing**: Automated document upload, analysis, and data extraction using IRA integration
- **Bank Account Management**: Track client bank accounts, authorized signers, and payment cards
- **Service & Subscription Management**: Manage client services, subscriptions, and billing
- **Address & Contact Management**: Maintain multiple addresses and contact information per client
- **Task Management**: Automated task generation and tracking for client services
- **File Management**: Secure file upload, storage, and processing with metadata extraction
- **Search & Analytics**: Advanced search capabilities across all client data
- **Compliance Tracking**: Monitor registration renewals, tax reporting, and other compliance requirements
- **Multi-tenant Support**: Manager-based access control and workspace isolation

## Prerequisites

- **Python**: Version 3.12+
- **PostgreSQL**: Version 14+ (local or remote)
- **IRA API Access**: Valid API token and workspace ID for document processing
- **Docker** (optional): For containerized deployment

## Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd records-api
```

### 2. Create Virtual Environment

```bash
python -m venv venv
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Database Setup

#### Option A: Use Local PostgreSQL

Install PostgreSQL and create a database:

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database
sudo -u postgres createdb records
```

#### Option B: Use Docker PostgreSQL

Run PostgreSQL using the provided script:

```bash
# Background mode
./run_postgres.sh

# Interactive mode (removes container on exit)
./run_postgres.sh -i
```

Or run directly with Docker:

```bash
docker run -p 5432:5432 --name postgres \
  -e POSTGRES_PASSWORD=password \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_DB=records \
  -e PGDATA=/var/lib/postgresql/data/pgdata \
  --rm -it -v $HOME/pgdata:/var/lib/postgresql/data/pgdata:z \
  postgres:16.6
```

## Configuration

### Environment Variables

Create a `.env` file or set the following environment variables:

#### Database Configuration
```bash
export PGHOST=localhost
export PGUSER=postgres
export PGPASSWORD=password
export PGDATABASE=records
```

#### IRA API Configuration (Required)
```bash
export IRA_API_URL=https://your-ira-api-url
export IRA_API_TOKEN=your-ira-api-token
export IRA_API_WORKSPACE_ID=your-workspace-id
export DEBUG_IRA=false  # Set to true for verbose IRA API logging
```

#### Application Configuration
```bash
export PORT=8084                    # API server port
export WORKERS=1                    # Number of worker processes
export ENV_PREFIX=local             # Environment prefix for isolation
export BLOWFISH_SALT=your-secret-key  # Encryption key for sensitive data
```

#### Optional File Management
```bash
export FILE_API_URL=               # External file API URL (optional)
export CONVERTER_API_URL=          # Document converter API URL (optional)
```

### Configuration File

For convenience, create a configuration file (e.g., `~/.recordsrc`) and source it:

```bash
# ~/.recordsrc
export PGHOST=localhost
export PGUSER=postgres
export PGPASSWORD=password
export PGDATABASE=records
export IRA_API_URL=https://your-ira-api-url
export IRA_API_TOKEN=your-ira-api-token
export IRA_API_WORKSPACE_ID=123
export ENV_PREFIX=local
export BLOWFISH_SALT=your-secret-key
export PORT=8084
```

Then activate with:
```bash
source ~/.recordsrc
```

## Database Migration

### Run Migrations

After setting up the database and environment variables, run the database migrations:

```bash
# Upgrade to latest schema
./migration_upgrade.sh

# Or use alembic directly
alembic upgrade head
```

### Create New Migrations

When you modify database models, create a new migration:

```bash
# Create migration with auto-generated changes
./migration_add.sh "description of changes"

# Or use alembic directly
alembic revision --autogenerate -m "description of changes"
```

## Running the Application

### Development Mode

```bash
# Make sure environment variables are set
source ~/.recordsrc

# Run the application
python records/main.py
```

The API will be available at `http://localhost:8084`

### Production Mode

```bash
# Using uvicorn directly
uvicorn records.main:app --workers 1 --host 0.0.0.0 --port 8084 --no-access-log --loop uvloop

# Or with environment variables
PORT=8084 WORKERS=1 python records/main.py
```

## Docker Deployment

### Build Docker Image

```bash
# Build the image
./build.sh

# Build and push to registry
./build.sh --push

# Or build manually
docker build --tag kuberlab/records-api:latest -f Dockerfile .
```

### Run with Docker

#### Using the provided script:

```bash
# Set environment variables first
export PGPASSWORD=password
export PGUSER=postgres
export PGHOST=localhost  # or your database host
export PGDATABASE=records
export BLOWFISH_SALT=your-secret-key
export ENV_PREFIX=prod

# Run the container
./run_records.sh
```

#### Using Docker directly:

```bash
docker run -it --rm --net=host --name=records \
  -e PGPASSWORD=password \
  -e PGUSER=postgres \
  -e PGHOST=localhost \
  -e PGDATABASE=records \
  -e IRA_API_URL=https://your-ira-api-url \
  -e IRA_API_TOKEN=your-ira-api-token \
  -e IRA_API_WORKSPACE_ID=123 \
  -e BLOWFISH_SALT=your-secret-key \
  -e ENV_PREFIX=prod \
  kuberlab/records-api:latest
```

### Kubernetes Deployment

The project includes Helm charts for Kubernetes deployment:

```bash
# Deploy using Helm
helm install records-api ./chart \
  --set api.db.host=your-db-host \
  --set api.db.password=your-db-password \
  --set api.envVars[0].name=IRA_API_URL \
  --set api.envVars[0].value=https://your-ira-api-url

# Or use predefined values
helm install records-api ./chart -f chart/values.yaml
```

## API Documentation

### Swagger/OpenAPI

Once the application is running, access the interactive API documentation:

- **Swagger UI**: `http://localhost:8084/docs`
- **ReDoc**: `http://localhost:8084/redoc`
- **OpenAPI JSON**: `http://localhost:8084/openapi.json`

### Health Check

```bash
curl http://localhost:8084/health
```

### Main API Endpoints

- **Clients**: `/api/v1/clients` - Client management
- **Client Uploads**: `/api/v1/clients_uploads` - Document upload and processing
- **Files**: `/api/v1/clients/{client_id}/files` - File management
- **Services**: `/api/v1/services` - Service management
- **Subscriptions**: `/api/v1/subscriptions` - Subscription management
- **Search**: `/api/v1/search` - Search across client data
- **Bank Accounts**: `/api/v1/clients/{client_id}/bank_accounts` - Banking information
- **Addresses**: `/api/v1/addresses` - Address management

## Project Structure

```
records-api/
├── records/                    # Main application package
│   ├── api/                   # API endpoints and routing
│   │   ├── clients.py         # Client management endpoints
│   │   ├── clients_uploads.py # Document upload processing
│   │   ├── files.py          # File management
│   │   ├── services.py       # Service management
│   │   └── ...               # Other API modules
│   ├── db/                   # Database layer
│   │   ├── models.py         # SQLAlchemy models
│   │   ├── api.py           # Database API functions
│   │   └── ...              # Specific DB modules
│   ├── services/             # Business logic services
│   │   ├── file_processing.py # Document processing
│   │   ├── client_upload_service.py # Upload lifecycle
│   │   └── ...              # Other services
│   ├── ira_chat_client/      # IRA API integration
│   ├── utils/                # Utility functions
│   ├── config/               # Configuration management
│   └── main.py              # Application entry point
├── migrations/               # Database migrations (Alembic)
├── chart/                   # Kubernetes Helm charts
├── scripts/                 # Utility scripts
├── tests/                   # Test suite
├── Dockerfile              # Docker configuration
├── requirements.txt        # Python dependencies
├── alembic.ini            # Database migration config
└── README.md              # This file
```

## Development

### Running Tests

```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_json_merge.py

# Run with coverage
python -m pytest tests/ --cov=records
```

### Code Quality

The project follows Python best practices:

- **Type Hints**: Extensive use of type annotations
- **Pydantic Models**: Data validation and serialization
- **SQLAlchemy 2.0**: Modern async database operations
- **FastAPI**: High-performance async web framework
- **Alembic**: Database migration management

### Adding New Features

1. **Database Changes**: Create models in `records/db/models.py`
2. **API Endpoints**: Add routes in appropriate `records/api/` modules
3. **Business Logic**: Implement services in `records/services/`
4. **Database Operations**: Add functions in `records/db/` modules
5. **Migrations**: Generate with `./migration_add.sh "description"`

### Environment-Specific Configuration

The application supports multiple environments through the `ENV_PREFIX` variable:

- `local` - Local development
- `dev` - Development environment
- `prod` - Production environment

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify PostgreSQL is running
   - Check environment variables (PGHOST, PGUSER, PGPASSWORD, PGDATABASE)
   - Ensure database exists

2. **IRA API Connection Issues**
   - Verify IRA_API_URL, IRA_API_TOKEN, and IRA_API_WORKSPACE_ID
   - Check network connectivity to IRA service
   - Enable DEBUG_IRA=true for detailed logging

3. **Migration Errors**
   - Ensure database is accessible
   - Check for conflicting schema changes
   - Review migration files in `migrations/versions/`

4. **File Upload Issues**
   - Check file permissions and storage configuration
   - Verify IRA API integration for document processing
   - Review file size limits

### Logs

Application logs provide detailed information about operations:

```bash
# View logs when running with Docker
docker logs records

# Enable debug logging
export DEBUG_IRA=true
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

[Add your license information here]

## Support

For support and questions:
- Review the API documentation at `/docs`
- Check the troubleshooting section above
- [Add contact information or issue tracker]
