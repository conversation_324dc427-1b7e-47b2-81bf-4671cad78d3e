"""
Test the sequential detection workflow implementation.
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from records.services.client_upload_service import ClientUploadService
from records.db import models


class TestSequentialDetectionWorkflow:
    """Test the new sequential detection workflow."""

    def setup_method(self):
        """Set up test fixtures."""
        with patch('records.services.client_upload_service.SharedConfig'):
            self.service = ClientUploadService()
            self.service.ira_client = Mock()
            self.service.ira_client.adetection_item_create = AsyncMock()

    def test_detection_type_config_includes_tax_report(self):
        """Test that tax report detection is included in configuration."""
        from records.services.client_upload_service import DetectionTypeConfig
        
        all_types = DetectionTypeConfig.get_all_types()
        assert models.DetectionType.TAX_REPORT_DETECTION in all_types
        
        tax_report_config = DetectionTypeConfig.get_config(models.DetectionType.TAX_REPORT_DETECTION)
        assert tax_report_config is not None
        assert tax_report_config['detection_id_field'] == 'tax_report_detection_id'
        assert tax_report_config['detection_item_id_field'] == 'tax_report_detection_item_id'

    def test_metadata_type_detection_logic(self):
        """Test the logic for determining detection type based on metadata."""
        # Test tax report detection
        metadata_output_tax = {'type': 'Tax Report', 'name': 'Test Tax Report'}
        assert 'tax report' in metadata_output_tax.get('type', '').lower()
        
        # Test client data detection
        metadata_output_client = {'type': 'Certificate of Incorporation', 'name': 'Test Certificate'}
        assert 'tax report' not in metadata_output_client.get('type', '').lower()

    @pytest.mark.asyncio
    async def test_conditional_detection_spawning_tax_report(self):
        """Test that tax report detection is spawned for tax report documents."""
        upload_dict = {
            'id': 1,
            'file_id': 'test-file-id',
            'tax_report_detection_id': 123,
            'client_data_detection_id': 456
        }
        
        metadata_item = {
            'status': 'SUCCESS',
            'results': [{'output': '{"type": "Tax Report", "name": "Test Tax Report"}'}]
        }
        
        # Mock the file operations
        with patch('records.services.client_upload_service.db_api') as mock_db_api, \
             patch('records.services.client_upload_service.detections') as mock_detections, \
             patch('records.services.client_upload_service.SharedConfig') as mock_config:
            
            # Mock file database object
            mock_file_db = Mock()
            mock_file_db.name = 'test_file.pdf'
            mock_file_db.description = 'Test file description'

            mock_db_api.get_client_file_by_id = AsyncMock(return_value=mock_file_db)
            mock_db_api.update_client_upload = AsyncMock()
            mock_detections.get_upload_file_path = Mock(return_value='test/path')
            
            # Mock file manager
            mock_file_manager = Mock()
            mock_file_manager.read_file = AsyncMock()
            mock_file_manager.get_data = AsyncMock(return_value=b'test file data')
            mock_config.return_value.file_manager = mock_file_manager
            
            # Mock IRA client response
            self.service.ira_client.adetection_item_create.return_value = {'id': 789}
            
            await self.service._start_conditional_detection(upload_dict, metadata_item)
            
            # Verify tax report detection was triggered
            self.service.ira_client.adetection_item_create.assert_called_once_with(
                123,  # tax_report_detection_id
                fp=b'test file data',
                file_name='test_file.pdf',
                title='test_file.pdf',
                description='Test file description'
            )
            
            # Verify database was updated with tax report detection item ID
            mock_db_api.update_client_upload.assert_called_once()
            call_args = mock_db_api.update_client_upload.call_args[0][1]
            assert call_args['tax_report_detection_item_id'] == 789

    @pytest.mark.asyncio
    async def test_conditional_detection_spawning_client_data(self):
        """Test that client data detection is spawned for non-tax report documents."""
        upload_dict = {
            'id': 1,
            'file_id': 'test-file-id',
            'tax_report_detection_id': 123,
            'client_data_detection_id': 456
        }
        
        metadata_item = {
            'status': 'SUCCESS',
            'results': [{'output': '{"type": "Certificate of Incorporation", "name": "Test Certificate"}'}]
        }
        
        # Mock the file operations
        with patch('records.services.client_upload_service.db_api') as mock_db_api, \
             patch('records.services.client_upload_service.detections') as mock_detections, \
             patch('records.services.client_upload_service.SharedConfig') as mock_config:

            # Mock file database object
            mock_file_db = Mock()
            mock_file_db.name = 'test_certificate.pdf'
            mock_file_db.description = 'Test certificate description'

            mock_db_api.get_client_file_by_id = AsyncMock(return_value=mock_file_db)
            mock_db_api.update_client_upload = AsyncMock()
            mock_detections.get_upload_file_path = Mock(return_value='test/path')
            
            # Mock file manager
            mock_file_manager = Mock()
            mock_file_manager.read_file = AsyncMock()
            mock_file_manager.get_data = AsyncMock(return_value=b'test file data')
            mock_config.return_value.file_manager = mock_file_manager
            
            # Mock IRA client response
            self.service.ira_client.adetection_item_create.return_value = {'id': 789}
            
            await self.service._start_conditional_detection(upload_dict, metadata_item)
            
            # Verify client data detection was triggered
            self.service.ira_client.adetection_item_create.assert_called_once_with(
                456,  # client_data_detection_id
                fp=b'test file data',
                file_name='test_certificate.pdf',
                title='test_certificate.pdf',
                description='Test certificate description'
            )
            
            # Verify database was updated with client data detection item ID
            mock_db_api.update_client_upload.assert_called_once()
            call_args = mock_db_api.update_client_upload.call_args[0][1]
            assert call_args['client_data_detection_item_id'] == 789

    def test_tax_report_output_format(self):
        """Test the new tax report output format handling."""
        # Test new format with tax_reports array
        new_format_output = {
            'tax_reports': [{'type': 'Federal Income Tax', 'fiscal_year': '2023'}],
            'client_data': {},
            'merged_client_data': {}
        }
        
        assert 'tax_reports' in new_format_output
        assert isinstance(new_format_output['tax_reports'], list)
        assert len(new_format_output['tax_reports']) == 1
        
        # Test legacy format
        legacy_format_output = {
            'ein': '12-3456789',
            'name': 'Test Company'
        }
        
        assert 'tax_reports' not in legacy_format_output

    def test_detection_types_constants(self):
        """Test that all detection type constants are properly defined."""
        assert hasattr(models.DetectionType, 'CLIENT_DATA')
        assert hasattr(models.DetectionType, 'SMART_MERGE')
        assert hasattr(models.DetectionType, 'METADATA_EXTRACTION')
        assert hasattr(models.DetectionType, 'TAX_REPORT_DETECTION')
        
        assert hasattr(models.DetectionName, 'CLIENT_DATA')
        assert hasattr(models.DetectionName, 'SMART_MERGE')
        assert hasattr(models.DetectionName, 'METADATA_EXTRACTION')
        assert hasattr(models.DetectionName, 'TAX_REPORT_DETECTION')
