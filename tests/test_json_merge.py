import os
from pathlib import Path
import sys
import pytest

# Add project root so that `import records` works when running tests directly.
ROOT_DIR = Path(__file__).resolve().parents[1]
if str(ROOT_DIR) not in sys.path:
    sys.path.insert(0, str(ROOT_DIR))

from langchain_openai import Chat<PERSON>penA<PERSON>

from records.utils.json_utils import adict_merge, amerge_jsons


@pytest.fixture(scope="session")
def llm() -> "ChatOpenAI":  # type: ignore[name-defined]
    """Instantiate a fast / inexpensive model for live tests.

    We purposefully use the `gpt-4o-mini` model which is both powerful and
    relatively cheap so the test can run quickly while still exercising the LLM
    powered merge logic.
    """
    api_key = os.getenv("OPENAI_API_KEY", None)
    if not api_key:
        pytest.skip("OPENAI_API_KEY not found in environment; skipping live LLM tests.")

    return ChatOpenAI(model_name="gpt-4o-mini", temperature=0.5, timeout=45)


# ---------------------------------------------------------------------------
# Tests
# ---------------------------------------------------------------------------


@pytest.mark.asyncio
async def test_dict_merge_overwrite_llm_lists(llm):
    """Merging two JSON lists that describe the *same* logical object should
    collapse into a single, consolidated entry chosen by the LLM.
    """

    # First object lacks an e-mail address, second has it. They should be merged
    # into a single element which contains the richer field set coming from
    # `list_two` as per the prompt rules in `MERGE_LISTS_PROMPT`.
    list_one = [
        {"firstname": "John", "lastname": "Doe", "email": ""}
    ]
    list_two = [
        {"firstname": "John", "lastname": "Doe", "email": "<EMAIL>"}
    ]

    json_one = {"clients": list_one}
    json_two = {"clients": list_two}

    merged = await adict_merge(json_one, json_two, mode="overwrite_llm", llm=llm)

    # It should have collapsed to a single entry.
    assert len(merged["clients"]) == 1, "Expected the two objects to be merged into one"

    merged_list = [{
        "firstname": "John",
        "lastname": "Doe",
        "email": "<EMAIL>",
    }]

    # The LLM should have filled in the e-mail coming from the second list.
    assert merged["clients"] == merged_list


@pytest.mark.asyncio
async def test_merge_jsons_multiple_objects_no_llm():
    """`merge_jsons` should correctly process multiple dictionaries."""

    json_a = {
        "name": "Alice",
        "clients": [{"firstname": "Alice", "lastname": "A"}]
    }
    json_b = {
        "name": "Alice B.",
        "clients": [{"firstname": "Alice", "lastname": "A", "email": "<EMAIL>"}],
    }
    json_c = {"age": 30}

    merged = await amerge_jsons([json_a, json_b, json_c])

    # Basic structural assertions
    assert merged["name"] == "Alice B.", "Later dictionaries should overwrite previous keys"
    assert merged["age"] == 30

    # The list under `clients` should have been merged by the LLM
    assert len(merged["clients"]) == 2
    assert merged["clients"][1]["email"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_merge_jsons_multiple_objects(llm):
    """`merge_jsons` should correctly process multiple dictionaries."""

    json_a = {
        "name": "Alice",
        "clients": [{"firstname": "Alice", "lastname": "A"}]
    }
    json_b = {
        "name": "Alice B.",
        "clients": [{"firstname": "Alice", "lastname": "A", "email": "<EMAIL>"}],
    }
    json_c = {"age": 30}

    merged = await amerge_jsons([json_a, json_b, json_c], llm=llm)

    # Basic structural assertions
    assert merged["name"] == "Alice B.", "Later dictionaries should overwrite previous keys"
    assert merged["age"] == 30

    # The list under `clients` should have been merged by the LLM
    assert len(merged["clients"]) == 1
    assert merged["clients"][0]["email"] == "<EMAIL>"


# ---------------------------------------------------------------------------
# Complex nested structure tests
# ---------------------------------------------------------------------------

def _addr(street: str | None = None, phone: str | None = None):
    """Helper to construct minimal ClientAddress-like dict."""
    return {
        "address_type": "business",
        "phone": phone or "",
        "address": {
            "full_address": street or "123 Main St",
            "city": "Springfield",
            "state": "IL",
        },
    }


@pytest.mark.asyncio
async def test_merge_nested_addresses_llm(llm):
    """Verify list merging when each element contains a nested dict."""

    json_one = {"addresses": [_addr(phone="")]}  # missing phone
    json_two = {"addresses": [_addr(phone="555-1111")]}  # richer data

    merged = await adict_merge(json_one, json_two, mode="overwrite_llm", llm=llm)

    assert len(merged["addresses"]) == 1
    addr = merged["addresses"][0]
    assert addr["phone"] == "555-1111"
    # nested dict preserved
    assert addr["address"]["full_address"] == "123 Main St"


def _service(price: float | None = None):
    return {
        "service_id": "svc-001",
        "service": {"title": "Bookkeeping", "price_type": "monthly"},
        "price": price,
    }


@pytest.mark.asyncio
async def test_merge_services_list_mixed_structures(llm):
    """One list item has full nested service, another has only primitive fields."""

    list_one = [_service(price=None)]
    list_two = [{"service_id": "svc-001", "price": 99.0}]

    json_one = {"services": list_one}
    json_two = {"services": list_two}

    merged = await adict_merge(json_one, json_two, mode="overwrite_llm", llm=llm)

    assert len(merged["services"]) == 1
    svc = merged["services"][0]
    # price should come from second list
    assert svc["price"] == 99.0
    # nested service details should be preserved
    assert svc["service"]["title"] == "Bookkeeping"
