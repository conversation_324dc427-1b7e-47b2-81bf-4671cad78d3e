#!/usr/bin/env python3
"""
Test script for multiple attached objects functionality
"""
import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from records.db import api as db_api, base
from records.db import models


async def test_attached_objects():
    """Test the new attached objects functionality"""
    
    # Initialize database connection
    base.get_engine()
    
    async with base.session_context():
        print("Testing ClientUploadAttachedObject functionality...")
        
        # Test 1: Create a client upload attached object
        print("\n1. Testing create attached object...")
        try:
            # First, let's see if there are any existing client uploads
            uploads = await db_api.list_client_uploads(limit=1)
            if uploads:
                upload = uploads[0]
                print(f"Found existing upload: {upload.id}")
                
                # Test creating an attached object
                attached_obj_data = {
                    'client_upload_id': upload.id,
                    'object_type': 'client',
                    'object_id': 'test-client-id-123'
                }
                
                attached_obj = await db_api.create_client_upload_attached_object(attached_obj_data)
                print(f"Created attached object: {attached_obj.id}")
                
                # Test 2: List attached objects for upload
                print("\n2. Testing list attached objects...")
                attached_objects = await db_api.list_upload_attached_objects_by_upload_id(upload.id)
                print(f"Found {len(attached_objects)} attached objects for upload {upload.id}")
                for obj in attached_objects:
                    print(f"  - {obj.object_type}: {obj.object_id}")
                
                # Test 3: Replace attached objects
                print("\n3. Testing replace attached objects...")
                new_objects = [
                    {'object_type': 'client', 'object_id': 'client-1'},
                    {'object_type': 'client', 'object_id': 'client-2'}
                ]
                
                created_objects = await db_api.replace_upload_attached_objects(upload.id, new_objects)
                print(f"Replaced with {len(created_objects)} new attached objects")
                
                # Verify replacement
                attached_objects = await db_api.list_upload_attached_objects_by_upload_id(upload.id)
                print(f"After replacement, found {len(attached_objects)} attached objects:")
                for obj in attached_objects:
                    print(f"  - {obj.object_type}: {obj.object_id}")
                
                # Test 4: Test the model relationship
                print("\n4. Testing model relationship...")
                upload_with_objects = await db_api.get_client_upload_by_id(upload.id)
                upload_dict = upload_with_objects.to_dict()
                print(f"Upload dict includes attached_objects: {'attached_objects' in upload_dict}")
                
                # Clean up
                print("\n5. Cleaning up...")
                await db_api.delete_upload_attached_objects_by_upload_id(upload.id)
                print("Cleaned up test attached objects")
                
            else:
                print("No existing uploads found to test with")
                
        except Exception as e:
            print(f"Error during testing: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_attached_objects())
