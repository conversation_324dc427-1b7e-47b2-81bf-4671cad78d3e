
import asyncio
import json
import os
import time

from langchain_openai import ChatOpenAI

from records.utils.json_utils import adict_merge
from records.utils import json_utils
from records.utils import utils


async def main():
    utils.setup_logging()
    api_key = os.getenv("OPENAI_API_KEY", None)
    if not api_key:
        raise ValueError("OPENAI_API_KEY not found in environment; skipping live LLM tests.")

    llm = ChatOpenAI(model_name="gpt-4o-mini", temperature=0.5, timeout=45)

    with open('merge_test.json', 'r') as f:
        data = json.loads(f.read())

    t = time.time()
    merged = await adict_merge(data['left'], data['right'], mode='overwrite_llm', llm=llm)
    print(f'Time: {time.time() - t}')
    t = time.time()
    merged2 = await adict_merge(data['left'], data['right'], mode='overwrite_llm', llm=llm)
    print(f'Time: {time.time() - t}')
    if merged != merged2:
        print('Different results!')
        print(json_utils.diff_json(merged, merged2))


if __name__ == '__main__':
    asyncio.run(main())
