#!/usr/bin/env python3
"""
Test script for enhanced Excel styling in client report generator.
This script demonstrates the improved styling capabilities.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from records.services.client_report_generator import ClientReportGenerator


async def test_enhanced_styling():
    """Test the enhanced Excel styling with sample data."""
    
    # Create a mock client data structure for testing
    mock_client_data = {
        'name': 'Test Corporation Inc.',
        'ein': '12-3456789',
        'legal_ent_type': 'Corporation',
        'description': 'Software Development Company',
        'naicscode': '541511',
        'primary_registration': {
            'state_of_incorporation': 'Delaware',
            'state_entity': 'DE123456',
            'registered_date': '2020-01-15'
        },
        'secondary_registrations': [],
        'accounting_method': 'ACCRUAL',
        'financial_year_end': '12/31',
        'addresses': [
            {
                'address_type': 'mailing',
                'address': {
                    'street': '123 Main Street',
                    'city': 'San Francisco',
                    'state': 'CA',
                    'zip': '94105'
                }
            }
        ],
        'company_phone': '(*************',
        'notes_main': 'Test company for demonstration',
        'llc_shareholders': [
            {
                'person': {
                    'firstname': 'John',
                    'lastname': 'Doe',
                    'citizenship': 'US',
                    'address': '456 Oak Ave, San Francisco, CA 94102',
                    'phone': '(*************'
                },
                'ownership': '60%'
            },
            {
                'person': {
                    'firstname': 'Jane',
                    'lastname': 'Smith',
                    'citizenship': 'US',
                    'address': '789 Pine St, San Francisco, CA 94103',
                    'phone': '(*************'
                },
                'ownership': '40%'
            }
        ],
        'contacts': [
            {
                'person': {
                    'firstname': 'John',
                    'lastname': 'Doe',
                    'phone': '(*************'
                },
                'position': 'CEO',
                'phone': '(*************'
            }
        ],
        'bank_accounts': [
            {
                'bank_name': 'First National Bank',
                'aba_number': '*********',
                'account_number': '**********',
                'notes': 'Primary business account'
            }
        ],
        'tax_reports': [],
        'fedtaxforms': [],
        'payroll': False,
        'notes_accounting': 'Uses QuickBooks for accounting',
        'notes_shareholders': 'All shareholders are US citizens',
        'notes_contacts': 'CEO is primary contact'
    }
    
    # Create generator instance
    generator = ClientReportGenerator()
    
    # Mock the get_client_data function for testing
    async def mock_get_client_data(client_id):
        return None, mock_client_data
    
    # Temporarily replace the clients.get_client_data function
    import records.services.clients as clients_module
    original_get_client_data = clients_module.get_client_data
    clients_module.get_client_data = mock_get_client_data
    
    try:
        print("Testing enhanced Excel styling...")
        
        # Test with enhanced styling (xlsxwriter)
        print("Generating Excel with enhanced styling (xlsxwriter)...")
        excel_data_enhanced, filename_enhanced = await generator.generate_questionnaire_excel(
            "test_client", use_enhanced_styling=True
        )
        
        # Save the enhanced version
        with open(f"test_enhanced_{filename_enhanced}", "wb") as f:
            f.write(excel_data_enhanced)
        print(f"Enhanced Excel file saved as: test_enhanced_{filename_enhanced}")
        
        # Test with basic styling (openpyxl)
        print("Generating Excel with basic styling (openpyxl)...")
        excel_data_basic, filename_basic = await generator.generate_questionnaire_excel(
            "test_client", use_enhanced_styling=False
        )
        
        # Save the basic version
        with open(f"test_basic_{filename_basic}", "wb") as f:
            f.write(excel_data_basic)
        print(f"Basic Excel file saved as: test_basic_{filename_basic}")
        
        print("\nTest completed successfully!")
        print("Compare the two files to see the styling differences:")
        print(f"- Enhanced: test_enhanced_{filename_enhanced}")
        print(f"- Basic: test_basic_{filename_basic}")
        
    finally:
        # Restore the original function
        clients_module.get_client_data = original_get_client_data


if __name__ == "__main__":
    asyncio.run(test_enhanced_styling())
