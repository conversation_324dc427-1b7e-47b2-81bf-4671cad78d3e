apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: records-tls
  annotations:
    kubernetes.io/ingress.class: "nginx"
    certmanager.k8s.io/cluster-issuer: kibernetika-cert-issuer
    cert-manager.io/cluster-issuer: kibernetika-cert-issuer
    nginx.ingress.kubernetes.io/proxy-body-size: 256m
    {{ if .Values.api.redirectDomain }}
    nginx.ingress.kubernetes.io/rewrite-target: "https://{{ .Values.api.redirectDomain }}/$1"
    {{- end }}
spec:
  tls:
    - hosts:
      {{- range $i, $dns := .Values.api.dns }}
      - "{{ $dns }}"
      {{- end }}
      secretName: records-tls
  rules:
  {{- range $i, $dns := .Values.api.dns }}
  - host: {{ $dns }}
    http:
      paths:
        - path: /docs
          pathType: Prefix
          backend:
            service:
              name: records-api
              port:
                number: 8084
        - path: /openapi.json
          pathType: Prefix
          backend:
            service:
              name: records-api
              port:
                number: 8084
        - path: /api
          pathType: Prefix
          backend:
            service:
              name: records-api
              port:
                number: 8084
        - path: /
          pathType: Prefix
          backend:
            service:
              name: records-ui
              port:
                number: 80
  {{- end }}
