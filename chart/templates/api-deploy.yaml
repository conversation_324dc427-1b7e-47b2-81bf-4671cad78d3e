apiVersion: apps/v1
kind: Deployment
metadata:
  name: records-api
spec:
  selector:
    matchLabels:
      app: records
      component: records-api
  replicas: {{ .Values.api.replicas }}
  template:
    metadata:
      labels:
        app: records
        component: records-api
    spec:
      containers:
        - name:  records-api
          image: "{{ printf "%s:%s" .Values.api.Image.Name .Values.api.Image.Tag}}"
          readinessProbe:
            httpGet:
              path: /health
              port: 8084
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: PGHOST
            {{- if .Values.api.db.enabled }}
              value: records-db
            {{- else }}
              value: {{ .Values.api.db.host }}
            {{- end }}
            - name: PGUSER
              value: {{ .Values.api.db.user }}
            - name: PGPASSWORD
              value: {{ .Values.api.db.password }}
            - name: PGDATABASE
              value: {{ .Values.api.db.dbname }}
            - name: BASE_URL
              value: {{ .Values.api.baseUrl }}
            - name: FILE_API_URL
              value: http://file-api:8084
            - name: ENV_PREFIX
              value: {{ .Values.api.envPrefix }}
            - name: CREDENTIALS
              value: "/go/credentials"
            - name: REDIS_HOST
              value: "redis"
            {{- range .Values.api.envVars }}
            - name: {{ .name }}
              value: '{{ .value }}'
            {{- end }}
            - name: BLOWFISH_SALT
              valueFrom:
                secretKeyRef:
                  name: crypto
                  key: blowfish-salt
          ports:
            - containerPort: 8084
              name: http
          volumeMounts:
            - mountPath: /go/credentials
              name: credentials
            #- mountPath: /data
            #  name: data
      terminationGracePeriodSeconds: 90
      volumes:
        #- name: data
        #  persistentVolumeClaim:
        #    claimName: records-data
        - name: credentials
          secret:
            secretName: records-credentials
            items:
              {{- range $key, $value := .Values.credentials }}
              - key: {{ $key }}.json
                path: {{ $key }}.json
              {{- end }}
