{{- if .Values.wildcard.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ws-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/issuer: records-ws-issuer
    nginx.ingress.kubernetes.io/proxy-body-size: 256m
spec:
  tls:
  - hosts:
    - '{{ .Values.wildcard.dns }}'
    secretName: wildcard-ira-app-tls
  rules:
  - host: '{{ .Values.wildcard.dns }}'
    http:
      paths:
        - path: /docs
          pathType: Prefix
          backend:
            service:
              name: records-api
              port:
                number: 8084
        - path: /openapi.json
          pathType: Prefix
          backend:
            service:
              name: records-api
              port:
                number: 8084
        - path: /api
          pathType: Prefix
          backend:
            service:
              name: records-api
              port:
                number: 8084
        - path: /
          pathType: Prefix
          backend:
            service:
              name: records-ui
              port:
                number: 80
{{- end }}