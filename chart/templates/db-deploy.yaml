{{- if .Values.api.db.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: records-db
spec:
  selector:
    matchLabels:
      app: records
      component: records-db
  template:
    metadata:
      labels:
        app: records
        component: records-db
    spec:
      containers:
        - name:  records-db
          image: postgres:17.4
          env:
            - name: POSTGRES_USER
              value: {{ .Values.api.db.user }}
            - name: PGUSER
              value: {{ .Values.api.db.user }}
            - name: POSTGRES_PASSWORD
              value: {{ .Values.api.db.password }}
            - name: POSTGRES_DB
              value: {{ .Values.api.db.dbname }}
            - name: PGDATA
              value: /var/lib/postgresql/data/pgdata
          ports:
            - containerPort: 5432
              name: psql
          volumeMounts:
            - name: db
              mountPath: /var/lib/postgresql/data/pgdata
              subPath: records-db
      volumes:
        - name: db
          persistentVolumeClaim:
            claimName: records-db
{{- end }}