apiVersion: apps/v1
kind: Deployment
metadata:
  name: records-ui
spec:
  replicas: {{ .Values.ui.replicas }}
  selector:
    matchLabels:
      app: records
      component: records-ui
  template:
    metadata:
      labels:
        app: records
        component: records-ui
    spec:
      containers:
        - name: records-ui
          image: "{{ printf "%s:%s" .Values.ui.Image.Name .Values.ui.Image.Tag}}"
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          ports:
            - containerPort: 80
              name: http
