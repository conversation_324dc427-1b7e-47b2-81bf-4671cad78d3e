api:
  Image:
    Name: kuberlab/records-api
    Tag: latest
  baseUrl: https://records.kibernetika.io
  db:
    enabled: true
    host: records-db
    user: postgres
    password: Privet1961sneg
    dbname: records
  dns:
    - records.kibernetika.io
  redirectDomain: ''
  replicas: 2
  envPrefix: 'dev'
  envVars:
    - name: some
      value: "value"
wildcard:
  enabled: false
  dns: '*.records.kibernetika.io'
  serviceAccountJson: ''
  googleProject: ''
resources:
   limits:
     memory: 2Gi
   requests:
     memory: 256Mi

files:
  Image:
    Name: kuberlab/chat-file-api
    Tag: latest

crypto: jS2fKKFSlfco3mxLdfckd0ew
credentials:
  admins: W10K
  smtp: e30=
  google: e30=
persistence:
  db:
    size: 20G
    storageClass: premium-rwo
  files:
    size: 40G
    storageClass: standard
ui:
  Image:
    Name: kuberlab/records-ui
    Tag: latest
  replicas: 1

nodeSelector: {}
tolerations: []
