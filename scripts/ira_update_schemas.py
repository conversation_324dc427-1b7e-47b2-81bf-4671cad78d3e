#!/usr/bin/env python3
import argparse
import asyncio
import os

from tqdm import tqdm

from records.db import models
from records.ira_chat_client.api_client import APIClient
from records.services import schemas
from records.services import detections as detections_svc
from records.utils import utils


def parse_args():
    parser = argparse.ArgumentParser(description="Update IRA extractor and timeline schemas to global_client_schema.")
    parser.add_argument('-k', '--api_key', required=True, help='API key/token for IRA')
    parser.add_argument('-u', '--api_url', required=True, help='Base API URL for IRA')
    parser.add_argument('-ws', '--workspace-id', type=int, required=True, help='Workspace ID')
    parser.add_argument('--verbose', action='store_true')
    parser.add_argument('--extractors', action='store_true', help='Update extractors')
    parser.add_argument('--timelines', action='store_true', help='Update timelines')
    parser.add_argument('--detections', action='store_true', help='Update detections')
    return parser.parse_args()


async def main():
    utils.setup_logging()
    args = parse_args()

    if os.getenv('DEBUG_IRA') == 'true':
        args.verbose = True

    client = APIClient(args.api_key, args.api_url, verbose=args.verbose)
    client.set_workspace(ws_id=args.workspace_id)

    arg_set = args.extractors or args.timelines or args.detections

    if arg_set and args.detections or not arg_set:
        # Update all detections
        detections = client.detection_list()
        print(f"Found {len(detections)} detections.")
        for detection in tqdm(detections, desc="Updating detections"):
            detection_id = detection['id']
            detection_title = detection['title']
            if detection_title == models.DetectionName.CLIENT_DATA:
                detection_data = {'config': detections_svc.get_client_upload_detection_config()}
            elif detection_title == models.DetectionName.SMART_MERGE:
                detection_data = {'config': detections_svc.get_client_smart_merge_detection_config()}
            elif detection_title == models.DetectionName.METADATA_EXTRACTION:
                detection_data = {'config': detections_svc.get_metadata_extraction_detection_config()}
            elif detection_title == models.DetectionName.TAX_REPORT_DETECTION:
                detection_data = {'config': detections_svc.get_tax_report_detection_config()}
            else:
                detection_data = {'config': detections_svc.get_client_upload_detection_config()}
            await client.adetection_update(detection_id, detection_data)
            if args.verbose:
                print(f"Updated detection {detection_id} - {detection.get('title')}")

    if arg_set and args.extractors or not arg_set:
        # Update all extractors

        extractors = client.extractor_list(limit=10000)
        print(f"Found {len(extractors)} extractors.")
        for extractor in tqdm(extractors, desc="Updating extractors"):
            extractor_id = extractor['id']
            config = extractor.get('config', {})
            config['prompts'] = {'system_prompt': detections_svc.EXTRACTOR_PROMPT}
            extractor_data = {
                'data_schema': schemas.global_client_schema,
                'config': config
            }
            await client.aextractor_update(extractor_id, extractor_data)
            if args.verbose:
                print(f"Updated extractor {extractor_id} - {extractor.get('title')}")

    if arg_set and args.timelines or not arg_set:
        # Update all timelines
        timelines = client.timeline_list(limit=10000)
        print(f"Found {len(timelines)} timelines.")
        for timeline in tqdm(timelines, desc="Updating timelines"):
            timeline_id = timeline['id']
            config = timeline.get('config', {})
            config['prompts'] = {'system_prompt': detections_svc.EXTRACTOR_PROMPT}
            timeline_data = {
                'data_schema': schemas.global_client_schema,
                'config': config
            }
            await client.atimeline_update(timeline_id, timeline_data)
            if args.verbose:
                print(f"Updated timeline {timeline_id} - {timeline.get('title')}")


if __name__ == '__main__':
    asyncio.run(main())
