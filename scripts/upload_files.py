import argparse
import asyncio
import glob
import os

import numpy as np
import tqdm

from records.records_client import api_client
from records.utils import utils


def parse_args():
    parser = argparse.ArgumentParser(description='Upload files to the records service')
    parser.add_argument('--client-id', type=str, help='Client ID')
    parser.add_argument('-u', '--api-url', default='https://dev-records.kibernetika.io', type=str, help='API URL of the records service')
    parser.add_argument('-k', '--api-key', type=str, help='API key for the records service')
    parser.add_argument('-d', '--dir', type=str, help='Dir to the file to upload')
    parser.add_argument('-dc', '--dir-as-client', action='store_true', help='Use the dir as the client name')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
    parser.add_argument('-w', '--workers', type=int, default=8, help='Number of workers for concurrent upload')
    parser.add_argument('--limit', type=int, help='Limit the number of files to upload')
    parser.add_argument('--offset', type=int, default=0, help='Offset for the files to upload')
    return parser.parse_args()


async def upload_file(client: api_client.APIClient, file_path: str, client_id: str = None, doc_type: str = None):
    with open(file_path, 'rb') as f:
        file_data = f.read()

    # Assuming the API client has a method to upload files
    file_name = os.path.basename(file_path)
    attempts = 7
    for attempt in range(attempts):
        response = await client.afile_upload(client_id, file_name, file_data, doc_type, raw=True)
        if response.status_code == 409:
            print(f'File already exists: {file_path}')
            return

        if response.status_code == 200:
            response = response.json()
            break
        if response.status_code == 500:
            if '502 Bad Gateway' in response.text:
                print(f'Bad gateway, retrying {file_path}')
                await asyncio.sleep(1)
                continue
        api_client.raise_for_status(response)
        response = response.json()

    # Wait status
    status = (response.get('status') or {}).get('status', 'PROCESSING')
    file_id = response['id']
    while status not in ['SUCCESS', 'ERROR']:
        await asyncio.sleep(1)
        file_obj = await client.afile_get(client_id, file_id)
        status = (file_obj.get('status') or {}).get('status', 'PROCESSING')


async def upload_batch(client: api_client.APIClient, files: list, pbar, client_ids: list[str] = None, doc_types: list[str] = None):
    # Upload a batch of files
    if client_ids is None:
        raise ValueError('Client ID is required')

    for file_path, client_id, doc_type in zip(files, client_ids, doc_types):
        await upload_file(client, file_path, client_id, doc_type)
        pbar.update(1)


async def upload_files(client: api_client.APIClient, args):
    root_dir = args.dir
    dir_as_client = args.dir_as_client
    workers = args.workers
    client_id = args.client_id

    # Current structure: <client_name>/<doc_type>/<file>
    if dir_as_client:
        all_files = glob.glob(os.path.join(root_dir, '*', '*', '*'), recursive=True)
        client_id = None
    else:
        # Upload for a specific client
        if client_id is None:
            raise ValueError('Client ID is required when not using --dir-as-client')
        all_files = glob.glob(os.path.join(root_dir, '*'), recursive=True)

    all_files = sorted([f.removeprefix(root_dir + '/') for f in all_files])
    all_files = all_files[args.offset:args.offset + args.limit] if args.limit else all_files[args.offset:]
    pbar = tqdm.tqdm(total=len(all_files), desc='Uploading files', unit='file')
    if dir_as_client:
        client_name_map = {}
        clients = await client.aclient_list(limit=10000)
        for client_dict in clients['items']:
            client_name_map[client_dict['name']] = client_dict['id']

        # Build arguments
        client_names = [f.split('/')[0] for f in all_files]
        client_ids = [client_name_map[name] for name in client_names]
        doc_types = [f.split('/')[1] for f in all_files]
        full_names = [os.path.join(root_dir, f) for f in all_files]

        # Group files by client_id
        client_groups = {}
        for full_name, client_id, doc_type in zip(full_names, client_ids, doc_types):
            if client_id not in client_groups:
                client_groups[client_id] = []
            client_groups[client_id].append((full_name, client_id, doc_type))

        # chunks = np.array_split(list(zip(full_names, client_ids, doc_types)), workers)
        # Initialize empty chunks
        chunks = [[] for _ in range(workers)]

        # Sort client groups by size (largest first) to optimize distribution
        sorted_groups = sorted(client_groups.items(), key=lambda x: len(x[1]), reverse=True)

        # Distribute groups to chunks using greedy approach
        for _, group in sorted_groups:
            # Find chunk with fewest items
            chunk_idx = min(range(workers), key=lambda i: len(chunks[i]))
            chunks[chunk_idx].extend(group)
    else:
        # All files belong to the same client
        doc_types = [None for _ in all_files]
        full_names = [os.path.join(root_dir, f) for f in all_files]
        client_ids = [client_id for _ in all_files]
        chunks = np.array_split(list(zip(full_names, client_ids, doc_types)), workers)

    tasks = []
    for chunk in chunks:
        if not chunk:
            continue
        files, client_ids, doc_types = zip(*chunk)
        tasks.append(upload_batch(client, files, pbar, client_ids=client_ids, doc_types=doc_types))

    await asyncio.gather(*tasks)


async def main():
    args = parse_args()
    utils.setup_logging()
    client = api_client.APIClient(args.api_key, args.api_url, verbose=args.verbose)

    await upload_files(client, args)


if __name__ == '__main__':
    asyncio.run(main())
