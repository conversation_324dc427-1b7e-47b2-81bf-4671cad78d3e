import os
from typing import Optional

import pydantic
import sqlalchemy as sa
import tqdm
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

from records.db import base, models
from records.utils import utils


class NormalizedAddress(pydantic.BaseModel):
    street: Optional[str] = None
    pobox: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip: Optional[str] = None
    country: Optional[str] = None
    full_address: Optional[str] = None


def normalize_address(address: str) -> NormalizedAddress:
    llm = ChatOpenAI(model_name='gpt-4o-mini').with_structured_output(NormalizedAddress, method="json_schema")

    prompt_tpl = (
        "You are an expert address parser. Parse and normalize the given address into structured components.\n\n"

        "FIELD EXTRACTION RULES:\n"
        "- street: Extract street number, street name, and street type (St, Ave, Blvd, etc.). Include apartment/suite numbers.\n"
        "- pobox: Extract if address contains 'PO Box', 'P.O. Box', 'Post Office Box', or similar. Set street to null if PO Box is present.\n"
        "- city: Extract city name. Capitalize properly (e.g., 'New York', not 'NEW YORK').\n"
        "- state: Extract state. Use 2-letter abbreviations for US states (CA, NY, TX, etc.). Full names for other countries.\n"
        "- zip: Extract postal/ZIP code. For US: 5 digits or 5+4 format (12345 or 12345-6789). Keep original format for other countries.\n"
        "- country: Determine country. Default to 'USA' if US state/ZIP detected. Use full country names (e.g., 'Canada', 'United Kingdom').\n\n"

        "FORMATTING STANDARDS:\n"
        "- Capitalize first letter of each word in street names and cities\n"
        "- Use standard abbreviations: Street→St, Avenue→Ave, Boulevard→Blvd, Road→Rd, Drive→Dr\n"
        "- Remove extra spaces and normalize punctuation\n"
        "- For US addresses: Use 2-letter state codes (CA, NY, TX, FL, etc.)\n\n"

        "INFERENCE RULES:\n"
        "- If ZIP code is 5 digits and no country specified, assume USA\n"
        "- If state looks like US state name/abbreviation, assume USA\n"
        "- If address has Canadian postal code format (A1A 1A1), set country to Canada\n"
        "- If unclear, leave field as null rather than guessing\n\n"

        "EXAMPLES:\n"
        "Input: '123 main street, anytown ca 90210'\n"
        "Output: street='123 Main St', city='Anytown', state='CA', zip='90210', country='USA'\n\n"

        "Input: 'PO Box 456, Springfield, IL 62701'\n"
        "Output: pobox='PO Box 456', city='Springfield', state='IL', zip='62701', country='USA'\n\n"

        "Input: '789 Oak Avenue Apt 2B, Toronto ON M5V 3A8 Canada'\n"
        "Output: street='789 Oak Ave Apt 2B', city='Toronto', state='ON', zip='M5V 3A8', country='Canada'\n\n"

        "FULL ADDRESS COMPILATION:\n"
        "Compile full_address using this format:\n"
        "- If PO Box: '[pobox], [city], [state] [zip], [country]'\n"
        "- If street: '[street], [city], [state] [zip], [country]'\n"
        "- Always include full_address field\n\n"

        "Address to parse: {address}"
    )
    chain = PromptTemplate.from_template(prompt_tpl) | llm

    normalized_address = chain.invoke({'address': address})
    country = normalized_address.country
    if not country:
        normalized_address.country = 'USA' if normalized_address.zip and normalized_address.zip.isdigit() else None
    if not normalized_address.full_address:
        normalized_address.full_address = (
            f"{normalized_address.street}, {normalized_address.city}, "
            f"{normalized_address.state} {normalized_address.zip}, {normalized_address.country}"
        )
    return normalized_address


def migrate_reg_agent_addresses(session: Session) -> None:
    all_reg_agents = session.execute(sa.text("SELECT id, address FROM reg_agents")).fetchall()
    found = 0
    not_found = []
    missing_addr_map = {}
    for reg_agent in all_reg_agents:
        id, address = reg_agent
        if not address:
            found += 1
            continue
        address = address.strip()
        # Find address
        address_db = session.execute(
            sa.text("SELECT id FROM addresses WHERE full_address = :address"), params={'address': address}).fetchone()
        if address_db:
            session.execute(
                sa.text("UPDATE reg_agents SET address_id = :address_id WHERE id = :id"),
                params={'address_id': address_db[0], 'id': id}
            )
            found += 1
        else:
            print(f'Not found address: {address}')
            not_found.append(address)
            missing_addr_map[id] = address

    print(f"Found {found} / {len(all_reg_agents)} addresses")
    print(f"Missing unique addresses: {len(set(not_found))}")

    for id, address in tqdm.tqdm(missing_addr_map.items(), total=len(missing_addr_map)):
        normalized_address = normalize_address(address)
        new_address = models.Address(
            id=utils.generate_unicode_uuid(),
            full_address=normalized_address.full_address,
            street=normalized_address.street,
            pobox=normalized_address.pobox,
            city=normalized_address.city,
            state=normalized_address.state,
            zip=normalized_address.zip,
            country=normalized_address.country
        )
        session.add(new_address)
        session.flush()
        session.execute(
            sa.text("UPDATE reg_agents SET address_id = :address_id WHERE id = :id"),
            params={'address_id': new_address.id, 'id': id}
        )


def main():
    engine = create_engine(
        f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@{os.getenv("PGHOST", "localhost")}:5432/{os.getenv("PGDATABASE")}',
        echo=os.getenv("DEBUG_DB", "false") == "true",
    )
    session = sessionmaker(bind=engine)()
    base.ModelBase.metadata.create_all(engine)

    try:
        migrate_reg_agent_addresses(session)
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    # Close the session
    session.close()


if __name__ == '__main__':
    main()
