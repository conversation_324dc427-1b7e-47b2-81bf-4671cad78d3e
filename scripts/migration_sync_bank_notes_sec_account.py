import collections
import json
import json
import os
import uuid
from datetime import datetime

from sqlalchemy import (
    create_engine, update
)
from sqlalchemy.orm import sessionmaker

from records.db import base, models, api as db_api


# Helper function to normalize the data
def normalize_json(obj):
    if isinstance(obj, dict):
        if '$oid' in obj:
            return obj['$oid']
        elif '$numberInt' in obj:
            return int(obj['$numberInt'])
        elif '$numberLong' in obj:
            return int(obj['$numberLong'])
        elif '$numberDouble' in obj:
            return float(obj['$numberDouble'])
        elif '$date' in obj:
            # Handle ISO date strings
            date_str = obj['$date']
            for fmt in ('%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'):
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return date_str  # Return as is if it doesn't match any format
        else:
            return {k: normalize_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [normalize_json(item) for item in obj]
    else:
        return obj


def compile_full_note():
    pass


# Helper function to normalize the data
def migrate_bank_account_notes(session, client_data):
    client_id = client_data['uid']
    notes = client_data['none_banks']

    # Main account info
    main_opened = client_data['date_opened_1']
    main_renewal = client_data['last_renewal_1']
    main_contact = client_data['bank_contact_1']
    main_aba = client_data['aba_number_1']
    main_bank = client_data['bank_name_1']
    main_signer = client_data['authorized_signer_1_1']
    main_control_by = client_data['control_by_1']

    # Secondary account info
    sec_account = client_data['sec_account_main_2']
    sec_opened = client_data['sec_main_date_opened_2']
    sec_signer = client_data['sec_authorized_signer_1']
    sec_bank = client_data['bank_name_2']
    sec_aba = client_data['aba_number_2']
    sec_bank_contact = client_data['bank_contact_2']
    sec_control_by = client_data['sec_controlled_by_2']
    processed = False

    if isinstance(main_aba, dict):
        main_aba = main_aba.get('title')
    if isinstance(sec_aba, dict):
        sec_aba = sec_aba.get('title')

    existing_main = session.query(models.BankAccount).filter_by(client_id=client_id, aba_number=main_aba).first()
    if existing_main:
        # Update the existing main account with the new notes
        update_q = update(models.BankAccount).where(models.BankAccount.id == existing_main.id)
        update_values = {'notes': notes, 'controlled_by': main_control_by}

        if main_signer:
            # find or create signer
            signer = session.query(models.AuthorizedSigner).filter_by(signer_name=main_signer).first()
            if not signer:
                signer = models.AuthorizedSigner(
                    id=str(uuid.uuid4()),
                    signer_name=main_signer
                )
                session.add(signer)
            update_values['authorized_signer_id'] = signer.id

        update_q = update_q.values(update_values)
        session.execute(update_q)
        processed = True

    if sec_account:
        existing_sec = session.query(models.BankAccount).filter_by(client_id=client_id, aba_number=sec_aba).first()
        if existing_sec:
            # Update the existing secondary account with the new notes
            update_q = update(models.BankAccount).where(models.BankAccount.id == existing_sec.id)
            update_values = {'notes': notes, 'controlled_by': sec_control_by, 'account_number': sec_account, 'date_opened': sec_opened}

            if sec_signer:
                # find or create signer
                signer = session.query(models.AuthorizedSigner).filter_by(signer_name=sec_signer).first()
                if not signer:
                    signer = models.AuthorizedSigner(
                        id=str(uuid.uuid4()),
                        signer_name=sec_signer
                    )
                    session.add(signer)
                update_values['authorized_signer_id'] = signer.id

            update_q = update_q.values(update_values)
            session.execute(update_q)

            processed = True

    return processed


def process_clients(session, filename):
    with open(filename, 'r') as f:
        updated = 0
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            client_data = data.copy()
            if migrate_bank_account_notes(session, client_data):
                updated += 1

        print(f"Processed {filename}, updated {updated} records")


def main():
    engine = create_engine(
        f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@{os.getenv("PGHOST", "localhost")}:5432/{os.getenv("PGDATABASE")}',
        echo=os.getenv("DEBUG_DB", "false") == "true",
    )
    session = sessionmaker(bind=engine)()
    base.ModelBase.metadata.create_all(engine)

    try:
        process_clients(session, 'clients.jsonl')
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    # Close the session
    session.close()


if __name__ == '__main__':
    main()
