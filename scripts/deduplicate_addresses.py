import collections
import json
import json
import os
import uuid
from datetime import datetime

from sqlalchemy import (
    create_engine, update
)
from sqlalchemy.orm import sessionmaker

from records.db import base, models, api as db_api


def update_client_address(client_address: models.ClientAddress, new_values: dict, session):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientAddress).where(models.ClientAddress.id == client_address.id)
    update_q = update_q.values(new_values)

    session.execute(update_q)

    client_address.update(new_values)

    return client_address


# Helper function to normalize the data
def migrate_addresses(session):
    addresses = session.query(models.Address).all()
    client_addresses = session.query(models.ClientAddress).all()
    any_client_with_address_id = {ca.address_id for ca in client_addresses if ca.address_id}
    address_map_unique = {a.full_address: a for a in addresses}
    address_map_by_id = {a.id: a for a in addresses}
    deleted = 0

    for client_address in client_addresses:
        if not client_address.address_id:
            continue

        address = address_map_by_id[client_address.address_id]
        unique_address = address_map_unique.get(address.full_address)
        if unique_address and unique_address.id != address.id:
            session.delete(address)
            update_client_address(client_address, {'address_id': unique_address.id}, session=session)

            deleted += 1

    address_map_by_address = collections.defaultdict(list)
    for address in addresses:
        address_map_by_address[address.full_address].append(address)
    for address in addresses:
        if address.id not in any_client_with_address_id and len(address_map_by_address[address.full_address]) > 1:
            session.delete(address)
            deleted += 1
            address_map_by_address[address.full_address].remove(address)
        # if address.full_address not in address_map_unique:
        #     session.delete(address)
        #     db_api.update_client_address(client_address, {'address_id': address.id}, session=session)
        #
        #     deleted += 1

    print(f"Deleted {deleted} addresses")


def main():
    engine = create_engine(
        f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@localhost:5432/{os.getenv("PGDATABASE")}',
        echo=os.getenv("DEBUG_DB", "false") == "true",
    )
    session = sessionmaker(bind=engine)()
    base.ModelBase.metadata.create_all(engine)

    try:
        migrate_addresses(session)
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    # Close the session
    session.close()


if __name__ == '__main__':
    main()
