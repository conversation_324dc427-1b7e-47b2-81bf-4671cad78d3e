import json
import os
import uuid
from datetime import datetime

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from records.db import models


# Helper function to normalize the data
def normalize_json(obj):
    if isinstance(obj, dict):
        if '$oid' in obj:
            return obj['$oid']
        elif '$numberInt' in obj:
            return int(obj['$numberInt'])
        elif '$numberLong' in obj:
            return int(obj['$numberLong'])
        elif '$numberDouble' in obj:
            return float(obj['$numberDouble'])
        elif '$date' in obj:
            # Handle ISO date strings
            date_str = obj['$date']
            for fmt in ('%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'):
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return date_str  # Return as is if it doesn't match any format
        else:
            return {k: normalize_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [normalize_json(item) for item in obj]
    else:
        return obj


def build_missing_reg_sql(session, client_data):
    client_id = client_data['uid']
    missing_reg_agent2 = bool(client_data['reg_agent_2']) is False
    if not missing_reg_agent2:
        return None

    any_reg_data2 = any([
        client_data['reg_state_2'],
        client_data['reg_date_2'],
        client_data['regpayby_2'],
        client_data['last_soi_filed_2'],
        client_data['state_entity_2'],
    ])
    if not any_reg_data2:
        return None
    reg_agent2 = client_data['reg_agent_2']
    reg_agent_uid = None
    title = ""
    nickname = None
    address = None
    if isinstance(reg_agent2, dict):
        reg_agent_uid = reg_agent2['uid']
        title = reg_agent2.get('title')
        nickname = reg_agent2.get('nickname')
        address = reg_agent2.get('address')
    if isinstance(reg_agent2, str):
        title = reg_agent2

    if not reg_agent_uid:
        reg_agent_uid = str(uuid.uuid4())

    existing_reg_agent = session.query(models.RegAgent).filter_by(title=title).first()
    if existing_reg_agent:
        reg_agent = existing_reg_agent
        reg_agent_uid = reg_agent.uid
    else:
        reg_agent = models.RegAgent(
            uid=reg_agent_uid,
            title=title,
            nickname=nickname,
            address=address,
        )
        session.add(reg_agent)
        sql_insert = (
            f"INSERT INTO reg_agents (uid, title, nickname, address) VALUES "
            f"('{reg_agent_uid}', '{title}', '{nickname}', '{address}');")
        print(sql_insert)

    registration2 = models.ClientRegistration(
        client_id=client_id,
        reg_agent_uid=reg_agent_uid,
        reg_date=client_data['reg_date_2'],
        reg_state=client_data['reg_state_2'],
        reg_pay_by=client_data['regpayby_2'],
        last_soi_filed=client_data['last_soi_filed_2'],
        state_entity=client_data['state_entity_2'],
    )
    session.add(registration2)
    sql_insert_registration2 = (
        f"INSERT INTO client_registrations (client_id, reg_agent_uid, reg_date, reg_state, reg_pay_by, last_soi_filed, state_entity) VALUES "
        f"('{client_id}', '{reg_agent_uid}', '{client_data['reg_date_2']}', '{client_data['reg_state_2']}', '{client_data['regpayby_2']}', '{client_data['last_soi_filed_2']}', '{client_data['state_entity_2']}');"
    )
    print(sql_insert_registration2)


def process_clients(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            client_data = data.copy()
            build_missing_reg_sql(session, client_data)

        print(f"Processed {filename}")


def main():
    engine = create_engine(
        f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@{os.getenv("PGHOST", "localhost")}:5432/{os.getenv("PGDATABASE")}',
        echo=os.getenv("DEBUG_DB", "false") == "true",
    )
    session = sessionmaker(bind=engine)()

    process_clients(session, 'clients.jsonl')
    session.commit()


if __name__ == '__main__':
    main()
