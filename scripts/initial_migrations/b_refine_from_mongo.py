import os
import uuid

from sqlalchemy import (
    Column, String, Integer, DateTime, ForeignKey, Text, JSON
)
from sqlalchemy import create_engine
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker

import a_migrate_from_mongo
from a_migrate_from_mongo import Base
from scripts.initial_migrations.a_migrate_from_mongo import RegAgent


class BankAccount(Base):
    __tablename__ = 'bank_accounts'

    id = Column(String(36), primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    client_person_id = Column(String, ForeignKey('client_persons.uid'))
    bank_name = Column(String)
    aba_number = Column(JSON)
    bank_contact = Column(String)
    date_opened = Column(DateTime(timezone=True))
    last_renewal = Column(DateTime(timezone=True))
    notes = Column(Text)

    # client = relationship('Client', back_populates='bank_accounts')


class AuthorizedSigner(Base):
    __tablename__ = 'authorized_signers'

    id = Column(String(36), primary_key=True)
    signer_name = Column(String)
    note = Column(Text)


class ClientAuthorizedSigner(Base):
    __tablename__ = 'client_authorized_signers'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    signer_id = Column(String, ForeignKey('authorized_signers.id'))

    # client = relationship('Client', back_populates='authorized_signers')
    # signer = relationship('AuthorizedSigner')


class ClientRegistration(Base):
    __tablename__ = 'client_registrations'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    reg_agent_uid = Column(String, ForeignKey('reg_agents.uid'))
    reg_date = Column(DateTime(timezone=True))
    reg_state = Column(String)
    reg_pay_by = Column(String)
    last_soi_filed = Column(DateTime(timezone=True))
    state_entity = Column(String)
    notes = Column(Text)

    # client = relationship('Client', back_populates='registrations')
    # reg_agent = relationship('RegAgent')


class ClientShareClass(Base):
    __tablename__ = 'client_share_classes'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    stock_authorized = Column(Integer)
    stock_issued = Column(Integer)
    shares_authorized_preferred = Column(String)
    shares_issued_preferred = Column(Integer)
    notes = Column(Text)

    # client = relationship('Client', back_populates='share_classes')


def migrate_bank_accounts(session):
    clients = session.query(migrate_from_mongo.Client).all()
    for client in clients:
        # Migrate first bank account
        if client.aba_number_1:
            if isinstance(client.aba_number_1, str):
                aba_number = str(client.aba_number_1).strip('"')
                bank_name = client.bank_name_1
            elif isinstance(client.aba_number_1, dict):
                bank_name = client.aba_number_1.get('bank')
                aba_number = client.aba_number_1.get('title')
            else:
                aba_number = None
                bank_name = client.bank_name_1

            bank_account1 = BankAccount(
                id=str(uuid.uuid4()),
                client_id=client.id,
                bank_name=bank_name,
                aba_number=aba_number,
                bank_contact=client.bank_contact_1,
                date_opened=client.date_opened_1,
                last_renewal=client.last_renewal_1,
                notes=None  # Add any additional notes if available
            )
            session.add(bank_account1)

        # Migrate second bank account
        if client.aba_number_2:
            if isinstance(client.aba_number_2, str):
                aba_number = str(client.aba_number_2).strip('"')
                bank_name = client.bank_name_2
            elif isinstance(client.aba_number_2, dict):
                bank_name = client.aba_number_2.get('bank')
                aba_number = client.aba_number_2.get('title')
            else:
                aba_number = None
                bank_name = client.bank_name_2
            bank_account2 = BankAccount(
                id=str(uuid.uuid4()),
                client_id=client.id,
                bank_name=bank_name,
                aba_number=aba_number,
                bank_contact=client.bank_contact_2,
                date_opened=client.date_opened_2,
                last_renewal=client.last_renewal_2,
                notes=client.sec_account_note
            )
            session.add(bank_account2)


def migrate_authorized_signers(session):
    clients = session.query(migrate_from_mongo.Client).all()
    for client in clients:
        # Migrate first authorized signer
        if client.authorized_signer_1_1:
            signer1 = AuthorizedSigner(
                id=str(uuid.uuid4()),
                signer_name=client.authorized_signer_1_1,
            )
            session.add(signer1)
            client_signer1 = ClientAuthorizedSigner(
                client_id=client.id,
                signer_id=signer1.id
            )
            session.add(client_signer1)

        # Migrate second authorized signer
        if client.authorized_signer_1_2:
            signer2 = AuthorizedSigner(
                id=str(uuid.uuid4()),
                signer_name=client.authorized_signer_1_2
            )
            session.add(signer2)
            client_signer2 = ClientAuthorizedSigner(
                client_id=client.id,
                signer_id=signer2.id
            )
            session.add(client_signer2)

        if client.authorized_signer_2_1:
            signer3 = AuthorizedSigner(
                id=str(uuid.uuid4()),
                signer_name=client.authorized_signer_2_1
            )
            session.add(signer3)
            client_signer3 = ClientAuthorizedSigner(
                client_id=client.id,
                signer_id=signer3.id
            )
            session.add(client_signer3)

        if client.authorized_signer_2_2:
            signer4 = AuthorizedSigner(
                id=str(uuid.uuid4()),
                signer_name=client.authorized_signer_2_2
            )
            session.add(signer4)
            client_signer4 = ClientAuthorizedSigner(
                client_id=client.id,
                signer_id=signer4.id
            )
            session.add(client_signer4)


def migrate_reg_agents(session):
    clients = session.query(migrate_from_mongo.Client).all()
    for client in clients:
        # Migrate first registration
        if client.reg_agent_1:
            if isinstance(client.reg_agent_1, str):
                reg_agent_title = str(client.reg_agent_1).strip('"')
                # find by title
                reg_agent = session.query(RegAgent).filter(RegAgent.title == reg_agent_title).first()
                reg_agent_uid = reg_agent.uid if reg_agent else None
            elif isinstance(client.reg_agent_1, dict):
                reg_agent_title = client.reg_agent_1.get('title')
                reg_agent_uid = client.reg_agent_1.get('uid')
                reg_agent = session.query(RegAgent).filter(RegAgent.title == reg_agent_title).first()
                if not reg_agent:
                    reg_agent = RegAgent(
                        uid=reg_agent_uid,
                        title=reg_agent_title,
                        nickname=client.reg_agent_1.get('nickname'),
                        address=client.reg_agent_1.get('address'),
                    )
                    session.add(reg_agent)
            else:
                reg_agent_uid = None

            registration1 = ClientRegistration(
                client_id=client.id,
                reg_agent_uid=reg_agent_uid,
                reg_date=client.reg_date_1,
                reg_state=client.reg_state_1,
                reg_pay_by=client.regpayby_1,
                last_soi_filed=client.last_soi_filed_1,
                state_entity=client.state_entity_1
            )
            session.add(registration1)

        # Migrate second registration
        if client.reg_agent_2:
            if isinstance(client.reg_agent_2, str):
                reg_agent_title = str(client.reg_agent_2).strip('"')
                # find by title
                reg_agent = session.query(RegAgent).filter(RegAgent.title == reg_agent_title).first()
                reg_agent_uid = reg_agent.uid if reg_agent else None
            elif isinstance(client.reg_agent_2, dict):
                reg_agent_title = client.reg_agent_2.get('title')
                reg_agent_uid = client.reg_agent_2.get('uid')
                reg_agent = session.query(RegAgent).filter(RegAgent.title == reg_agent_title).first()
                if not reg_agent:
                    reg_agent = RegAgent(
                        uid=reg_agent_uid,
                        title=reg_agent_title,
                        nickname=client.reg_agent_2.get('nickname'),
                        address=client.reg_agent_2.get('address'),
                    )
                    session.add(reg_agent)
            else:
                reg_agent_uid = None
            registration2 = ClientRegistration(
                client_id=client.id,
                reg_agent_uid=reg_agent_uid,
                reg_date=client.reg_date_2,
                reg_state=client.reg_state_2,
                reg_pay_by=client.regpayby_2,
                last_soi_filed=client.last_soi_filed_2,
                state_entity=client.state_entity_2
            )
            session.add(registration2)


def migrate_share_classes(session):
    clients = session.query(migrate_from_mongo.Client).all()
    for client in clients:
        # Migrate first share class
        if client.stock_authorized_1 or client.stock_issued_1:
            share_class1 = ClientShareClass(
                client_id=client.id,
                stock_authorized=client.stock_authorized_1,
                stock_issued=client.stock_issued_1,
                shares_authorized_preferred=client.shares_authorized_preferred_1,
                shares_issued_preferred=client.shares_issued_preferred_1
            )
            session.add(share_class1)

        # Migrate second share class
        if client.stock_authorized_2 or client.stock_issued_2:
            share_class2 = ClientShareClass(
                client_id=client.id,
                stock_authorized=client.stock_authorized_2,
                stock_issued=client.stock_issued_2,
                # Assuming there are corresponding fields for the second share class
                # Update with correct field names if they exist
            )
            session.add(share_class2)


def drop_old_columns(session):
    # List all columns to be dropped
    columns_to_drop = [
        'aba_number_1', 'aba_number_2',
        'authorized_signer_1_1', 'authorized_signer_1_2',
        'authorized_signer_2_1', 'authorized_signer_2_2',
        'bank_contact_1', 'bank_contact_2',
        'bank_name_1', 'bank_name_2',
        'date_opened_1', 'date_opened_2',
        'last_renewal_1', 'last_renewal_2',
        'last_soi_filed_1', 'last_soi_filed_2',
        'reg_agent_1', 'reg_agent_2',
        'reg_date_1', 'reg_date_2',
        'reg_state_1', 'reg_state_2',
        'regpayby_1', 'regpayby_2',
        'state_entity_1', 'state_entity_2',
        'stock_authorized_1', 'stock_authorized_2',
        'stock_issued_1', 'stock_issued_2',
        'shares_authorized_preferred_1', 'shares_issued_preferred_1',
        'sec_account_main_2', 'sec_main_date_opened_2',
        'sec_authorized_signer_1', 'sec_controlled_by_2',
        'sec_account_note'
    ]

    for column_name in columns_to_drop:
        drop_column = f"ALTER TABLE clients DROP COLUMN IF EXISTS {column_name};"
        raw = text(drop_column)
        session.execute(raw)
        # conn.execute(drop_column)
        print(f"Dropped column: {column_name}")


def main():
    engine = create_engine(f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@localhost:5432/{os.getenv("PGDATABASE")}')
    session = sessionmaker(bind=engine)()
    try:
        migrate_from_mongo.Client.metadata.create_all(engine)
        migrate_from_mongo.Person.metadata.create_all(engine)
        ClientShareClass.metadata.create_all(engine)

        migrate_bank_accounts(session)
        migrate_reg_agents(session)
        migrate_share_classes(session)
        # Drop old columns if desired
        drop_old_columns(session)
        session.commit()
    except Exception as e:
        session.rollback()
        raise e


if __name__ == "__main__":
    main()
