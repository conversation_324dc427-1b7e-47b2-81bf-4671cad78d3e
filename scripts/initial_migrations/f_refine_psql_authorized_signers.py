import json
import json
import os
import uuid
from datetime import datetime

from sqlalchemy import (
    create_engine,
)
from sqlalchemy.orm import sessionmaker

from records.db import base, models


# Helper function to normalize the data
def normalize_json(obj):
    if isinstance(obj, dict):
        if '$oid' in obj:
            return obj['$oid']
        elif '$numberInt' in obj:
            return int(obj['$numberInt'])
        elif '$numberLong' in obj:
            return int(obj['$numberLong'])
        elif '$numberDouble' in obj:
            return float(obj['$numberDouble'])
        elif '$date' in obj:
            # Handle ISO date strings
            date_str = obj['$date']
            for fmt in ('%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'):
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return date_str  # Return as is if it doesn't match any format
        else:
            return {k: normalize_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [normalize_json(item) for item in obj]
    else:
        return obj


def migrate_authorized_signers(session, client_data):
    client_id = client_data['uid']
    client = session.query(models.Client).filter_by(id=client_id).first()
    fields = [
        'authorized_signer_1_1',
        'authorized_signer_2_1',
        'authorized_signer_3_1',
        'authorized_signer_1_2',
        'authorized_signer_2_2',
        'authorized_signer_3_2'
    ]
    for field in fields:
        if client_data[field]:
            existing_signer = session.query(models.AuthorizedSigner).filter_by(signer_name=client_data[field]).first()
            if existing_signer:
                signer_id = existing_signer.id
            else:
                signer = models.AuthorizedSigner(
                    id=str(uuid.uuid4()),
                    signer_name=client_data[field]
                )
                session.add(signer)
                signer_id = signer.id

            client_signer = models.ClientAuthorizedSigner(
                client_id=client.id,
                signer_id=signer_id
            )
            session.add(client_signer)


def process_clients(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            client_data = data.copy()
            migrate_authorized_signers(session, client_data)

        print(f"Processed {filename}")


# Main function to create tables and process all files

def main():
    engine = create_engine(
        f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@localhost:5432/{os.getenv("PGDATABASE")}',
        echo=os.getenv("DEBUG_DB", "false") == "true",
    )
    session = sessionmaker(bind=engine)()
    base.ModelBase.metadata.create_all(engine)

    try:
        process_clients(session, 'clients.jsonl')
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    print('Create all tables')
    # Close the session
    session.close()


if __name__ == '__main__':
    main()
