import json
import os
import uuid
from datetime import datetime

import sqlalchemy
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from records.db import models


# Helper function to normalize the data
def normalize_json(obj):
    if isinstance(obj, dict):
        if '$oid' in obj:
            return obj['$oid']
        elif '$numberInt' in obj:
            return int(obj['$numberInt'])
        elif '$numberLong' in obj:
            return int(obj['$numberLong'])
        elif '$numberDouble' in obj:
            return float(obj['$numberDouble'])
        elif '$date' in obj:
            # Handle ISO date strings
            date_str = obj['$date']
            for fmt in ('%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'):
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return date_str  # Return as is if it doesn't match any format
        else:
            return {k: normalize_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [normalize_json(item) for item in obj]
    else:
        return obj


def update_deregister_date(session, client_data):
    client_id = client_data['uid']
    dissolution_date = client_data['dissolution_date']
    withdrawal_date = client_data['withdrawal_date']
    last_renewal_1 = client_data['last_renewal_1']
    last_renewal_2 = client_data['last_renewal_2']

    registrations = session.query(models.ClientRegistration).filter_by(client_id=client_id).all()
    primary_reg = None
    secondary_reg = None
    for reg in registrations:
        if reg.is_primary:
            primary_reg = reg
        else:
            secondary_reg = reg

    updated = False
    if (dissolution_date is not None or last_renewal_1 is not None) and primary_reg:
        # Update client registration
        query = sqlalchemy.update(models.ClientRegistration).where(models.ClientRegistration.id == primary_reg.id).values(
            {'deregister_date': dissolution_date, 'last_renewal_date': last_renewal_1 if last_renewal_1 else None}
        )
        session.execute(query)
        updated = True

    if (withdrawal_date is not None or last_renewal_2 is not None) and secondary_reg:
        # Update client registration
        query = sqlalchemy.update(models.ClientRegistration).where(models.ClientRegistration.id == secondary_reg.id).values(
            {'deregister_date': withdrawal_date, 'last_renewal_date': last_renewal_2 if last_renewal_2 else None}
        )
        session.execute(query)
        updated = True

    return updated


def process_clients(session, filename):
    with open(filename, 'r') as f:
        updated = 0
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            client_data = data.copy()
            if update_deregister_date(session, client_data):
                updated += 1

        print(f"Processed {filename}, updated {updated} records")


def main():
    engine = create_engine(
        f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@{os.getenv("PGHOST", "localhost")}:5432/{os.getenv("PGDATABASE")}',
        echo=os.getenv("DEBUG_DB", "false") == "true",
    )
    session = sessionmaker(bind=engine)()

    process_clients(session, 'clients.jsonl')
    session.commit()


if __name__ == '__main__':
    main()
