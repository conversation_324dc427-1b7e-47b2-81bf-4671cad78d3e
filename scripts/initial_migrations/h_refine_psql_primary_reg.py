import json
import os
import uuid
from datetime import datetime

import sqlalchemy
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from records.db import models


# Helper function to normalize the data
def normalize_json(obj):
    if isinstance(obj, dict):
        if '$oid' in obj:
            return obj['$oid']
        elif '$numberInt' in obj:
            return int(obj['$numberInt'])
        elif '$numberLong' in obj:
            return int(obj['$numberLong'])
        elif '$numberDouble' in obj:
            return float(obj['$numberDouble'])
        elif '$date' in obj:
            # Handle ISO date strings
            date_str = obj['$date']
            for fmt in ('%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'):
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return date_str  # Return as is if it doesn't match any format
        else:
            return {k: normalize_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [normalize_json(item) for item in obj]
    else:
        return obj


def update_primary_reg(session, client_data):
    client_id = client_data['uid']
    reg_agent_1 = client_data['reg_agent_1']
    if not reg_agent_1:
        return None

    if isinstance(reg_agent_1, dict):
        reg_agent_uid = reg_agent_1['uid']
    else:
        title = reg_agent_1
        reg_agent = session.query(models.RegAgent).filter_by(title=title).first()
        reg_agent_uid = reg_agent.id if reg_agent else None

    registrations = session.query(models.ClientRegistration).filter_by(client_id=client_id).all()
    primary_reg = None
    for reg in registrations:
        if reg.reg_agent_id == reg_agent_uid:
            primary_reg = reg
            break

    if primary_reg:
        # Set the primary registration to the first registration
        # Update client registration
        query = sqlalchemy.update(models.ClientRegistration).where(models.ClientRegistration.id == primary_reg.id).values(
            {'is_primary': True}
        )
        session.execute(query)


def process_clients(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            client_data = data.copy()
            update_primary_reg(session, client_data)

        print(f"Processed {filename}")


def main():
    engine = create_engine(
        f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@{os.getenv("PGHOST", "localhost")}:5432/{os.getenv("PGDATABASE")}',
        echo=os.getenv("DEBUG_DB", "false") == "true",
    )
    session = sessionmaker(bind=engine)()

    process_clients(session, 'clients.jsonl')
    session.commit()


if __name__ == '__main__':
    main()
