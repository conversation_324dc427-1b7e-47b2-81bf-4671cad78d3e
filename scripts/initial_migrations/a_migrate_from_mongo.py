import inspect
import json
import uuid

from sqlalchemy import (
    create_engine,
    Column,
    Integer,
    String,
    DateTime,
    Boolean,
    ForeignKey,
    Float,
    Text,
    Table,
)
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.dialects.postgresql import JSON
from datetime import datetime

# Update this with your actual database URI
DATABASE_URI = 'postgresql://postgres:passwd@localhost:5432/crm'

Base = declarative_base()


# Helper function to normalize the data
def normalize_json(obj):
    if isinstance(obj, dict):
        if '$oid' in obj:
            return obj['$oid']
        elif '$numberInt' in obj:
            return int(obj['$numberInt'])
        elif '$numberLong' in obj:
            return int(obj['$numberLong'])
        elif '$numberDouble' in obj:
            return float(obj['$numberDouble'])
        elif '$date' in obj:
            # Handle ISO date strings
            date_str = obj['$date']
            for fmt in ('%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'):
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return date_str  # Return as is if it doesn't match any format
        else:
            return {k: normalize_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [normalize_json(item) for item in obj]
    else:
        return obj


# Define SQLAlchemy models corresponding to each collection/table

class AbaNumber(Base):
    __tablename__ = 'aba_numbers'

    id = Column(Integer, primary_key=True)
    uid = Column(String)
    title = Column(String)
    bank = Column(String)


class Address(Base):
    __tablename__ = 'addresses'

    id = Column(Integer, primary_key=True)
    uid = Column(String)
    title = Column(String)
    street = Column(String)
    pobox = Column(String)
    city = Column(String)
    state = Column(String)
    zip = Column(String)
    country = Column(String)


class Catalog(Base):
    __tablename__ = 'catalogs'

    id = Column(Integer, primary_key=True)
    title = Column(String)
    options = Column(JSON)  # Storing options as a JSONB field


class ClientPerson(Base):
    __tablename__ = 'client_persons'

    id = Column(Integer, primary_key=True)
    uid = Column(String, unique=True)
    title = Column(String)
    firstname = Column(String)
    lastname = Column(String)
    legal_full_name = Column(String)
    email = Column(String)
    phone = Column(String)
    pcm = Column(String)
    citizenship = Column(String)
    address = Column(Text)  # This could be a ForeignKey to Address.id if needed
    companies = Column(String)


class Manager(Base):
    __tablename__ = 'managers'

    id = Column(Integer, primary_key=True)
    uid = Column(String)
    userid = Column(String)
    title = Column(String)
    email = Column(String)
    phone = Column(String)


class RegAgent(Base):
    __tablename__ = 'reg_agents'

    id = Column(Integer, primary_key=True)
    uid = Column(String, unique=True)
    title = Column(String)
    nickname = Column(String)
    address = Column(String)


# Define association table for Client and Service
client_services = Table(
    'client_services',
    Base.metadata,
    Column('client_id', String, ForeignKey('clients.id')),
    Column('service_uid', String, ForeignKey('services.uid')),
)


class Service(Base):
    __tablename__ = 'services'

    id = Column(Integer, primary_key=True)
    uid = Column(String, unique=True)
    title = Column(String)
    price = Column(Float)
    price_type = Column(String)

    clients = relationship(
        "Client", secondary=client_services, back_populates="services"
    )


class Source(Base):
    __tablename__ = 'sources'

    id = Column(String, primary_key=True)  # Assuming 'id' is a UUID string
    title = Column(String)


class File(Base):
    __tablename__ = 'files'

    id = Column(Integer, primary_key=True)
    date = Column(DateTime(timezone=True))
    uid = Column(String)
    title = Column(String)
    file_type = Column(String)
    doc_type = Column(String)
    client = Column(String)
    manager = Column(String)
    uploaded = Column(Boolean)
    url = Column(String)


# Define Client model
class Client(Base):
    __tablename__ = 'clients'

    account = Column(String)
    account_add = Column(String)
    accounting_method = Column(String)
    active_since = Column(DateTime(timezone=True))
    agr_signed = Column(DateTime(timezone=True))
    agreement_sum = Column(Float)
    billing_method = Column(String)
    bookkeeping = Column(Boolean)
    changed = Column(DateTime(timezone=True))
    client = Column(String)
    company_phone = Column(String)
    control_by = Column(String)
    cpa = Column(String)
    date = Column(DateTime(timezone=True))
    description = Column(Text)
    disable_secreg = Column(Boolean)
    dissolution_date = Column(DateTime(timezone=True))
    ein = Column(String)
    fedtaxforms = Column(String)
    foreign_address = Column(String)
    fye = Column(String)
    fye_for_subsidiary = Column(String)
    id = Column(String, primary_key=True)
    incorp_by = Column(String)
    legal_address = Column(JSON)
    legal_ent_type = Column(String)
    login = Column(String)
    mailing_address = Column(JSON)
    manager = Column(String)  # Could be FK to Manager.id
    monthly_bill = Column(Float)
    naicscode = Column(String)
    none_banks = Column(Boolean)
    notes_accounting = Column(Text)
    notes_address = Column(Text)
    notes_agreement = Column(Text)
    notes_contacts = Column(Text)
    notes_main = Column(Text)
    notes_registration = Column(Text)
    notes_shareholders = Column(Text)
    optional = Column(Integer)
    paid_by = Column(String)
    paid_by_mail = Column(String)
    password = Column(String)
    payroll = Column(Boolean)
    physical_address = Column(JSON)
    renewal_date = Column(DateTime(timezone=True))
    renewal_date_mail = Column(DateTime(timezone=True))
    since = Column(DateTime(timezone=True))
    source = Column(JSON)  # Could be FK to Source.id
    statetaxforms = Column(String)
    status = Column(String)
    subjurisd = Column(String)
    subsidiary_legal_entity_type = Column(String)
    subsidiary_to_consolidate = Column(String)
    tax_return_address = Column(JSON)
    total_shares = Column(Integer)
    withdrawal_date = Column(DateTime(timezone=True))

    # Relationships
    addresses = relationship('ClientAddress', back_populates='client', cascade="all, delete-orphan")
    tax_reporting = relationship('TaxReporting', back_populates='client', cascade="all, delete-orphan")
    debit_cards = relationship('DebitCard', back_populates='client', cascade="all, delete-orphan")
    payment_systems = relationship('PaymentSystem', back_populates='client', cascade="all, delete-orphan")
    contacts = relationship('ClientContact', back_populates='client', cascade="all, delete-orphan")
    shareholders = relationship('ClientShareholder', back_populates='client', cascade="all, delete-orphan")
    tasks = relationship('ClientTask', back_populates='client', cascade="all, delete-orphan")
    # client_files = relationship('ClientFile', back_populates='client_backref', cascade="all, delete-orphan")
    services = relationship("Service", secondary=client_services, back_populates="clients")


# Define models for nested arrays

class ClientAddress(Base):
    __tablename__ = 'client_addresses'

    id = Column(String, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    address_type = Column(String)
    address = Column(String)
    renewal_date = Column(DateTime(timezone=True))
    paid_by = Column(String)
    note = Column(Text)

    client = relationship('Client', back_populates='addresses')


class TaxReporting(Base):
    __tablename__ = 'tax_reporting'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    year = Column(String)  # 'year_' in data
    reporting_1099 = Column(String)
    tax_return_by = Column(String)
    note = Column(Text)
    files = Column(String)  # Could be JSONB if files is complex

    client = relationship('Client', back_populates='tax_reporting')


class ClientContact(Base):
    __tablename__ = 'client_contacts'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    client_person_uid = Column(String)
    position = Column(String)
    email = Column(String)
    phone = Column(String)
    pcm = Column(String)
    note = Column(Text)

    client = relationship('Client', back_populates='contacts')


class ClientShareholder(Base):
    __tablename__ = 'client_shareholders'

    id = Column(String(36), primary_key=True, default=str(uuid.uuid4()))
    client_id = Column(String, ForeignKey('clients.id'))
    client_person_uid = Column(String)
    # Fields for the shareholders
    position = Column(String)
    ownership = Column(String)
    note = Column(Text)

    client = relationship('Client', back_populates='shareholders')


class DebitCard(Base):
    __tablename__ = 'debit_cards'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    # Fields for debit cards
    debit_card = Column(String)
    last_4_digits = Column(String)
    expired_at = Column(DateTime(timezone=True))
    cid = Column(String)
    linked_to = Column(String)
    card_holder = Column(String)
    exp = Column(String)

    client = relationship('Client', back_populates='debit_cards')


class PaymentSystem(Base):
    __tablename__ = 'payment_systems'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    # Fields for payment systems
    payment_system = Column(String)
    date_opened = Column(DateTime(timezone=True))
    opened_by = Column(String)
    email_connected = Column(String)
    responsible_person = Column(String)
    login_pass = Column(String)
    note = Column(Text)

    client = relationship('Client', back_populates='payment_systems')


class ClientTask(Base):
    __tablename__ = 'client_tasks'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    # Fields for tasks
    date = Column(DateTime(timezone=True))
    task = Column(String)
    due_date = Column(DateTime(timezone=True))
    status = Column(String)
    manager = Column(String)

    client = relationship('Client', back_populates='tasks')


class ClientFile(Base):
    __tablename__ = 'client_files'

    id = Column(Integer, primary_key=True)
    client = Column(String)
    manager = Column(String, nullable=True)
    client_id = Column(String, ForeignKey('clients.id'))
    # Fields for client files
    file_name = Column(String)
    file_type = Column(String)
    file_uid = Column(String)
    doc_type = Column(String)
    date = Column(DateTime(timezone=True))
    operation_uid = Column(String)


# You may need to adjust the Client model based on your needs
# For now, we will skip the Client model due to its complexity

# Create a database engine and session
engine = create_engine(DATABASE_URI)
Session = sessionmaker(bind=engine)
session = Session()


# Define functions to process each JSONL file and insert data into the database

def process_aba_numbers(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            aba_number = AbaNumber(
                id=data.get('id'),
                uid=data.get('uid'),
                title=data.get('title'),
                bank=data.get('bank')
            )
            session.add(aba_number)
    session.commit()
    print(f"Processed {filename}")


def process_addresses(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            address = Address(
                # id=data.get('id'),
                uid=data.get('uid'),
                title=data.get('title'),
                street=data.get('street'),
                pobox=data.get('pobox'),
                city=data.get('city'),
                state=data.get('state'),
                zip=data.get('zip'),
                country=data.get('country')
            )
            session.add(address)
    session.commit()
    print(f"Processed {filename}")


def process_catalogs(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            catalog = Catalog(
                # id=data.get('id'),
                title=data.get('title'),
                options=data.get('options')
            )
            session.add(catalog)
    session.commit()
    print(f"Processed {filename}")


def process_client_persons(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            client_person = ClientPerson(
                # id=data.get('id'),
                uid=data.get('uid'),
                title=data.get('title'),
                firstname=data.get('firstname'),
                lastname=data.get('lastname'),
                legal_full_name=data.get('legal_full_name'),
                email=data.get('email'),
                phone=data.get('phone'),
                pcm=data.get('pcm'),
                citizenship=data.get('citizenship'),
                address=data.get('address'),
                companies=data.get('companies')
            )
            session.add(client_person)
            session.commit()
    print(f"Processed {filename}")


def process_managers(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            manager = Manager(
                # id=data.get('id'),
                uid=data.get('uid'),
                userid=data.get('userid'),
                title=data.get('title'),
                email=data.get('email'),
                phone=data.get('phone')
            )
            session.add(manager)
    session.commit()
    print(f"Processed {filename}")


def process_reg_agents(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            reg_agent = RegAgent(
                # id=data.get('id'),
                uid=data.get('uid'),
                title=data.get('title'),
                nickname=data.get('nickname'),
                address=data.get('address')
            )
            session.add(reg_agent)
    session.commit()
    print(f"Processed {filename}")


def process_services(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            service = Service(
                # id=data.get('id'),
                uid=data.get('uid'),
                title=data.get('title'),
                price=data.get('price'),
                price_type=data.get('price_type')
            )
            session.add(service)
    session.commit()
    print(f"Processed {filename}")


def process_sources(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            source = Source(
                id=data.get('id'),
                title=data.get('title')
            )
            session.add(source)
    session.commit()
    print(f"Processed {filename}")


def process_files(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            file_entry = File(
                id=data.get('id'),
                date=data.get('date'),
                uid=data.get('uid'),
                title=data.get('title'),
                file_type=data.get('file_type'),
                doc_type=data.get('doc_type'),
                is_deleted=data.get('is_deleted'),
                client=data.get('client'),
                manager=data.get('manager'),
                uploaded=data.get('uploaded'),
                url=data.get('url')
            )
            session.add(file_entry)
    session.commit()
    print(f"Processed {filename}")


def process_clients(session, filename):
    with open(filename, 'r') as f:
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            client_data = data.copy()
            addresses = client_data.pop('addresses', [])
            tax_reporting = client_data.pop('tax_reporting', [])
            debit_cards = client_data.pop('debit_cards', [])
            payment_systems = client_data.pop('payment_systems', [])
            contacts = client_data.pop('contacts', [])
            shareholders = client_data.pop('shareholders', [])
            tasks = client_data.pop('tasks', [])
            client_files = client_data.pop('files', [])
            services = client_data.pop('services', [])
            # Remove '_id'
            client_data.pop('_id', None)
            # Map 'pass' to 'password' if necessary
            if 'pass' in client_data:
                client_data['password'] = client_data.pop('pass')
            # Convert date strings to datetime objects
            for key, value in client_data.items():
                if isinstance(value, str):
                    for fmt in ('%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'):
                        try:
                            client_data[key] = datetime.strptime(value, fmt)
                            break
                        except ValueError:
                            continue

            # Cast payroll, bookkeeping, disable_secreg, none_banks to boolean
            client_data['bookkeeping'] = bool(client_data.get('bookkeeping'))
            client_data['payroll'] = bool(client_data.get('payroll'))
            client_data['disable_secreg'] = bool(client_data.get('disable_secreg'))
            client_data['none_banks'] = bool(client_data.get('none_banks'))
            client_data['id'] = client_data['uid']
            del client_data['uid']

            if client_data['renewal_date_mail'] == 'Invalid Date':
                client_data['renewal_date_mail'] = None
            if not client_data.get('reg_date_1'):
                client_data['reg_date_1'] = None
            if not client_data.get('reg_date_2'):
                client_data['reg_date_2'] = None

            # print(client_data)
            client = Client(**client_data)
            session.add(client)
            session.flush()  # To get the client.id

            # Process addresses
            for address_data in addresses:
                address_data.pop('operationid', None)
                address_data.pop('_id', None)
                address_data['client_id'] = client.id
                address_data['id'] = str(uuid.uuid4())
                client_address = ClientAddress(**address_data)
                session.add(client_address)

            # Process tax_reporting
            for tax_data in tax_reporting:
                tax_data.pop('operationid', None)
                tax_data.pop('_id', None)
                tax_data['client_id'] = client.id
                tax_data['year'] = tax_data.pop('year_', None)  # Fix key
                if 'id' in tax_data and isinstance(tax_data['id'], dict):
                    tax_data['id'] = tax_data['id'].get('$numberInt')
                tax_report = TaxReporting(**tax_data)
                session.add(tax_report)

            # Process contacts
            for contact_data in contacts:
                contact_data.pop('operationid', None)
                contact_data.pop('_id', None)
                contact_data['client_id'] = client.id
                if 'id' in contact_data:
                    del contact_data['id']
                if contact_data.get('client_person'):
                    if isinstance(contact_data['client_person'], dict):
                        # Find client person by title
                        person_title = contact_data['client_person'].get('title')
                        person = session.query(ClientPerson).filter_by(title=person_title).first()
                        person_uid = person.uid
                    else:
                        person = session.query(ClientPerson).filter_by(title=contact_data['client_person']).first()
                        if not person:
                            print(f"Person '{contact_data['client_person']}' not found, creating")
                            client_person = ClientPerson(
                                uid=str(uuid.uuid4()),
                                title=contact_data['client_person'],
                                firstname=contact_data['client_person'].split()[0],
                                lastname=contact_data['client_person'].split()[1],
                                legal_full_name=contact_data['client_person'],
                                email=contact_data['email'],
                                phone=contact_data['phone'],
                                pcm=contact_data['pcm']
                            )
                            session.add(client_person)
                            continue
                        person_uid = person.uid
                    contact_data['client_person_uid'] = person_uid
                    contact_data.pop('client_person', None)
                else:
                    print(f'Invalid ContactData: {contact_data}')
                    continue
                # print(contact_data)
                client_contact = ClientContact(**contact_data)
                session.add(client_contact)

            # Process debit_cards
            for card_data in debit_cards:
                card_data.pop('operationid', None)
                card_data.pop('_id', None)
                card_data['client_id'] = client.id
                if 'id' in card_data and isinstance(card_data['id'], dict):
                    card_data['id'] = card_data['id'].get('$numberInt')

                if 'expired' in card_data:
                    if card_data['expired'] is not None:
                        card_data['expired_at'] = datetime.strptime(card_data['expired'], '%Y-%m-%dT%H:%M:%S')
                    else:
                        card_data['expired_at'] = None
                    del card_data['expired']
                # print(card_data)
                debit_card = DebitCard(**card_data)
                session.add(debit_card)

            # Process payment_systems
            for payment_data in payment_systems:
                payment_data.pop('operationid', None)
                payment_data.pop('_id', None)
                payment_data['client_id'] = client.id
                if 'id' in payment_data and isinstance(payment_data['id'], dict):
                    payment_data['id'] = payment_data['id'].get('$numberInt')
                # print(payment_data)
                payment_system = PaymentSystem(**payment_data)
                session.add(payment_system)

            # Process shareholders
            for shareholder_data in shareholders:
                shareholder_data.pop('operationid', None)
                shareholder_data.pop('_id', None)
                shareholder_data['client_id'] = client.id
                if 'id' in shareholder_data:
                    shareholder_data['id'] = str(uuid.uuid4())
                if shareholder_data.get('client_person'):
                    if isinstance(shareholder_data['client_person'], dict):
                        # Find client person by title
                        person_title = shareholder_data['client_person'].get('title')
                        person = session.query(ClientPerson).filter_by(title=person_title).first()
                        person_uid = person.uid
                    else:
                        person = session.query(ClientPerson).filter_by(title=shareholder_data['client_person']).first()
                        person_uid = person.uid
                    shareholder_data['client_person_uid'] = person_uid
                    shareholder_data.pop('client_person', None)
                else:
                    print(f'Invalid ShareholderData: {shareholder_data}')
                    continue
                client_shareholder = ClientShareholder(**shareholder_data)
                session.add(client_shareholder)

            # Process tasks
            for task_data in tasks:
                task_data.pop('operationid', None)
                task_data.pop('_id', None)
                task_data['client_id'] = client.id
                if 'id' in task_data and isinstance(task_data['id'], dict):
                    task_data['id'] = task_data['id'].get('$numberInt')
                client_task = ClientTask(**task_data)
                session.add(client_task)

            # Process client_files
            for file_data in client_files:
                file_data.pop('operationid', None)
                file_data.pop('_id', None)
                file_data['client_id'] = client.id
                if 'id' in file_data and isinstance(file_data['id'], dict):
                    file_data['id'] = file_data['id'].get('$numberInt')
                client_file = ClientFile(**file_data)
                session.add(client_file)

            # Process services
            for service_data in services:
                service_title = service_data.get('service')
                if isinstance(service_title, dict):
                    service_title = service_title.get('title')
                service_obj = session.query(Service).filter_by(title=service_title).first()
                if service_obj:
                    client.services.append(service_obj)
                else:
                    if service_title is None:
                        print(f'Invalid ServiceData: {service_data}')
                        continue
                    raise ValueError(f"Service '{service_title}' not found")

            session.commit()
        print(f"Processed {filename}")


# Main function to create tables and process all files

def main():
    print('Create all tables')
    Base.metadata.create_all(engine)

    def is_empty(x):
        if x is None:
            return True
        if isinstance(x, str):
            return x.strip() == ''
        # if isinstance(x, datetime):
        #     return False
        return not bool(x)

    # models = list(Base.registry._class_registry.values())
    # for model in models:
    #     if not inspect.isclass(model):
    #         continue
    #     model = Client
        # items = session.query(model).all()
        # for col in model.__table__.columns:
        #     if all(is_empty(getattr(item, col.name)) for item in items):
        #         print(f"Column '{col.name}' is empty for all {model.__name__}.")

    print('Process each JSONL file')
    process_aba_numbers(session, 'aba_number.jsonl')
    process_addresses(session, 'address.jsonl')
    process_catalogs(session, 'catalogs.jsonl')
    process_client_persons(session, 'client_person.jsonl')
    process_managers(session, 'managers.jsonl')
    process_reg_agents(session, 'reg_agents.jsonl')
    process_services(session, 'service.jsonl')
    process_sources(session, 'source.jsonl')
    process_files(session, 'files.jsonl')
    process_clients(session, 'clients.jsonl')

    # Close the session
    session.close()


if __name__ == '__main__':
    main()
