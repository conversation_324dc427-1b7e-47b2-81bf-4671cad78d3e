import os
import uuid
from typing import Optional

import pydantic
import tqdm
from sqlalchemy import (
    create_engine,
    Column,
    Integer,
    String,
    DateTime,
    Boolean,
    ForeignKey,
    Float,
    Text,
    Table,
)
from sqlalchemy.dialects.postgresql import JSON
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.orm import declarative_base

from scripts.initial_migrations.c_refine_from_mongo2 import normalize_address

Base = declarative_base()


class Address(Base):
    __tablename__ = 'addresses'

    id = Column(Integer, primary_key=True)
    uid = Column(String)
    full_address = Column(String)
    street = Column(String)
    pobox = Column(String)
    city = Column(String)
    state = Column(String)
    zip = Column(String)
    country = Column(String)


class Catalog(Base):
    __tablename__ = 'catalogs'

    id = Column(Integer, primary_key=True)
    title = Column(String)
    options = Column(JSON)  # Storing options as a JSONB field


class ClientPerson(Base):
    __tablename__ = 'client_persons'

    id = Column(Integer, primary_key=True)
    uid = Column(String, unique=True)
    title = Column(String)
    firstname = Column(String)
    lastname = Column(String)
    legal_full_name = Column(String)
    email = Column(String)
    phone = Column(String)
    pcm = Column(String)
    citizenship = Column(String)
    address = Column(Text)  # This could be a ForeignKey to Address.id if needed
    companies = Column(String)


class Manager(Base):
    __tablename__ = 'managers'

    id = Column(Integer, primary_key=True)
    uid = Column(String)
    userid = Column(String)
    title = Column(String)
    email = Column(String)
    phone = Column(String)


class RegAgent(Base):
    __tablename__ = 'reg_agents'

    id = Column(Integer, primary_key=True)
    uid = Column(String, unique=True)
    title = Column(String)
    nickname = Column(String)
    address = Column(String)


# Define association table for Client and Service
client_services = Table(
    'client_services',
    Base.metadata,
    Column('client_id', String, ForeignKey('clients.id')),
    Column('service_uid', String, ForeignKey('services.uid')),
)


class Service(Base):
    __tablename__ = 'services'

    id = Column(Integer, primary_key=True)
    uid = Column(String, unique=True)
    title = Column(String)
    price = Column(Float)
    price_type = Column(String)

    clients = relationship(
        "Client", secondary=client_services, back_populates="services"
    )


class Source(Base):
    __tablename__ = 'sources'

    id = Column(String, primary_key=True)  # Assuming 'id' is a UUID string
    title = Column(String)


class File(Base):
    __tablename__ = 'files'

    id = Column(Integer, primary_key=True)
    date = Column(DateTime(timezone=True))
    uid = Column(String)
    name = Column(String)
    file_type = Column(String)
    doc_type = Column(String)
    client = Column(String)
    manager = Column(String)
    uploaded = Column(Boolean)
    url = Column(String)


# Define Client model
class Client(Base):
    __tablename__ = 'clients'

    account = Column(String)
    account_add = Column(String)
    accounting_method = Column(String)
    active_since = Column(DateTime(timezone=True))
    agr_signed = Column(DateTime(timezone=True))
    agreement_sum = Column(Float)
    billing_method = Column(String)
    bookkeeping = Column(Boolean)
    changed = Column(DateTime(timezone=True))
    client = Column(String)
    company_phone = Column(String)
    control_by = Column(String)
    cpa = Column(String)
    date = Column(DateTime(timezone=True))
    description = Column(Text)
    disable_secreg = Column(Boolean)
    dissolution_date = Column(DateTime(timezone=True))
    ein = Column(String)
    fedtaxforms = Column(String)
    foreign_address = Column(String)
    fye = Column(String)
    fye_for_subsidiary = Column(String)
    id = Column(String, primary_key=True)
    incorp_by = Column(String)
    legal_address = Column(JSON)
    legal_ent_type = Column(String)
    login = Column(String)
    mailing_address = Column(JSON)
    manager = Column(String)  # Could be FK to Manager.id
    monthly_bill = Column(Float)
    naicscode = Column(String)
    none_banks = Column(Boolean)
    notes_accounting = Column(Text)
    notes_address = Column(Text)
    notes_agreement = Column(Text)
    notes_contacts = Column(Text)
    notes_main = Column(Text)
    notes_shareholders = Column(Text)
    optional = Column(Integer)
    paid_by = Column(String)
    paid_by_mail = Column(String)
    password = Column(String)
    payroll = Column(Boolean)
    physical_address = Column(JSON)
    renewal_date = Column(DateTime(timezone=True))
    renewal_date_mail = Column(DateTime(timezone=True))
    since = Column(DateTime(timezone=True))
    source = Column(JSON)  # Could be FK to Source.id
    statetaxforms = Column(String)
    status = Column(String)
    subjurisd = Column(String)
    subsidiary_legal_entity_type = Column(String)
    subsidiary_to_consolidate = Column(String)
    tax_return_address = Column(JSON)
    total_shares = Column(Integer)
    withdrawal_date = Column(DateTime(timezone=True))

    # Relationships
    addresses = relationship('ClientAddress', back_populates='client', cascade="all, delete-orphan")
    tax_reporting = relationship('TaxReporting', back_populates='client', cascade="all, delete-orphan")
    debit_cards = relationship('DebitCard', back_populates='client', cascade="all, delete-orphan")
    payment_systems = relationship('PaymentSystem', back_populates='client', cascade="all, delete-orphan")
    contacts = relationship('ClientContact', back_populates='client', cascade="all, delete-orphan")
    shareholders = relationship('ClientShareholder', back_populates='client', cascade="all, delete-orphan")
    tasks = relationship('ClientTask', back_populates='client', cascade="all, delete-orphan")
    # client_files = relationship('ClientFile', back_populates='client_backref', cascade="all, delete-orphan")
    services = relationship("Service", secondary=client_services, back_populates="clients")


# Define models for nested arrays

class ClientAddress(Base):
    __tablename__ = 'client_addresses'

    id = Column(String, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    address_type = Column(String)
    address_id = Column(String(36))
    renewal_date = Column(DateTime(timezone=True))
    paid_by = Column(String)
    note = Column(Text)

    client = relationship('Client', back_populates='addresses')


class TaxReporting(Base):
    __tablename__ = 'tax_reporting'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    year = Column(String)  # 'year_' in data
    reporting_1099 = Column(String)
    tax_return_by = Column(String)
    note = Column(Text)
    files = Column(String)  # Could be JSONB if files is complex

    client = relationship('Client', back_populates='tax_reporting')


class ClientContact(Base):
    __tablename__ = 'client_contacts'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    client_person_uid = Column(String)
    position = Column(String)
    email = Column(String)
    phone = Column(String)
    pcm = Column(String)
    note = Column(Text)

    client = relationship('Client', back_populates='contacts')


class ClientShareholder(Base):
    __tablename__ = 'client_shareholders'

    id = Column(String(36), primary_key=True, default=str(uuid.uuid4()))
    client_id = Column(String, ForeignKey('clients.id'))
    client_person_uid = Column(String)
    # Fields for the shareholders
    position = Column(String)
    ownership = Column(String)
    note = Column(Text)

    client = relationship('Client', back_populates='shareholders')


class DebitCard(Base):
    __tablename__ = 'debit_cards'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    # Fields for debit cards
    debit_card = Column(String)
    last_4_digits = Column(String)
    expired_at = Column(DateTime(timezone=True))
    cid = Column(String)
    linked_to = Column(String)
    card_holder = Column(String)
    exp = Column(String)

    client = relationship('Client', back_populates='debit_cards')


class PaymentSystem(Base):
    __tablename__ = 'payment_systems'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    # Fields for payment systems
    payment_system = Column(String)
    date_opened = Column(DateTime(timezone=True))
    opened_by = Column(String)
    email_connected = Column(String)
    responsible_person = Column(String)
    login_pass = Column(String)
    note = Column(Text)

    client = relationship('Client', back_populates='payment_systems')


class ClientTask(Base):
    __tablename__ = 'client_tasks'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    # Fields for tasks
    date = Column(DateTime(timezone=True))
    task = Column(String)
    due_date = Column(DateTime(timezone=True))
    status = Column(String)
    manager = Column(String)

    client = relationship('Client', back_populates='tasks')


class BankAccount(Base):
    __tablename__ = 'bank_accounts'

    id = Column(String(36), primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    client_person_id = Column(String, ForeignKey('client_persons.uid'))
    bank_name = Column(String)
    aba_number = Column(JSON)
    bank_contact = Column(String)
    date_opened = Column(DateTime(timezone=True))
    last_renewal = Column(DateTime(timezone=True))
    notes = Column(Text)

    # client = relationship('Client', back_populates='bank_accounts')


class AuthorizedSigner(Base):
    __tablename__ = 'authorized_signers'

    id = Column(String(36), primary_key=True)
    signer_name = Column(String)
    note = Column(Text)


class ClientAuthorizedSigner(Base):
    __tablename__ = 'client_authorized_signers'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    signer_id = Column(String, ForeignKey('authorized_signers.id'))

    # client = relationship('Client', back_populates='authorized_signers')
    # signer = relationship('AuthorizedSigner')


class ClientRegistration(Base):
    __tablename__ = 'client_registrations'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    reg_agent_uid = Column(String, ForeignKey('reg_agents.uid'))
    reg_date = Column(DateTime(timezone=True))
    reg_state = Column(String)
    reg_pay_by = Column(String)
    last_soi_filed = Column(DateTime(timezone=True))
    state_entity = Column(String)
    notes = Column(Text)

    # client = relationship('Client', back_populates='registrations')
    # reg_agent = relationship('RegAgent')


class ClientShareClass(Base):
    __tablename__ = 'client_share_classes'

    id = Column(Integer, primary_key=True)
    client_id = Column(String, ForeignKey('clients.id'))
    stock_authorized = Column(Integer)
    stock_issued = Column(Integer)
    shares_authorized_preferred = Column(String)
    shares_issued_preferred = Column(Integer)
    notes = Column(Text)

    # client = relationship('Client', back_populates='share_classes')


class NormalizedAddress(pydantic.BaseModel):
    street: Optional[str] = None
    pobox: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip: Optional[str] = None
    country: Optional[str] = None
    full_address: Optional[str] = None


def process_client_addresses(session):
    # Get all addresses
    clients = session.query(Client).all()

    types = ['Legal address', 'Foreign address', 'Physical address', 'Mailing address', 'Tax return address']
    for client in tqdm.tqdm(clients):
        inline_addresses = [
            client.legal_address,
            client.foreign_address,
            client.physical_address,
            client.mailing_address,
            client.tax_return_address
        ]
        for address_type, address in zip(types, inline_addresses):
            address_id = None
            if not address or str(address).lower() == 'null':
                continue
            if isinstance(address, str):
                full_address = address.strip('"')
                existing_address = session.query(Address).filter_by(full_address=full_address).first()
                address_id = existing_address.uid if existing_address else None
                if not existing_address:
                    print(f'Normalizing address: {full_address}')
                    normalized_address = normalize_address(full_address)
                    print(f'Normalized address: {normalized_address}')
                    new_address = Address(
                        uid=str(uuid.uuid4()),
                        full_address=normalized_address.full_address,
                        street=normalized_address.street,
                        pobox=normalized_address.pobox,
                        city=normalized_address.city,
                        state=normalized_address.state,
                        zip=normalized_address.zip,
                        country=normalized_address.country
                    )
                    session.add(new_address)
                    address_id = new_address.uid
            elif isinstance(address, dict):
                full_address = address.get('title')
                existing_address = session.query(Address).filter_by(full_address=full_address).first()
                address_id = existing_address.uid if existing_address else None
                if not existing_address:
                    print(f'Normalizing address: {full_address}')
                    normalized_address = normalize_address(full_address)
                    print(f'Normalized address: {normalized_address}')
                    new_address = Address(
                        uid=address.get('uid') or str(uuid.uuid4()),
                        full_address=normalized_address.full_address,
                        street=normalized_address.street,
                        pobox=normalized_address.pobox,
                        city=normalized_address.city,
                        state=normalized_address.state,
                        zip=normalized_address.zip,
                        country=normalized_address.country
                    )
                    session.add(new_address)
                    address_id = new_address.uid

            # Add new client address
            if not address_id:
                continue
            new_client_address = ClientAddress(
                id=str(uuid.uuid4()),
                client_id=client.id,
                address_type=address_type,
                address_id=address_id,
            )
            session.add(new_client_address)


def main():
    engine = create_engine(
        f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@localhost:5432/{os.getenv("PGDATABASE")}',
        echo=os.getenv("DEBUG_DB", "false") == "true",
    )
    session = sessionmaker(bind=engine)()
    ClientShareClass.metadata.create_all(engine)

    try:
        process_client_addresses(session)
        session.commit()
    except Exception as e:
        session.rollback()
        raise e


if __name__ == "__main__":
    main()
