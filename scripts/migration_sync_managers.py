import json
import os

from sqlalchemy import (
    create_engine, update
)
from sqlalchemy.orm import sessionmaker

from records.api import auth_utils
from records.db import base, models


def migrate_client_managers(session):
    all_clients = session.query(models.Client).all()
    managers = session.query(models.Manager).all()
    manager_by_name = {m.title: m for m in managers}
    logpass_info = {}
    for client in all_clients:
        manager_name = client.manager
        if not manager_name:
            continue

        manager_name = manager_name.strip()

        manager = manager_by_name.get(manager_name)
        if not manager:
            # Create user manager
            login_name = manager_name.replace(' ', '.').lower()
            login = f'{login_name}@records.kibernetika.io'
            new_user = {
                'name': manager_name,
                'login': login,
                'password': auth_utils.generate_temp_password(),
                'confirmed': True,
            }
            user_db = models.User(**new_user)
            session.add(user_db)
            session.flush()
            manager = models.Manager(
                title=manager_name,
                user_id=user_db.id,
                email=login,
                phone=None,
                role_name='owner',
                permissions=1
            )
            session.add(manager)
            session.flush()
            manager_by_name[manager_name] = manager
            logpass_info[login] = new_user['password']

        update_q = update(models.Client).where(models.Client.id == client.id).values(manager_id=manager.id)
        session.execute(update_q)

    return logpass_info


def main():
    engine = create_engine(
        f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@{os.getenv("PGHOST", "localhost")}:5432/{os.getenv("PGDATABASE")}',
        echo=os.getenv("DEBUG_DB", "false") == "true",
    )
    session = sessionmaker(bind=engine)()
    base.ModelBase.metadata.create_all(engine)

    try:
        logpass_info = migrate_client_managers(session)
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    # Close the session
    session.close()

    # save logpass info
    print(logpass_info)
    with open('logpass_info.json', 'w') as f:
        f.write(json.dumps(logpass_info))


if __name__ == '__main__':
    main()
