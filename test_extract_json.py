d = """It appears you've pasted a template or requirements list for a client management or legal entity tracking system, rather than a document for metadata extraction. To proceed with extracting comprehensive metadata as per your earlier instructions, **please upload or paste the actual document you want analyzed**.

If you intended to have metadata extracted from the text you just posted, here's how it would look based on your JSON requirements and the content provided:

```
{
  "summary": "This document outlines a comprehensive client management template for legal entities, including sections for legal information, contacts, services, compliance, banking, accounting, files, and tasks. It is intended to organize and track all relevant data for a business client in a structured manner.",
  "type": "Template",
  "name": "Client Management and Legal Entity Information Template",
  "date": "",
  "keywords": [
    "client management",
    "legal entity",
    "compliance",
    "banking",
    "accounting",
    "contacts",
    "tasks",
    "services",
    "legal information",
    "template"
  ],
  "author": "",
}
```

**Notes:**
- `"date"` and `"author"` are blank because they are not provided in your text.
- If you provide an actual document (e.g., a tax return, certificate, form, etc.), I can extract more specific metadata using your guidelines.

**Next step:**  
Please upload or paste the specific document you want to analyze for metadata."""

from records.utils import json_utils

__import__('ipdb').set_trace()
print(json_utils.extract_json(d))
