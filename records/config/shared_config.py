import logging
import os

from records.ira_chat_client import api_client
from records.utils import singleton
from records.files.local_manager import LocalManager
from records.files.remote_manager import RemoteManager

logger = logging.getLogger(__name__)


class SharedConfig(object, metaclass=singleton.Singleton):
    def __init__(self):
        self.ira_client = api_client.APIClient(
            api_token=os.getenv('IRA_API_TOKEN'),
            base_url=os.getenv('IRA_API_URL'),
            verbose=os.getenv('DEBUG_IRA', 'false').lower() == 'true'
        )
        logger.info("Checking IRA API connection...")
        ws_id = os.getenv('IRA_API_WORKSPACE_ID')
        if not ws_id:
            raise ValueError('IRA_API_WORKSPACE_ID is not set')
        try:
            self.ira_client.set_workspace(ws_id=int(ws_id))
        except Exception as e:
            logger.warning(f'Can not connect to ira-chat-api: {e}')
        else:
            logger.info("Done checking IRA API connection.")

        if os.environ.get('FILE_API_URL'):
            logger.info('Initialize RemoteManager')
            file_manager = RemoteManager()
        else:
            logger.info('Initialize LocalManager')
            file_manager = LocalManager()

        self.file_manager = file_manager
