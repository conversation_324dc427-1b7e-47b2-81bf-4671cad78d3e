import json

from records.config.shared_config import SharedConfig
from records.db import api as db_api
from records.db import models
from records.utils import json_utils


async def list_pending_changes(client_data: dict):
    ira_client = SharedConfig().ira_client
    client_id = client_data['id']
    timeline = await db_api.get_timeline_by_type(client_id=client_id, type_=models.TimelineType.CLIENT_DATA)
    if not timeline:
        return []
    # timeline_ira = await ira_client.atimeline_get(timeline.external_id)
    
    point_list = await ira_client.apoint_list(timeline.external_id)

    # Filter out points that are already applied
    filtered_points = []
    for point in point_list:
        note = point.get('note')
        if not note:
            filtered_points.append(point)
            continue
        else:
            try:
                note = json.loads(note)
            except json.JSONDecodeError:
                filtered_points.append(point)
                continue
            if note.get('crm_status') == 'applied':
                continue

    for point in filtered_points:
        if point.get('status', {}).get('status') in ['UPDATED']:
            # Trigger processing and take new status
            point_processing = await ira_client.apoint_process(timeline.external_id, point['id'])
            point['status'] = point_processing['status']

        point['output'] = await json_utils.amerge_jsons([client_data, point['output']])
    return filtered_points


async def reprocess_pending_change(client_id, point_id: str):
    ira_client = SharedConfig().ira_client
    timeline = await db_api.get_timeline_by_type(client_id=client_id, type_=models.TimelineType.CLIENT_DATA)
    if not timeline:
        return
    point = await ira_client.apoint_process(timeline.external_id, point_id)
    return point


async def list_applied_changes(client_data: dict):
    ira_client = SharedConfig().ira_client
    client_id = client_data['id']
    timeline = await db_api.get_timeline_by_type(client_id=client_id, type_=models.TimelineType.CLIENT_DATA)
    if not timeline:
        return []
    timeline_ira = await ira_client.atimeline_get(timeline.external_id)

    point_list = await ira_client.apoint_list(timeline_ira['id'])

    # Filter points that are not applied
    filtered_points = []
    for point in point_list:
        note = point.get('note')
        if not note:
            continue
        else:
            try:
                note = json.loads(note)
            except json.JSONDecodeError:
                continue
            if note.get('crm_status') == 'applied':
                filtered_points.append(point)

    for point in filtered_points:
        point['output'] = await json_utils.amerge_jsons([client_data, point['output']])
    return filtered_points


async def get_pending_change_by_file(client_data: dict, file_db: models.File):
    ira_client = SharedConfig().ira_client
    client_id = client_data['id']
    timeline = await db_api.get_timeline_by_type(client_id=client_id, type_=models.TimelineType.CLIENT_DATA)
    if not timeline:
        return None
    
    external_id = await db_api.get_external_id_by(
        internal_type='file',
        internal_id=file_db.id,
        external_app_type='timeline',
        external_app_id=str(timeline.external_id),
        external_item_type='file',
        notfoundok=True
    )
    if not external_id:
        return None
    saved_point = external_id.additional_info['point']

    point = await ira_client.apoint_get(timeline.external_id, saved_point['id'])
    if not point:
        return None

    point['output'] = await json_utils.amerge_jsons([client_data, point['output']])
    return point


async def mark_as_applied(client_id: str, change_id: str):
    ira_client = SharedConfig().ira_client
    timeline = await db_api.get_timeline_by_type(client_id=client_id, type_=models.TimelineType.CLIENT_DATA)
    if not timeline:
        return

    point = await ira_client.apoint_get(timeline.external_id, change_id)
    if not point:
        return

    point_note = point.get('note')
    if not point_note:
        point_note = {}
    else:
        try:
            point_note = json.loads(point_note)
        except json.JSONDecodeError:
            point_note = {}

    point_note['crm_status'] = 'applied'
    point['note'] = json.dumps(point_note)

    await ira_client.apoint_update(timeline.external_id, point['id'], {'note': point['note']})
