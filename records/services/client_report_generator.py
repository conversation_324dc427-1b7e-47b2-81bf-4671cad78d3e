import datetime
import io
import logging
from typing import Dict, <PERSON>, Tuple, Optional, Any

import pandas as pd
import xlsxwriter
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side, Alignment

from records.services import clients

logger = logging.getLogger(__name__)


class ClientReportGenerator:
    """
    Service for generating client reports in various formats.
    Supports generating questionnaire-style reports similar to the reference Excel file.
    """
    
    def __init__(self):
        self.questionnaire_template = self._get_questionnaire_template()
    
    def _get_questionnaire_template(self) -> List[Dict[str, Any]]:
        """
        Define the questionnaire template structure based on the reference Excel file.
        Each item represents a row in the questionnaire with field mapping information.
        """
        return [
            # I. Corporation INFO
            {"section": "I. Corporation INFO", "field": "Company Name", "db_field": "name", "required": True},
            {"section": "I. Corporation INFO", "field": "Tax ID/EIN", "db_field": "ein", "required": True},
            {"section": "I. Corporation INFO", "field": "Legal Entity Type", "db_field": "legal_ent_type", "required": False},
            {"section": "I. Corporation INFO", "field": "Type of business /description", "db_field": "description", "required": False},
            {"section": "I. Corporation INFO", "field": "NAICS code (choose here https://www.naics.com/search/)", "db_field": "naicscode", "required": False},
            {"section": "I. Corporation INFO", "field": "Primary State of registration", "db_field": "primary_registration.state_of_incorporation", "required": False},
            {"section": "I. Corporation INFO", "field": "State Registration Number", "db_field": "primary_registration.state_entity", "required": False},
            {"section": "I. Corporation INFO", "field": "Date of registration (mm/dd/yyyy)", "db_field": "primary_registration.registered_date", "required": False, "transform": "datetime_to_date"},
            {"section": "", "field": "", "db_field": "", "required": False},  # Empty row
            {"section": "I. Corporation INFO", "field": "Is corporation registered in additional state? (YES or NO)", "db_field": "secondary_registrations", "required": False, "transform": "has_secondary_registration"},
            {"section": "I. Corporation INFO", "field": "Secondary State of Registration", "db_field": "secondary_registrations.0.state_of_incorporation", "required": False},
            {"section": "I. Corporation INFO", "field": "Secondary State Registration number", "db_field": "secondary_registrations.0.state_entity", "required": False},
            {"section": "I. Corporation INFO", "field": "Secondary state registration Date", "db_field": "secondary_registrations.0.registered_date", "required": False},
            {"section": "", "field": "", "db_field": "", "required": False},  # Empty row
            {"section": "I. Corporation INFO", "field": "Accounting method - CASH or ACCRUAL", "db_field": "accounting_method", "required": False},
            {"section": "I. Corporation INFO", "field": "FYE (Financial Year End)", "db_field": "financial_year_end", "required": False},
            {"section": "I. Corporation INFO", "field": "Mailing address in US (in the state of registration)", "db_field": "addresses", "required": False, "transform": "mailing_address"},
            {"section": "I. Corporation INFO", "field": "Notes (Address):", "db_field": "notes_address", "required": False},
            {"section": "I. Corporation INFO", "field": "Telephone in US:", "db_field": "company_phone", "required": False},
            {"section": "I. Corporation INFO", "field": "Notes (Main):", "db_field": "notes_main", "required": False},
            {"section": "", "field": "", "db_field": "", "required": False},  # Empty row
            {"section": "I. Corporation INFO", "field": "SUBSIDIARY / Дочернее предприятие", "db_field": "subsidiary_to_consolidate", "required": False},
            {"section": "I. Corporation INFO", "field": "Jurisdiction of subsidiary", "db_field": "subjurisd", "required": False},
            {"section": "I. Corporation INFO", "field": "Legal Ent.Type of Subsidiary", "db_field": "subsidiary_legal_entity_type", "required": False},
            {"section": "I. Corporation INFO", "field": "FYE for subsidiary:", "db_field": "financial_year_end_for_subsidiary", "required": False},
            {"section": "", "field": "", "db_field": "", "required": False},  # Empty row
            {"section": "I. Corporation INFO", "field": "Total NUMBER OF Authorized shares (from Certificate of Incorporation)", "db_field": "shares", "required": False, "transform": "total_authorized_shares"},
            {"section": "I. Corporation INFO", "field": "Total NUMBER OF ACTUAL ISSUED SHARES (excluding SAFE/Options)", "db_field": "shares", "required": False, "transform": "total_issued_shares"},
            {"section": "I. Corporation INFO", "field": "Total Assets as of last day of Tax Year?", "db_field": "tax_reports", "required": False, "transform": "total_assets"},
            {"section": "I. Corporation INFO", "field": "", "db_field": "llc_shareholders", "required": False, "transform": "shareholder_details"},
            {"section": "I. Corporation INFO", "field": "Did you file your Prior year tax returns?", "db_field": "tax_reports", "required": False, "transform": "filed_prior_returns"},
            {"section": "I. Corporation INFO", "field": "Have WE prepared your Prior year tax returns?", "db_field": "tax_reports", "required": False, "transform": "we_prepared_returns"},
            {"section": "I. Corporation INFO", "field": "Did company file 1099 forms to US based contractors?", "db_field": "fedtaxforms", "required": False, "transform": "filed_1099"},
            {"section": "I. Corporation INFO", "field": "If \"Yes\", How many?", "db_field": "", "required": False},
            {"section": "I. Corporation INFO", "field": "Did company have employees on payroll this year?", "db_field": "payroll", "required": False, "transform": "yes_no"},
            {"section": "I. Corporation INFO", "field": "If \"Yes\", How many?", "db_field": "", "required": False},
            {"section": "I. Corporation INFO", "field": "Notes (Accounting):", "db_field": "notes_accounting", "required": False},
        ]
    
    async def generate_questionnaire_excel(self, client_id: str) -> Tuple[bytes, str]:
        """
        Generate a questionnaire-style Excel report for the specified client.

        Args:
            client_id: The ID of the client to generate the report for

        Returns:
            Tuple of (report_data_bytes, filename)
        """
        return await self._generate_excel_with_xlsxwriter(client_id)

    async def _generate_excel_with_xlsxwriter(self, client_id: str) -> Tuple[bytes, str]:
        """Generate Excel with enhanced styling using xlsxwriter."""
        _, client_data = await clients.get_client_data(client_id)

        # Create Excel workbook with xlsxwriter
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        worksheet = workbook.add_worksheet('Info')

        # Define enhanced styles
        styles = self._create_xlsxwriter_styles(workbook)

        # Generate questionnaire data
        questionnaire_data = await self._generate_questionnaire_data(client_data)

        # Set column widths
        worksheet.set_column('A:A', 65)
        worksheet.set_column('B:B', 85)

        # Write data with enhanced styling
        for row_idx, (question, answer) in enumerate(questionnaire_data):
            # Determine row style
            is_section_header = question and (question.startswith('I.') or question.startswith('II.') or
                                            question.startswith('III.') or question.startswith('IV.') or
                                            question.startswith('V.') or question.startswith('VI.') or
                                            question.startswith('VII.'))
            is_empty_row = not question and not answer
            is_alternating = row_idx % 2 == 1

            if is_section_header:
                # Section headers
                worksheet.write(row_idx, 0, question, styles['section_header'])
                worksheet.write(row_idx, 1, answer, styles['section_header'])
                worksheet.set_row(row_idx, 25)  # Taller rows for headers
            elif is_empty_row:
                # Empty rows for spacing
                worksheet.write(row_idx, 0, '', styles['empty'])
                worksheet.write(row_idx, 1, '', styles['empty'])
                worksheet.set_row(row_idx, 15)  # Shorter rows for spacing
            else:
                # Regular data rows with alternating colors
                question_style = styles['question_alt'] if is_alternating else styles['question']
                answer_style = styles['answer_alt'] if is_alternating else styles['answer']

                worksheet.write(row_idx, 0, question, question_style)
                worksheet.write(row_idx, 1, answer, answer_style)
                worksheet.set_row(row_idx, 22)  # Standard row height

        workbook.close()
        output.seek(0)

        # Generate filename
        client_name = client_data.get('name', 'Unknown')
        date = datetime.datetime.now().strftime('%Y-%m-%d')
        filename = f"Questionnaire_CORP_{date}_{client_name}.xlsx"

        return output.getvalue(), filename

    def _create_xlsxwriter_styles(self, workbook):
        """Create enhanced styles for xlsxwriter."""
        return {
            'section_header': workbook.add_format({
                'bold': True,
                'font_size': 14,
                'font_color': '#FFFFFF',
                'bg_color': '#2E5984',  # Professional blue
                'border': 1,
                'border_color': '#1F3A5F',
                'align': 'left',
                'valign': 'vcenter',
                'text_wrap': True
            }),
            'question': workbook.add_format({
                'bold': True,
                'font_size': 11,
                'font_color': '#2C3E50',
                'bg_color': '#FFFFFF',
                'border': 1,
                'border_color': '#D5DBDB',
                'align': 'left',
                'valign': 'top',
                'text_wrap': True
            }),
            'question_alt': workbook.add_format({
                'bold': True,
                'font_size': 11,
                'font_color': '#2C3E50',
                'bg_color': '#F8F9FA',  # Light gray for alternating rows
                'border': 1,
                'border_color': '#D5DBDB',
                'align': 'left',
                'valign': 'top',
                'text_wrap': True
            }),
            'answer': workbook.add_format({
                'font_size': 11,
                'font_color': '#34495E',
                'bg_color': '#FFFFFF',
                'border': 1,
                'border_color': '#D5DBDB',
                'align': 'left',
                'valign': 'top',
                'text_wrap': True
            }),
            'answer_alt': workbook.add_format({
                'font_size': 11,
                'font_color': '#34495E',
                'bg_color': '#F8F9FA',  # Light gray for alternating rows
                'border': 1,
                'border_color': '#D5DBDB',
                'align': 'left',
                'valign': 'top',
                'text_wrap': True
            }),
            'empty': workbook.add_format({
                'bg_color': '#FFFFFF',
                'border': 0
            })
        }

    def _create_openpyxl_styles(self):
        """Create enhanced styles for openpyxl."""
        # Define colors
        blue_bg = PatternFill(start_color="2E5984", end_color="2E5984", fill_type="solid")
        alternating_bg = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")

        # Define borders
        thin_border = Border(
            left=Side(style='thin', color='D5DBDB'),
            right=Side(style='thin', color='D5DBDB'),
            top=Side(style='thin', color='D5DBDB'),
            bottom=Side(style='thin', color='D5DBDB')
        )

        # Define fonts
        section_font = Font(bold=True, size=14, color='FFFFFF')
        question_font = Font(bold=True, size=11, color='2C3E50')
        answer_font = Font(size=11, color='34495E')

        # Define alignments
        center_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
        top_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)

        return {
            'section_header': {
                'font': section_font,
                'fill': blue_bg,
                'border': thin_border,
                'alignment': center_alignment
            },
            'question_font': question_font,
            'answer_font': answer_font,
            'alternating_fill': alternating_bg,
            'border': thin_border,
            'question_alignment': top_alignment,
            'answer_alignment': top_alignment
        }

    async def generate_questionnaire_csv(self, client_id: str) -> Tuple[bytes, str]:
        """
        Generate a questionnaire-style CSV report for the specified client.
        
        Args:
            client_id: The ID of the client to generate the report for
            
        Returns:
            Tuple of (report_data_bytes, filename)
        """
        # Get comprehensive client data using existing service
        _, client_data = await clients.get_client_data(client_id)

        # Generate questionnaire data
        questionnaire_data = await self._generate_questionnaire_data(client_data)

        # Create DataFrame
        df = pd.DataFrame(questionnaire_data, columns=['Question', 'Answer'])

        # Save to CSV
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8')

        # Convert to bytes
        csv_bytes = output.getvalue().encode('utf-8')

        # Generate filename
        client_name = client_data.get('name', 'Unknown').replace(' ', '_')
        date = datetime.datetime.now().strftime('%Y-%m-%d')
        filename = f"Questionnaire_CORP_{date}_{client_name}.csv"

        return csv_bytes, filename
            
    async def generate_questionnaire_pdf(self, client_id: str) -> Tuple[bytes, str]:
        """
        Generate a questionnaire-style PDF report for the specified client.
        
        Args:
            client_id: The ID of the client to generate the report for
            
        Returns:
            Tuple of (report_data_bytes, filename)
        """
        # For now, we'll implement a basic PDF generation
        # In a production environment, you might want to use libraries like reportlab or weasyprint
        raise NotImplementedError("PDF generation not yet implemented. Please use Excel or CSV format.")
    
    async def _generate_questionnaire_data(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """
        Generate questionnaire data by mapping client data to template fields.

        Args:
            client_data: Comprehensive client data dictionary

        Returns:
            List of (question, answer) tuples
        """
        questionnaire_data = []

        # Process each template item
        for template_item in self.questionnaire_template:
            question = template_item['field']
            db_field = template_item['db_field']
            transform = template_item.get('transform')

            # Get the answer value
            if not db_field:
                # Empty row or section header
                answer = ""
            else:
                answer = await self._get_field_value(client_data, db_field, transform)

            questionnaire_data.append((question, answer))

        # Add additional sections
        questionnaire_data.extend(await self._generate_shareholders_section(client_data))
        questionnaire_data.extend(await self._generate_directors_section(client_data))
        questionnaire_data.extend(await self._generate_officers_section(client_data))
        questionnaire_data.extend(await self._generate_bank_account_section(client_data))
        questionnaire_data.extend(await self._generate_financial_section(client_data))

        return questionnaire_data

    async def _get_field_value(self, client_data: Dict[str, Any], db_field: str, transform: Optional[str] = None) -> str:
        """
        Extract field value from client data with optional transformation.

        Args:
            client_data: Client data dictionary
            db_field: Dot-notation field path (e.g., 'primary_registration.state_of_incorporation')
            transform: Optional transformation function name

        Returns:
            String value for the field
        """
        try:
            # Handle nested field access
            value = client_data
            for field_part in db_field.split('.'):
                if field_part.isdigit():
                    # Array index access
                    index = int(field_part)
                    if isinstance(value, list) and len(value) > index:
                        value = value[index]
                    else:
                        value = None
                        break
                else:
                    # Dictionary key access
                    value = value.get(field_part) if isinstance(value, dict) else None
                    if value is None:
                        break

            # Apply transformation if specified
            if transform and value is not None:
                value = await self._apply_transform(value, transform, client_data)

            # Convert to string and handle None values
            if value is None:
                return ""
            elif isinstance(value, bool):
                return "Yes" if value else "No"
            elif isinstance(value, datetime.datetime):
                return value.strftime('%m/%d/%Y')
            elif isinstance(value, (int, float)):
                return str(value)
            else:
                return str(value)

        except Exception as e:
            logger.warning(f"Error getting field value for {db_field}: {str(e)}")
            return ""

    async def _apply_transform(self, value: Any, transform: str, client_data: Dict[str, Any]) -> Any:
        """
        Apply transformation functions to field values.

        Args:
            value: The raw field value
            transform: The transformation function name
            client_data: Full client data for context

        Returns:
            Transformed value
        """
        if transform == "has_secondary_registration":
            return "Yes" if value and len(value) > 0 else "No"

        elif transform == "datetime_to_date":
            if isinstance(value, datetime.datetime):
                return value.strftime('%m/%d/%Y')
            return ""

        elif transform == "mailing_address":
            # Find mailing address from addresses list
            if isinstance(value, list):
                for addr in value:
                    if addr.get('address_type') == 'mailing' or addr.get('address_type') == 'registered':
                        address_obj = addr.get('address', {})
                        if isinstance(address_obj, dict):
                            parts = [
                                address_obj.get('street', ''),
                                address_obj.get('city', ''),
                                address_obj.get('state', ''),
                                address_obj.get('zip', '')
                            ]
                            return ', '.join([p for p in parts if p])
            return ""

        elif transform == "total_authorized_shares":
            if isinstance(value, list):
                return sum(share.get('stock_authorized', 0) for share in value)
            return ""

        elif transform == "total_issued_shares":
            if isinstance(value, list):
                return sum(share.get('stock_issued', 0) for share in value)
            return ""

        elif transform == "total_assets":
            # This would need to be calculated from financial data
            return ""

        elif transform == "shareholder_details":
            # Generate shareholder summary
            shareholders = client_data.get('llc_shareholders', [])
            if shareholders:
                details = []
                for sh in shareholders:
                    person = sh.get('person', {})
                    name = f"{person.get('firstname', '')} {person.get('lastname', '')}".strip()
                    ownership = sh.get('ownership', '')
                    if name and ownership:
                        details.append(f"{name} - {ownership}")
                return '; '.join(details)
            return ""

        elif transform == "filed_prior_returns":
            # Check if there are any filed tax reports
            tax_reports = client_data.get('tax_reports', [])
            return "Yes" if any(report.get('status') == 'FILED' for report in tax_reports) else "No"

        elif transform == "we_prepared_returns":
            # Check if any tax reports were filed by us
            tax_reports = client_data.get('tax_reports', [])
            return "Yes" if any(report.get('filed_by') == 'us' for report in tax_reports) else "No"

        elif transform == "filed_1099":
            # Check if 1099 forms were filed
            return "Yes" if "1099" in str(value).upper() else "No"

        elif transform == "yes_no":
            return "Yes" if value else "No"

        return value

    async def _generate_shareholders_section(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """Generate the shareholders/owners section."""
        section_data = [
            ("", ""),  # Empty row
            ("II. Shareholders/Owners", ""),
        ]

        shareholders = client_data.get('llc_shareholders', [])
        contacts = client_data.get('contacts', [])

        # Only show actual shareholders (not empty ones)
        actual_shareholders = []
        for shareholder in shareholders:
            person = shareholder.get('person', {})
            name = f"{person.get('firstname', '')} {person.get('lastname', '')}".strip()
            if name or shareholder.get('ownership'):  # Has name or ownership info
                actual_shareholders.append(shareholder)

        for i, shareholder in enumerate(actual_shareholders[:4], 1):
            person = shareholder.get('person', {})

            # Try to find phone number - first from person, then from contacts
            phone = person.get('phone', '')
            if not phone:
                phone = self._find_phone_in_contacts(person, contacts)

            section_data.extend([
                (f"Name {i}", f"{person.get('firstname', '')} {person.get('lastname', '')}".strip()),
                ("Citizenship", person.get('citizenship', '')),
                ("Address of Residence", person.get('address', '')),
                ("% of ownership", shareholder.get('ownership', '')),
                ("Phone", phone),
                ("", ""),  # Empty row
            ])

        # Add notes for shareholders
        notes_shareholders = client_data.get('notes_shareholders', '')
        if notes_shareholders or actual_shareholders:  # Show notes if there are shareholders or notes exist
            section_data.append(("Notes (Shareholders):", notes_shareholders))

        return section_data

    def _find_phone_in_contacts(self, person: Dict[str, Any], contacts: List[Dict[str, Any]]) -> str:
        """Find phone number for a person in the contacts list."""
        if not person or not contacts:
            return ""

        person_name = f"{person.get('firstname', '')} {person.get('lastname', '')}".strip().lower()
        if not person_name:
            return ""

        # Look for matching contact by name
        for contact in contacts:
            contact_person = contact.get('person', {})
            contact_name = f"{contact_person.get('firstname', '')} {contact_person.get('lastname', '')}".strip().lower()

            if contact_name == person_name:
                # Return phone from contact or contact's person
                return contact.get('phone', '') or contact_person.get('phone', '')

        return ""

    async def _generate_directors_section(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """Generate the directors section."""
        section_data = [
            ("", ""),  # Empty row
            ("III. Director(s) (at least one)", ""),
        ]

        # Get directors from contacts (assuming directors have position containing 'director')
        contacts = client_data.get('contacts', [])
        directors = [c for c in contacts if 'director' in str(c.get('position', '')).lower()]

        # Only show actual directors (not empty ones)
        actual_directors = []
        for director in directors:
            person = director.get('person', {})
            name = f"{person.get('firstname', '')} {person.get('lastname', '')}".strip()
            if name:  # Has name
                actual_directors.append(director)

        for i, director in enumerate(actual_directors, 1):
            person = director.get('person', {})

            # Use enhanced phone finding logic
            phone = person.get('phone', '') or director.get('phone', '')
            if not phone:
                phone = self._find_phone_in_contacts(person, contacts)

            section_data.extend([
                (f"Name {i}", f"{person.get('firstname', '')} {person.get('lastname', '')}".strip()),
                ("Address", person.get('address', '')),
                ("Phone number", phone),
                ("", ""),  # Empty row
            ])

        # Add notes for contacts
        notes_contacts = client_data.get('notes_contacts', '')
        if notes_contacts or actual_directors:  # Show notes if there are directors or notes exist
            section_data.append(("Notes (Directors/Contacts):", notes_contacts))

        return section_data

    async def _generate_officers_section(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """Generate the officers section."""
        section_data = [
            ("", ""),  # Empty row
            ("IV. Officer(s) (at least one, who will sign declaration)", ""),
        ]

        # Get officers from contacts (assuming officers have position containing 'ceo', 'president', etc.)
        contacts = client_data.get('contacts', [])
        officers = [c for c in contacts if any(title in str(c.get('position', '')).lower()
                                             for title in ['ceo', 'president', 'officer', 'cfo', 'cto'])]

        if officers:
            officer = officers[0]  # Take the first officer as CEO
            person = officer.get('person', {})

            # Use enhanced phone finding logic
            phone = person.get('phone', '') or officer.get('phone', '')
            if not phone:
                phone = self._find_phone_in_contacts(person, contacts)

            section_data.extend([
                ("CEO - name", f"{person.get('firstname', '')} {person.get('lastname', '')}".strip()),
                ("Address", person.get('address', '')),
                ("Phone number", phone),
            ])
        else:
            section_data.extend([
                ("CEO - name", ""),
                ("Address", ""),
                ("Phone number", ""),
            ])

        # Add notes for contacts (officers are also contacts)
        notes_contacts = client_data.get('notes_contacts', '')
        if notes_contacts:
            section_data.append(("Notes (Officers/Contacts):", notes_contacts))

        return section_data

    async def _generate_bank_account_section(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """Generate the bank account information section."""
        section_data = [
            ("", ""),  # Empty row
            ("V. BANK ACCOUNT INFORMATION for TAX WITHDRAWALS BY IRS", ""),
        ]

        bank_accounts = client_data.get('bank_accounts', [])

        if bank_accounts:
            account = bank_accounts[0]  # Take the first bank account

            section_data.extend([
                ("account name (if different from company name above)", ""),
                ("bank name", account.get('bank_name', '')),
                ("ABA for ACH witdrawal", account.get('aba_number', '')),
                ("account number", account.get('account_number', '')),
                ("Notes (Bank Account):", account.get('notes', '')),
                ("", str(datetime.datetime.now().year)),  # Current year
            ])
        else:
            section_data.extend([
                ("account name (if different from company name above)", ""),
                ("bank name", ""),
                ("ABA for ACH witdrawal", ""),
                ("account number", ""),
                ("Notes (Bank Account):", ""),
                ("", str(datetime.datetime.now().year)),  # Current year
            ])

        return section_data

    async def _generate_financial_section(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """Generate the financial transactions and profit/loss section."""
        section_data = [
            ("VI. ЕСЛИ БЫЛИ ФИНАНСОВЫЕ ТРАНСАКЦИИ МЕЖДУ КОРПОРАЦИЕЙ И ВЛАДЕЛЬЦАМИ-НЕРЕЗИДЕНТАМИ, необходимо подать форму 5472", ""),
            ("Сумма и назначение переводов ОТ КОМПАНИИ ВЛАДЕЛЬЦУ (имя:)", ""),
            ("Сумма и назначение переводов ОТ ВЛАДЕЛЬЦА (имя:) в КОМПАНИЮ", ""),
            ("", ""),  # Empty row
            ("", "$$$"),
            ("VII. Profit and Loss - если бухгалтерия ведется в QuickBooks, достаточно приложить отчеты сгенерированные прогрраммой.", ""),
            ("Total Revenue (общий доход, без учета расходов)", "0"),
            ("Total Expenses (общая сумма расходов - приложите таблицу расходов)", "0"),
            ("Gross Profit (Прибыль - Доход минус расход)", "0"),
            ("Balance at the beginning of the year \n(Сумма на счету на начало года)", "0"),
            ("Balance at the end of the year \n(сумма на счету на конец года)", "0"),
            ("Loans (займы, взятые в банке или предоставленные акционерами - приложите расшифровку; если акции не распределены официально все вложения акционеров рассматриваются как \"КРЕДИТ ОТ АКЦИОНЕРА\")", "0"),
            ("", ""),  # Empty row
            ("Owners Equity (investments/stock sales) (инвестиции / продажа акций, зафиксированные документально )", "0"),
            ("Bank Accounts outside of US, if any \n(Если есть банковские счета и активность за пределами США,  укажите Наименование банка, адрес банка, номер счета, максимальный баланс на счету за прошлый год)", "No"),
            ("", ""),  # Empty row
            ("If a Corporation is registered in California, was FTB tax paid? Please, incude receipt / proof of payment \nЕсли Корпорация зарегистрирована в Калифорнии, оплачивался ли минимальный налог штата ? Приложите инвойса или чека (квитанции на оплату)", "No"),
            ("", ""),  # Empty row
            ("Notes (Financial/Accounting):", client_data.get('notes_accounting', '')),
        ]

        return section_data
