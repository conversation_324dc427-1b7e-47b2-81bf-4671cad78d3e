from typing import Literal

import pydantic

from records.config.shared_config import SharedConfig
from records.db import api as db_api, models


class SearchResult(pydantic.BaseModel):
    type: Literal['document', 'client', 'person', 'address', 'reg_agent', 'bank_account']
    data: dict


async def perform_search(query: str, client_id: str = None, limit: int = 10) -> list[SearchResult]:
    ira_client = SharedConfig().ira_client
    if client_id:
        extractor = await db_api.get_client_extractor_by_client_and_type(client_id=client_id, type=models.ExtractorType.MAIN)
    else:
        extractor = await db_api.get_client_extractor_by_type(type=models.ExtractorType.GLOBAL)

    search_results = ira_client.extractor_search(extractor.external_id, query, limit=limit)
    # {'documents': [ Document() ]}
    results = []
    for doc in search_results['documents']:
        results.append(SearchResult(
            type='document',
            data=doc
        ))

    return results
