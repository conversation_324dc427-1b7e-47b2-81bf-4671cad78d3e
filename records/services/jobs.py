import collections
import inspect
import logging


logger = logging.getLogger(__name__)
JOB_COUNTER = collections.Counter()


def job_tracker(func):
    def decorator(*args, **kwargs):
        name = func.__name__
        inc(name)
        if inspect.isasyncgenfunction(func):
            # For async generator
            async def inner():
                try:
                    async for v in func(*args, **kwargs):
                        yield v
                finally:
                    dec(name)
        else:
            # For coroutine
            async def inner():
                try:
                    return await func(*args, **kwargs)
                finally:
                    dec(name)
        return inner()

    return decorator


def inc(job_name: str):
    # logger.info(f'Increase job {job_name}')
    JOB_COUNTER.update(**{job_name: 1})


def dec(job_name: str):
    # logger.info(f'Decrease job {job_name}')
    JOB_COUNTER.update(**{job_name: -1})
