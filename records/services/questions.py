import logging
from typing import List, Dict, Any, Optional

from records.config.shared_config import SharedConfig
from records.db import api as db_api, models
from records.services.file_processing import ExtractorResource

logger = logging.getLogger(__name__)


async def list_questions(client_id: str) -> List[Dict[str, Any]]:
    """
    List all questions for a client by retrieving extractor items
    from the main extractor.
    
    Args:
        client_id: The client ID
        
    Returns:
        List of questions
    """
    client = await db_api.get_client_by_id(client_id)
    ira_client = SharedConfig().ira_client
    
    # Get the main extractor for the client
    extractor = await ExtractorResource.ensure_created(
        client, ira_client, type_=models.ExtractorType.MAIN
    )
    
    # Get all extractor items
    items = await ira_client.aextractor_item_list(extractor.external_id)
    
    return items


async def create_question(client_id: str, question: str) -> Dict[str, Any]:
    """
    Create a new question for a client by creating an extractor item
    in the main extractor.
    
    Args:
        client_id: The client ID
        question: The question text
        
    Returns:
        The created question
    """
    client = await db_api.get_client_by_id(client_id)
    ira_client = SharedConfig().ira_client
    
    # Get the main extractor for the client
    extractor = await ExtractorResource.ensure_created(
        client, ira_client, type_=models.ExtractorType.MAIN
    )
    
    # Create the question as an extractor item
    item = await ira_client.aextractor_item_create(extractor.external_id, question)
    
    return item


async def delete_question(client_id: str, question_id: str) -> Dict[str, Any]:
    """
    Delete a question for a client by deleting an extractor item
    from the main extractor.
    
    Args:
        client_id: The client ID
        question_id: The question ID
        
    Returns:
        The deleted question
    """
    client = await db_api.get_client_by_id(client_id)
    ira_client = SharedConfig().ira_client
    
    # Get the main extractor for the client
    extractor = await ExtractorResource.ensure_created(
        client, ira_client, type_=models.ExtractorType.MAIN
    )
    
    # Delete the question
    await ira_client.aextractor_item_delete(extractor.external_id, question_id)
    

async def get_question_by_id(client_id: str, question_id: str) -> Dict[str, Any]:
    """
    Get a question for a client by retrieving an extractor item
    from the main extractor.
    
    Args:
        client_id: The client ID
        question_id: The question ID
        
    Returns:
        The question
    """
    client = await db_api.get_client_by_id(client_id)
    ira_client = SharedConfig().ira_client
    
    # Get the main extractor for the client
    extractor = await ExtractorResource.ensure_created(
        client, ira_client, type_=models.ExtractorType.MAIN
    )
    
    # Get the question
    item = await ira_client.aextractor_item_get(extractor.external_id, question_id)
    
    return item
