"""
Simplified client upload service that centralizes all upload-related logic.
This service handles the complete lifecycle of client uploads.
"""

import datetime
import io
import json
import logging
from typing import Dict, List, Tuple

from fastapi import HTTPException

from records.cache import global_cache
from records.config.shared_config import SharedConfig
from records.db import api as db_api, models, base
from records.services import detections, clients, client_structs, file_processing
from records.services.constants import UploadObjectType
from records.utils import json_utils, utils
from records.utils.singleton import Singleton

logger = logging.getLogger(__name__)


class DetectionTypeConfig(object, metaclass=Singleton):
    """Configuration for detection types and their field mappings."""

    CLIENT_DATA = {
        'detection_id_field': 'client_data_detection_id',
        'detection_item_id_field': 'client_data_detection_item_id',
    }

    METADATA_EXTRACTION = {
        'detection_id_field': 'metadata_extraction_detection_id',
        'detection_item_id_field': 'metadata_extraction_detection_item_id',
    }

    SMART_MERGE = {
        'detection_id_field': 'smart_merge_detection_id',
        'detection_item_id_field': 'smart_merge_detection_item_id',
    }

    TAX_REPORT_DETECTION = {
        'detection_id_field': 'tax_report_detection_id',
        'detection_item_id_field': 'tax_report_detection_item_id',
    }

    @classmethod
    def get_all_types(cls):
        return {
            models.DetectionType.CLIENT_DATA: cls.CLIENT_DATA,
            models.DetectionType.METADATA_EXTRACTION: cls.METADATA_EXTRACTION,
            models.DetectionType.SMART_MERGE: cls.SMART_MERGE,
            models.DetectionType.TAX_REPORT_DETECTION: cls.TAX_REPORT_DETECTION,
        }

    @classmethod
    def get_config(cls, detection_type: str):
        return cls.get_all_types().get(detection_type)


class ClientUploadService(object, metaclass=Singleton):
    """Centralized service for managing client upload lifecycle."""

    def __init__(self):
        self.ira_client = SharedConfig().ira_client

    async def create_upload(self, file_db: models.File, file_data: bytes, manager_id: str) -> models.ClientUpload:
        """Create a new client upload and start initial processing."""
        upload_dict = {
            'file_id': file_db.id,
            'manager_id': manager_id,
            'status': models.ClientUploadStatus.PROCESSING,
        }

        upload_db = await db_api.create_client_upload(upload_dict)

        # Start initial detection processing
        await self._start_initial_processing(upload_db, file_db, file_data)

        return upload_db

    async def _start_initial_processing(self, upload_db: models.ClientUpload, file_db: models.File, file_data: bytes):
        """Start initial file processing with metadata extraction only (Stage 1)."""
        detection_ids = await detections.ensure_client_init_detections()

        # Stage 1: Only start metadata extraction
        metadata_detection_item = await self.ira_client.adetection_item_create(
            detection_ids['metadata_detection'],
            fp=file_data,
            file_name=file_db.name,
            title=file_db.name,
            description=file_db.description,
        )

        # Store all detection IDs but only metadata detection item ID for now
        await db_api.update_client_upload(upload_db, {
            'client_data_detection_id': int(detection_ids['client_init_detection']),
            'smart_merge_detection_id': int(detection_ids['smart_merge_detection']),
            'metadata_extraction_detection_id': int(detection_ids['metadata_detection']),
            'tax_report_detection_id': int(detection_ids['tax_report_detection']),
            'metadata_extraction_detection_item_id': int(metadata_detection_item['id']),
        })

    async def _get_file_doc_type(self, upload_dict: dict, file_db_or_dict: models.File | dict) -> str:
        return await self._get_file_metafield(upload_dict, file_db_or_dict, 'type', 'doc_type')

    async def _get_file_date(self, upload_dict: dict, file_db_or_dict: models.File | dict) -> str:
        return await self._get_file_metafield(upload_dict, file_db_or_dict, 'date')

    async def _get_file_metafield(
        self,
        upload_dict: dict,
        file_db_or_dict: models.File | dict,
        field: str,
        db_field: str = None
    ) -> str:
        """Get file metafield from upload or file metadata."""
        db_field = db_field or field
        if isinstance(file_db_or_dict, dict):
            if file_db_or_dict.get('meta'):
                return file_db_or_dict['meta'].get(field)
            return file_db_or_dict[db_field]
        else:
            file_db = file_db_or_dict
            if file_db.meta:
                return file_db.meta.get(field)
            return getattr(file_db, db_field)

    async def list_uploads_with_status(self, **filters) -> Tuple[List[Dict], int]:
        """List uploads with current processing status."""
        uploads, count = await db_api.list_client_uploads(**filters)
        upload_dicts = [upload.to_dict() for upload in uploads]

        # Get file information
        file_ids = [upload['file_id'] for upload in upload_dicts if upload.get('file_id')]
        if file_ids:
            files = await db_api.list_client_files_all(file_ids)
            file_map = {f.id: f.to_dict() for f in files}
        else:
            file_map = {}

        # Attach file info to uploads
        for upload_dict in upload_dicts:
            file_dict = file_map.get(upload_dict['file_id'])
            upload_dict['file'] = file_dict
            upload_dict['doc_type'] = await self._get_file_doc_type(upload_dict, file_dict)
            upload_dict['date'] = await self._get_file_date(upload_dict, file_dict)

        # Batch update statuses
        await self._batch_update_upload_statuses(upload_dicts)

        return upload_dicts, count

    async def get_upload_with_status(self, upload_id: int, return_db: bool = False):
        """Get upload with current processing status."""
        upload_db = await db_api.get_client_upload_by_id(upload_id)
        upload_dict = upload_db.to_dict()

        # Get file information
        file_db = await db_api.get_client_file_by_id(upload_dict['file_id'])
        upload_dict['file'] = file_db.to_dict()

        # Update status based on detection item
        await self._update_upload_status(upload_dict)
        upload_dict['doc_type'] = await self._get_file_doc_type(upload_dict, file_db)
        upload_dict['date'] = await self._get_file_date(upload_dict, file_db)

        if return_db:
            return upload_dict, upload_db
        return upload_dict

    async def _update_upload_status(self, upload_dict: Dict):
        """Update single upload status based on sequential detection workflow."""
        if upload_dict['status'] == models.ClientUploadStatus.APPLIED:
            return

        # Stage 1: Check metadata extraction status
        metadata_item = await self._get_detection_item_with_cache(
            upload_dict, models.DetectionType.METADATA_EXTRACTION,
            upload_dict['metadata_extraction_detection_id'],
            upload_dict['metadata_extraction_detection_item_id']
        )

        # Process metadata completion and trigger conditional second detection if needed
        await self._handle_metadata_completion(upload_dict, metadata_item)

        # Stage 2: Check second detection status (client_data or tax_report)
        second_detection_item = await self._get_second_detection_item(upload_dict)

        # Process the final result
        await self._process_sequential_detection_result(upload_dict, metadata_item, second_detection_item)

    async def _batch_update_upload_statuses(self, upload_dicts: List[Dict]):
        """Efficiently update multiple upload statuses with sequential workflow optimization."""
        if not upload_dicts:
            return

        # Process each upload individually with the new sequential workflow
        for upload_dict in upload_dicts:
            upload_dict['doc_type'] = await self._get_file_doc_type(upload_dict, upload_dict['file'])

            if upload_dict['status'] == models.ClientUploadStatus.APPLIED:
                continue

            # Use the sequential workflow for each upload
            await self._update_upload_status(upload_dict)

    async def _handle_metadata_completion(self, upload_dict: Dict, metadata_item: Dict):
        """Handle metadata extraction completion and trigger conditional second detection (Stage 2)."""
        metadata_status = metadata_item.get('status', 'PROCESSING')
        upload_id = upload_dict['id']

        # Cache metadata detection item if needed
        if self._needs_caching(upload_dict, models.DetectionType.METADATA_EXTRACTION, metadata_item):
            await self._cache_detection_item(upload_id, models.DetectionType.METADATA_EXTRACTION, metadata_item)

        # Process file metadata
        if metadata_status == 'SUCCESS':
            # Check if file metadata already set
            if not upload_dict['file'].get('meta'):
                metadata_output = self._extract_detection_output(metadata_item)
                if not metadata_output:
                    metadata_output = {'type': 'Unknown', 'name': 'Unknown', 'date': 'Unknown'}

                await db_api.update_client_file(
                    models.File(id=upload_dict['file_id']),
                    {'meta': metadata_output, 'doc_type': metadata_output.get('type')}
                )
                upload_dict['file']['meta'] = metadata_output

            # Stage 2: Trigger conditional second detection based on metadata type
            await self._start_conditional_detection(upload_dict, metadata_item)

        elif metadata_status == 'ERROR':
            error_output = self._extract_detection_error(metadata_item)
            upload_dict['file']['meta'] = {'error': f'Failed to extract metadata: {error_output}'}

    async def _start_conditional_detection(self, upload_dict: Dict, metadata_item: Dict):
        """Start conditional second detection based on metadata type."""
        # Check if second detection already started
        if upload_dict.get('client_data_detection_item_id') or upload_dict.get('tax_report_detection_item_id'):
            return

        # Extract metadata to determine detection type
        metadata_output = self._extract_detection_output(metadata_item)
        document_type = metadata_output.get('type', '').lower()

        # Get file data for second detection
        file_db = await db_api.get_client_file_by_id(upload_dict['file_id'])
        file_path = detections.get_upload_file_path(file_db)
        read_file = await SharedConfig().file_manager.read_file(file_path)
        file_data = await SharedConfig().file_manager.get_data(read_file)

        if 'tax report' in document_type:
            # Trigger tax report detection
            detection_item = await self.ira_client.adetection_item_create(
                upload_dict['tax_report_detection_id'],
                fp=file_data,
                file_name=file_db.name,
                title=file_db.name,
                description=file_db.description,
            )
            await db_api.update_client_upload(
                models.ClientUpload(id=upload_dict['id']),
                {'tax_report_detection_item_id': int(detection_item['id'])}
            )
            upload_dict['tax_report_detection_item_id'] = int(detection_item['id'])
        else:
            # Trigger client data detection
            detection_item = await self.ira_client.adetection_item_create(
                upload_dict['client_data_detection_id'],
                fp=file_data,
                file_name=file_db.name,
                title=file_db.name,
                description=file_db.description,
            )
            await db_api.update_client_upload(
                models.ClientUpload(id=upload_dict['id']),
                {'client_data_detection_item_id': int(detection_item['id'])}
            )
            upload_dict['client_data_detection_item_id'] = int(detection_item['id'])

    async def _get_second_detection_item(self, upload_dict: Dict):
        if upload_dict.get('client_data_detection_item_id'):
            return await self._get_detection_item_with_cache(
                upload_dict, models.DetectionType.CLIENT_DATA,
                upload_dict['client_data_detection_id'],
                upload_dict['client_data_detection_item_id']
            )
        elif upload_dict.get('tax_report_detection_item_id'):
            return await self._get_detection_item_with_cache(
                upload_dict, models.DetectionType.TAX_REPORT_DETECTION,
                upload_dict['tax_report_detection_id'],
                upload_dict['tax_report_detection_item_id']
            )
        return {}

    async def _process_sequential_detection_result(
        self,
        upload_dict: Dict,
        metadata_item: Dict,
        second_detection_item: Dict
    ):
        """Process the result of the sequential detection workflow."""
        metadata_status = metadata_item.get('status', 'PROCESSING')
        second_status = second_detection_item.get('status', 'PROCESSING') if second_detection_item else 'PROCESSING'
        upload_id = upload_dict['id']

        # If metadata is not complete, upload is still processing
        if metadata_status != 'SUCCESS':
            if metadata_status == 'ERROR':
                upload_dict['status'] = models.ClientUploadStatus.ERROR
                if not upload_dict.get('error_message'):
                    error_message = self._extract_detection_error(metadata_item)
                    upload_dict['error_message'] = error_message
                    await db_api.update_client_upload(
                        models.ClientUpload(id=upload_id),
                        {'error_message': error_message, 'status': models.ClientUploadStatus.ERROR}
                    )
            else:
                upload_dict['status'] = models.ClientUploadStatus.PROCESSING
            return

        # If second detection hasn't started yet, still processing
        if not second_detection_item:
            upload_dict['status'] = models.ClientUploadStatus.PROCESSING
            return

        # Process second detection result
        if second_status == models.ClientUploadStatus.SUCCESS:
            # Determine detection type and handle accordingly
            if upload_dict.get('tax_report_detection_item_id'):
                await self._handle_tax_report_detection_completion(upload_dict, second_detection_item)
            else:
                await self._handle_client_data_detection_completion(upload_dict, second_detection_item)
        elif second_status == models.ClientUploadStatus.ERROR:
            upload_dict['status'] = models.ClientUploadStatus.ERROR
            if not upload_dict.get('error_message'):
                error_message = self._extract_detection_error(second_detection_item)
                upload_dict['error_message'] = error_message
                await db_api.update_client_upload(
                    models.ClientUpload(id=upload_id),
                    {'error_message': error_message, 'status': models.ClientUploadStatus.ERROR}
                )
        else:
            upload_dict['status'] = models.ClientUploadStatus.PROCESSING

    async def _handle_client_data_detection_completion(self, upload_dict: Dict, detection_item: Dict):
        """Handle completion of client data detection (existing logic)."""
        upload_id = upload_dict['id']

        # Cache detection item if needed
        if self._needs_caching(upload_dict, models.DetectionType.CLIENT_DATA, detection_item):
            await self._cache_detection_item(upload_id, models.DetectionType.CLIENT_DATA, detection_item)

        if not upload_dict.get('output'):
            # Extract and save output
            output = self._extract_detection_output(detection_item)
            upload_dict['output'] = output

            await db_api.update_client_upload(
                models.ClientUpload(id=upload_id),
                {'output': output, 'status': models.ClientUploadStatus.REVIEW}
            )
        upload_dict['status'] = models.ClientUploadStatus.REVIEW

    async def _handle_tax_report_detection_completion(self, upload_dict: Dict, detection_item: Dict):
        """Handle completion of tax report detection with merge logic."""
        upload_id = upload_dict['id']

        # Cache detection item if needed
        if self._needs_caching(upload_dict, models.DetectionType.TAX_REPORT_DETECTION, detection_item):
            await self._cache_detection_item(upload_id, models.DetectionType.TAX_REPORT_DETECTION, detection_item)

        # Extract tax report data
        tax_report_output = self._extract_detection_output(detection_item)

        upload_dict['output'] = tax_report_output
        await db_api.update_client_upload(
            models.ClientUpload(id=upload_id),
            {'output': tax_report_output, 'status': models.ClientUploadStatus.REVIEW}
        )
        upload_dict['status'] = models.ClientUploadStatus.REVIEW

    async def merge_uploads_with_clients(self, upload_dicts: List[Dict]) -> List[Dict]:
        """Merge uploads with existing client data."""
        # Find existing clients by EIN and attach_ids
        client_map = await self._find_clients_by_eins(upload_dicts)
        attach_client_map = await self._find_clients_by_attach_ids(upload_dicts)

        # Process each upload
        for upload_dict in upload_dicts:
            # Skip processing state because it is not yet ready for merge
            if upload_dict['status'] == models.ClientUploadStatus.PROCESSING:
                continue
            # Skip applied uploads
            if upload_dict['status'] == models.ClientUploadStatus.APPLIED:
                upload_dict['exists'] = True
                continue
            await self._process_upload_merge(upload_dict, client_map, attach_client_map)

        return upload_dicts

    async def _clear_merge_info(self, upload_db: models.ClientUpload, status: str = models.ClientUploadStatus.REVIEW):
        """Clear merge-related info from upload."""
        # Delete merge detection item if exists
        if upload_db.smart_merge_detection_item_id:
            await detections.delete_detection_item(
                upload_db.smart_merge_detection_id, upload_db.smart_merge_detection_item_id
            )

        # Clear smart_merge from cache
        current_cache = upload_db.detection_items_cache or {}
        if models.DetectionType.SMART_MERGE in current_cache:
            del current_cache[models.DetectionType.SMART_MERGE]

        return await db_api.update_client_upload(upload_db, {
            'smart_merge_detection_item_id': None,
            'client': None,
            'client_output': None,
            'status': status,
            'detection_items_cache': current_cache,
        })

    async def apply_upload(
        self,
        upload_id: int,
        overridden_client_data: dict = None,
        skip_override: bool = False,
        skip_client: dict = None
    ):
        """Apply upload to client, creating new client if necessary.

        Args:
            upload_id: The ID of the upload to apply
            overridden_client_data: Optional client data to use instead of merged data
            skip_override: If True, skip the merge process and apply directly
            skip_client: If provided, skip client creation/update and use this client data
        """
        upload_dict, upload_db = await self.get_upload_with_status(upload_id, return_db=True)

        if upload_dict['status'] == models.ClientUploadStatus.APPLIED:
            return upload_dict

        if upload_dict['status'] in [
            models.ClientUploadStatus.PROCESSING, models.ClientUploadStatus.MERGING, models.ClientUploadStatus.ERROR
        ]:
            if not skip_override:
                raise ValueError(f'Cannot apply upload with {upload_dict['status']} status')

        if not skip_override:
            # Do merge process to get, process and merge client data
            dicts = await self.merge_uploads_with_clients([upload_dict])
            upload_dict = dicts[0]
            existing = upload_dict['exists']

            # If overridden_client_data is provided, store it as client_output
            if overridden_client_data and not skip_override:
                # Validate pydantic first
                client_structs.ClientUpdate.model_validate(overridden_client_data)
                await db_api.update_client_upload(
                    models.ClientUpload(id=upload_id), {'client_output': overridden_client_data}
                )
                upload_dict['client_output'] = overridden_client_data

            if existing:
                client_data_to_use = upload_dict['client_output']
            else:
                client_data_to_use = upload_dict['client_output'] or upload_dict['output']

            # Create or update client FIRST
            if client_data_to_use is None:
                raise ValueError('No client data available for apply operation')

            client_data_to_use['internal_draft_flag'] = True

            # Determine if this is an update or create operation
            client_id = client_data_to_use.get('id')
            if client_id:
                # Update existing client
                client_struct = client_structs.ClientUpdate.model_validate(client_data_to_use)
                client_dict = await clients.update_client_data(
                    client_id, client_struct.model_dump(exclude_unset=True)
                )
            else:
                # Create new client
                client_struct = client_structs.ClientCreate.model_validate(client_data_to_use)
                client_dict = await clients.create_client_data(client_struct.model_dump(exclude_unset=True))

            # Process tax reports AFTER client creation/update
            doc_type = await self._get_file_doc_type(upload_dict, upload_dict['file'])
            if 'tax report' in doc_type.lower():
                await self._process_tax_report(upload_dict, client_dict)
        else:
            if not skip_client:
                raise ValueError('No client data available for apply operation if skip_override is False')
            client_dict = skip_client

        # Attach upload to client (works for both normal and skip_override flows)
        await self._attach_upload_to_client(upload_db, client_dict['id'])

        post_process_action = await clients.apply_upload_for_file_process(upload_db, client_dict['id'], client_dict['name'])
        await post_process_action()

        # Refresh upload data to get updated file information (including client_id)
        upload_dict = await self.get_upload_with_status(upload_id)
        upload_dict['status'] = models.ClientUploadStatus.APPLIED

        return upload_dict

    async def _attach_upload_to_client(self, upload_db: models.ClientUpload, client_id: str):
        """
        Attach the client_upload to the target client during apply process.

        Args:
            upload_db: The ClientUpload model instance
            client_id: The target client ID to attach to
        """
        # 1. Client Existence Validation
        client_db = await db_api.get_client_by_id(client_id)

        # 2. Duplication Prevention
        if await self._is_upload_already_attached_to_client(upload_db.id, client_id):
            logger.info(f'Upload {upload_db.id} is already attached to client {client_id}, skipping attachment')
            # Even if already attached, ensure client property is set
            await self._set_upload_client_property(upload_db, client_id)
            return

        # 3. Create attachment
        await db_api.create_client_upload_attached_object({
            'upload_id': upload_db.id,
            'object_type': UploadObjectType.CLIENT,
            'object_id': client_id
        })

        # 4. Set the client property with current client data
        await self._set_upload_client_property(upload_db, client_id)

        logger.info(f'Successfully attached upload {upload_db.id} to client {client_id}')

    async def _is_upload_already_attached_to_client(self, upload_id: int, client_id: str) -> bool:
        attached_objects = await db_api.list_upload_attached_objects_by_upload_id(upload_id)

        for obj in attached_objects:
            if obj.object_type == UploadObjectType.CLIENT and obj.object_id == client_id:
                return True

        return False

    async def _set_upload_client_property(self, upload_db: models.ClientUpload, client_id: str):
        _, client_data = await clients.get_client_data(client_id)

        # Update the upload's client property
        await db_api.update_client_upload(upload_db, {'client': client_data})

        logger.info(f'Successfully set client property for upload {upload_db.id} with client {client_id} data')

    async def delete_client_upload(
        self, upload_db: models.ClientUpload = None,
        upload_id: int = None,
        delete_file_db: bool = False,
        deletion_behavior: str = models.ClientUploadDeletionBehavior.DEFAULT
    ):
        if not upload_id and not upload_db:
            raise ValueError('Either upload_id or upload_db must be provided')
        if not upload_db:
            upload_db = await db_api.get_client_upload_by_id(upload_id)

        file_db = await db_api.get_client_file_by_id(upload_db.file_id)
        file_manager = SharedConfig().file_manager
        file_path = detections.get_upload_file_path(file_db)

        # Delete detection items
        if upload_db.client_data_detection_item_id:
            await detections.delete_detection_item(
                upload_db.client_data_detection_id, upload_db.client_data_detection_item_id
            )
        if upload_db.metadata_extraction_detection_item_id:
            await detections.delete_detection_item(
                upload_db.metadata_extraction_detection_id, upload_db.metadata_extraction_detection_item_id
            )
        if upload_db.smart_merge_detection_item_id:
            await detections.delete_detection_item(
                upload_db.smart_merge_detection_id, upload_db.smart_merge_detection_item_id
            )
        if upload_db.tax_report_detection_item_id:
            await detections.delete_detection_item(
                upload_db.tax_report_detection_id, upload_db.tax_report_detection_item_id
            )

        # Delete upload attached objects before deleting the upload to avoid foreign key constraint violation
        await db_api.delete_upload_attached_objects_by_upload_id(upload_db.id)
        await db_api.delete_client_upload(upload_db.id)

        if file_db.client_id:
            # If file is attached to a client, delete it from there as well
            await file_processing.delete_file(file_db)

        # Handle file deletion with tax report reference check
        should_delete_physical_file = True
        if delete_file_db:
            should_delete_physical_file = await self._handle_file_deletion_with_tax_report_check(
                file_db, deletion_behavior
            )

        # Delete physical file only if we're not keeping it
        if should_delete_physical_file:
            await file_manager.delete_file(file_path)

    async def _handle_file_deletion_with_tax_report_check(self, file_db: models.File, deletion_behavior: str) -> bool:
        """
        Handle file deletion when it might be referenced by tax reports.

        Args:
            file_db: The file to delete
            deletion_behavior: How to handle the deletion (DELETE_TAX_REPORT_AND_FILE or KEEP_TAX_REPORT_AND_FILE)

        Returns:
            bool: True if the physical file should be deleted, False if it should be kept
        """
        # Check if file is referenced by any tax reports
        is_referenced = await db_api.is_file_referenced_by_tax_reports(file_db.id)

        if not is_referenced:
            # No tax report references, safe to delete the file
            await db_api.delete_client_file(file_db.id)
            return True  # Delete physical file

        if deletion_behavior == models.ClientUploadDeletionBehavior.DELETE_TAX_REPORT_AND_FILE:
            # Delete all tax reports that reference this file, then delete the file
            tax_reports = await db_api.list_client_tax_reports_by_file_id(file_db.id)
            for tax_report in tax_reports:
                # Delete any upload attached objects that reference this tax report first
                await db_api.delete_upload_attached_objects_by_object(UploadObjectType.TAX_REPORT, str(tax_report.id))
                # Delete the tax report
                await db_api.delete_client_tax_report(tax_report.id)

            # Now safe to delete the file
            await db_api.delete_client_file(file_db.id)
            return True  # Delete physical file

        elif deletion_behavior == models.ClientUploadDeletionBehavior.KEEP_TAX_REPORT_AND_FILE:
            # Keep both tax report and file - do not delete the file
            # This prevents the foreign key constraint violation
            return False  # Keep physical file
        else:
            raise ValueError(f"Unknown deletion behavior: {deletion_behavior}")

    async def _find_clients_by_eins(self, upload_dicts: List[Dict]) -> Dict[str, Dict]:
        """Find existing clients by EIN from upload outputs."""
        eins = set()
        for upload in upload_dicts:
            output = upload.get('output') or {}
            ein = utils.clean_ein(output.get('ein'))
            if ein:
                eins.add(ein)

        if not eins:
            return {}

        clients = await db_api.list_clients_by_eins(list(eins))
        client_map = {}

        for client in clients:
            client_data = client.to_dict()
            ein = utils.clean_ein(client_data.get('ein'))
            if ein:
                client_map[ein] = client_data

        return client_map

    async def _find_clients_by_attach_ids(self, upload_dicts: List[Dict]) -> Dict[str, Dict]:
        """Find existing clients by attached objects and legacy attach_object_id from uploads."""
        attach_ids = set()

        for upload in upload_dicts:
            # Check new attached_objects structure
            attached_objects = upload.get('attached_objects', [])
            for obj in attached_objects:
                if obj.get('object_type') == 'client':
                    attach_ids.add(obj['object_id'])

            # Check legacy attach_object_id for backward compatibility
            attach_object_id = upload.get('attach_object_id')
            if attach_object_id and upload.get('attach_object_type') == 'client':
                attach_ids.add(attach_object_id)

        if not attach_ids:
            return {}

        clients = await db_api.list_clients_all(ids=list(attach_ids))
        client_map = {}

        for client in clients:
            client_data = client.to_dict()
            client_map[client_data['id']] = client_data

        return client_map

    async def _process_upload_merge(self, upload_dict: Dict, client_map: Dict[str, Dict],
                                    attach_client_map: Dict[str, Dict] = None):
        """Process merge for a single upload."""
        if attach_client_map is None:
            attach_client_map = {}

        # First check attached_objects (new approach)
        attached_objects = upload_dict.get('attached_objects', [])
        client_attachments = [obj for obj in attached_objects if obj.get('object_type') == 'client']

        if client_attachments:
            # Use the first client attachment for merge process
            client_attachment = client_attachments[0]
            client_data = attach_client_map.get(client_attachment['object_id'])
            if client_data:
                _, client_data = await clients.get_client_data(client_data['id'])
                upload_dict['client'] = client_data
                upload_dict['exists'] = True
                await self._handle_client_merge(upload_dict, client_data)
                return

        # Legacy fallback: check attach_object_id (for backward compatibility)
        attach_object_id = upload_dict.get('attach_object_id')
        if attach_object_id and upload_dict.get('attach_object_type') == 'client':
            client_data = attach_client_map.get(attach_object_id)
            if client_data:
                _, client_data = await clients.get_client_data(client_data['id'])
                upload_dict['client'] = client_data
                upload_dict['exists'] = True
                await self._handle_client_merge(upload_dict, client_data)
                return
        elif not attach_object_id and upload_dict.get('attach_object_type') == 'client':
            # Just clear and not merge anything
            upload_dict['client'] = None
            upload_dict['client_output'] = None
            upload_dict['exists'] = False
            return

        # Fall back to EIN-based lookup if no attached clients or client not found
        output = upload_dict.get('output') or {}
        ein = utils.clean_ein(output.get('ein'))

        if not ein:
            upload_dict['client'] = None
            upload_dict['client_output'] = None
            upload_dict['exists'] = False
            return

        client_data = client_map.get(ein)
        if client_data:
            _, client_data = await clients.get_client_data(client_data['id'])
        upload_dict['client'] = client_data
        upload_dict['exists'] = bool(client_data)

        if client_data:
            await self._handle_client_merge(upload_dict, client_data)
        else:
            upload_dict['client_output'] = None

    async def _handle_client_merge(self, upload_dict: Dict, client_data: Dict):
        """Handle merging with existing client data."""
        merge_detection_item_id = upload_dict.get('smart_merge_detection_item_id')

        if merge_detection_item_id:
            # Check existing merge status
            await self._check_merge_status(upload_dict, client_data)
        else:
            # Start new merge process
            await self._start_merge_process(upload_dict, client_data)

        # Always provide a merged output for preview
        # upload_dict['client_output'] = await json_utils.amerge_jsons([
        #     client_data, upload_dict.get('output', {})
        # ])

    async def _check_merge_status(self, upload_dict: Dict, client_data: Dict):
        """Check status of existing merge process with caching optimization."""
        merge_detection_id = upload_dict['smart_merge_detection_id']
        merge_detection_item_id = upload_dict['smart_merge_detection_item_id']

        merge_item = await self._get_detection_item_with_cache(
            upload_dict, models.DetectionType.SMART_MERGE, merge_detection_id, merge_detection_item_id
        )

        merge_status = merge_item.get('status', 'PROCESSING')

        if merge_status == 'SUCCESS':
            # Extract merged result
            merged_output = self._extract_detection_output(merge_item)
            upload_dict['client_output'] = merged_output
            upload_dict['status'] = models.ClientUploadStatus.REVIEW

            # do not always update client_output in DB
            if (
                    json_utils.is_json_db_different(upload_dict.get('client_output', {}), merged_output) or
                    json_utils.is_json_db_different(upload_dict.get('client', {}), client_data)
            ):
                await db_api.update_client_upload(
                    models.ClientUpload(id=upload_dict['id']),
                    {
                        'client_output': merged_output,
                        'client': client_data,
                    }
                )

        elif merge_status == 'ERROR':
            error_message = self._extract_detection_error(merge_item)
            upload_dict['status'] = models.ClientUploadStatus.ERROR
            upload_dict['error_message'] = error_message

        else:
            upload_dict['status'] = models.ClientUploadStatus.MERGING

    async def _start_merge_process(self, upload_dict: Dict, client_data: Dict):
        """Start new merge process with IRA."""
        merge_detection_id = upload_dict['smart_merge_detection_id']

        input_json = {
            'left': client_data,
            'right': upload_dict.get('output', {})
        }

        fp = io.BytesIO(json.dumps(input_json).encode())
        merge_item = await self.ira_client.adetection_item_create(
            merge_detection_id,
            fp=fp,
            file_name=f'merge_{client_data.get("name", "unknown")}.json',
            title=f'Merge {client_data.get("name", "unknown")}',
        )

        await db_api.update_client_upload(
            models.ClientUpload(id=upload_dict['id']),
            {'smart_merge_detection_item_id': merge_item['id']}
        )

        upload_dict['smart_merge_detection_item_id'] = merge_item['id']
        upload_dict['status'] = models.ClientUploadStatus.MERGING

    async def update_upload(self, upload_db: models.ClientUpload, new_values: dict) -> Dict:
        """Update upload and return updated status."""
        doc_type = new_values.pop('doc_type', None)
        date = new_values.pop('date', None)
        overridden_client_data = new_values.pop('overridden_client_data', None)
        attached_objects = new_values.pop('attached_objects', None)

        # Handle overridden_client_data
        if overridden_client_data:
            # Validate pydantic first
            client_structs.ClientUpdate.model_validate(overridden_client_data)
            new_values['client_output'] = overridden_client_data

        # Handle attached_objects changes (new approach)
        if attached_objects is not None:
            await self._handle_attached_objects_change(upload_db, attached_objects)
        else:
            # Handle legacy attach_object_id changes (backward compatibility)
            attach_object_id = new_values.pop('attach_object_id', None)
            attach_object_type = new_values.pop('attach_object_type', None)

            # Validate attach_object_type when attach_object_id is provided
            if attach_object_id is not None:
                if attach_object_type != UploadObjectType.CLIENT:
                    raise HTTPException(
                        400, f'Invalid attach_object_type: {attach_object_type}, only "client" is supported'
                    )

                # Convert legacy format to new format
                legacy_attached_objects = []
                if attach_object_id and attach_object_type:
                    legacy_attached_objects = [{'object_type': attach_object_type, 'object_id': attach_object_id}]

                await self._handle_attached_objects_change(upload_db, legacy_attached_objects)

        if new_values:
            upload_db = await db_api.update_client_upload(upload_db, new_values)

        upload_dict = await self.get_upload_with_status(upload_db.id)
        dicts = await self.merge_uploads_with_clients([upload_dict])
        upload_dict = dicts[0]

        if doc_type or date:
            await self._update_file_metadata(upload_dict, doc_type, date)

        return upload_dict

    async def _handle_attached_objects_change(self, upload_db: models.ClientUpload, attached_objects: list):
        """Handle attached_objects change by updating the junction table and restarting merge process."""
        # Validate attached objects
        for obj in attached_objects:
            if not UploadObjectType.is_valid(obj.get('object_type')):
                raise HTTPException(
                    400, f'Invalid object_type: {obj.get("object_type")}, only {UploadObjectType.all()} are supported'
                )

            # Validate object types and ids
            if obj.get('object_type') == UploadObjectType.CLIENT and obj.get('object_id'):
                try:
                    client_db = await db_api.get_client_by_id(obj['object_id'])
                    if not client_db:
                        raise ValueError(f"{obj['object_type'].title()} with id {obj['object_id']} not found")
                except Exception as e:
                    raise HTTPException(400, f"Invalid client_id {obj['object_id']}: {str(e)}")

            elif obj.get('object_type') == UploadObjectType.TAX_REPORT and obj.get('object_id'):
                try:
                    tax_report_db = await db_api.get_client_tax_report_by_id(int(obj['object_id']))
                    if not tax_report_db:
                        raise ValueError(f"{obj['object_type'].title()} with id {obj['object_id']} not found")
                except Exception as e:
                    raise HTTPException(400, f"Invalid tax_report_id {obj['object_id']}: {str(e)}")

            # Ensure upload_id is set
            obj['upload_id'] = upload_db.id

        # Replace all attached objects for this upload
        await db_api.replace_upload_attached_objects(upload_db.id, attached_objects)

        # Check for tax report attachments and trigger automatic apply if needed
        tax_report_attachments = [obj for obj in attached_objects if obj['object_type'] == UploadObjectType.TAX_REPORT]

        if tax_report_attachments:
            await self._handle_tax_report_attachment(upload_db, tax_report_attachments)

        # Handle merge process for client attachments
        client_attachments = [obj for obj in attached_objects if obj['object_type'] == UploadObjectType.CLIENT]

        if client_attachments:
            # For now, use the first client attachment for merge process
            # TODO: In the future, we might want to handle multiple client merges
            client_attachment = client_attachments[0]
            client_id = client_attachment['object_id']
            if not client_id:
                # client_id is intentionally not provided, clear merge info
                await self._clear_merge_info(upload_db)
                return
            client_db = await db_api.get_client_by_id(client_id)

            # Clear previous merge detection item and restart merge process
            upload_db = await self._clear_merge_info(upload_db, status=models.ClientUploadStatus.MERGING)

            # Start new merge process with the attached client
            upload_dict = upload_db.to_dict()
            _, client_data = await clients.get_client_data(client_db.id)

            # Set the client property in the upload
            await self._set_upload_client_property(upload_db, client_id)

            await self._start_merge_process(upload_dict, client_data)
        else:
            # No client attachments, clear the client property
            await db_api.update_client_upload(upload_db, {'client': None})
            logger.info(f'Cleared client property for upload {upload_db.id} (no client attachments)')

    async def _handle_tax_report_attachment(self, upload_db: models.ClientUpload, tax_report_attachments: list):
        """
        Handle tax report attachment by automatically triggering apply_upload.
        """
        try:
            # Check if upload is in a valid state for application
            if not self._is_upload_ready_for_apply(upload_db):
                msg = (
                    f'Upload {upload_db.id} is not ready for automatic apply. '
                    f'Current status: {upload_db.status}.'
                )
                raise HTTPException(400, msg)

            # Get the tax report to determine the client context
            tax_report_attachment = tax_report_attachments[0]  # Use first tax report for client context
            tax_report_db = await db_api.get_client_tax_report_by_id(int(tax_report_attachment['object_id']))

            if not tax_report_db:
                logger.error(f'Tax report {tax_report_attachment["object_id"]} not found for automatic apply')
                return

            # Get client data for the tax report
            client_db = await db_api.get_client_by_id(tax_report_db.client_id)
            if not client_db:
                logger.error(f'Client {tax_report_db.client_id} not found for tax report {tax_report_db.id}')
                return

            logger.info(
                f'Automatically applying upload {upload_db.id} due to tax report attachment '
                f'to tax report {tax_report_db.id} for client {client_db.id}'
            )

            # Trigger automatic apply_upload
            # if a client upload file is not a tax report (check metadata.type), skip override
            file_db = await db_api.get_client_file_by_id(upload_db.file_id)
            file_meta = file_db.meta or {}
            if 'tax report' not in file_meta.get('type', '').lower():
                await self.apply_upload(upload_db.id, skip_override=True, skip_client=client_db.to_dict())
            else:
                await self.apply_upload(upload_db.id)

            logger.info(f'Successfully auto-applied upload {upload_db.id} for tax report attachment')

        except Exception as e:
            msg = (
                f'Failed to automatically apply upload {upload_db.id} for tax report attachment: {e}. '
                f'Tax report attachment was created but upload must be applied manually.'
            )
            raise HTTPException(500, msg)

    def _is_upload_ready_for_apply(self, upload_db: models.ClientUpload) -> bool:
        # Upload must not be in processing states or error states
        invalid_states = [
            models.ClientUploadStatus.PROCESSING,
            models.ClientUploadStatus.ERROR,
            models.ClientUploadStatus.APPLIED  # Already applied
        ]

        if upload_db.status in invalid_states:
            return False

        # TODO: Upload should have some output data to apply?
        # if not upload_db.output:
        #     logger.warning(f'Upload {upload_db.id} has no output data for apply')
        #     return False

        return True

    async def _process_tax_report(self, upload_dict: Dict, client_data_to_use: Dict):
        """
        Process tax report from client upload file and integrate with client data.

        This method handles both creation of new tax reports and updating of existing ones
        based on the tax report detection output from the upload.
        """
        output = upload_dict.get('output', {})
        client_id = client_data_to_use.get('id')

        # 1. Validate client exists
        if not client_id:
            logger.error('Cannot process tax report: client_id is missing from client_data_to_use')
            raise ValueError('Client must be created before processing tax reports')

        # 2. Check for tax report detection format
        if not self._has_tax_report_data(output):
            return

        # 3. Process each detected tax report
        tax_reports_data = output['tax_reports']
        for raw_tax_report_data in tax_reports_data:
            await self._process_single_tax_report(upload_dict, client_id, raw_tax_report_data)

    def _has_tax_report_data(self, output: Dict) -> bool:
        """Check if output contains valid tax report detection data."""
        return 'tax_reports' in output and isinstance(output['tax_reports'], list)

    async def _process_single_tax_report(self, upload_dict: Dict, client_id: str, raw_tax_report_data: Dict):
        """Process a single tax report from the detection output."""
        # Only process new tax reports (those without existing IDs)
        if raw_tax_report_data.get('id'):
            return

        # 1. Validate required fields
        if not self._validate_tax_report_data(raw_tax_report_data):
            return

        # 2. Prepare enhanced tax report data
        enhanced_tax_report_data = self._prepare_tax_report_data(
            upload_dict, client_id, raw_tax_report_data
        )

        # 3. Check if tax report already exists
        existing_tax_report = await self._check_existing_tax_report(
            client_id, enhanced_tax_report_data
        )

        # 4. Create or update tax report
        tax_report_db = await self._create_or_update_tax_report(
            existing_tax_report, enhanced_tax_report_data
        )

        # 5. Link upload to tax report
        await self._link_upload_to_tax_report(upload_dict['id'], tax_report_db.id)

    def _validate_tax_report_data(self, tax_report_data: Dict) -> bool:
        fiscal_year = tax_report_data.get('fiscal_year')
        tax_type = tax_report_data.get('type')

        if not fiscal_year or not tax_type:
            logger.warning(
                f'Skipping tax report with missing required fields: '
                f'fiscal_year={fiscal_year}, type={tax_type}'
            )
            return False

        return True

    def _prepare_tax_report_data(self, upload_dict: Dict, client_id: str, raw_data: Dict) -> Dict:
        """
        Prepare enhanced tax report data with file information and client context.

        Args:
            upload_dict: The upload dictionary containing file information
            client_id: The client ID to associate with the tax report
            raw_data: Raw tax report data from detection

        Returns:
            Dict: Enhanced tax report data ready for database operations
        """
        file_dict = upload_dict['file']
        file_meta = file_dict.get('meta', {})
        file_date = file_meta.get('date')

        # Create enhanced data with file and client information
        enhanced_data = raw_data.copy()
        enhanced_data.update({
            'file_id': file_dict['id'],
            'filed_date': raw_data.get('filed_date') or file_date,
            'status': raw_data.get('status') or 'COMPLETED',
            'client_id': client_id
        })

        return enhanced_data

    async def _check_existing_tax_report(self, client_id: str, tax_report_data: Dict):
        """
        Check if a tax report already exists for the same client, fiscal year, and type.

        Returns:
            TaxReport model instance if exists, None otherwise
        """
        fiscal_year = tax_report_data['fiscal_year']
        tax_type = tax_report_data['type']

        existing_tax_report = await db_api.get_existing_tax_report_by_client_year_type(
            client_id, fiscal_year, tax_type
        )

        return existing_tax_report

    async def _create_or_update_tax_report(self, existing_tax_report, tax_report_data: Dict):
        if existing_tax_report:
            return await self._update_existing_tax_report(existing_tax_report, tax_report_data)
        else:
            return await self._create_new_tax_report(tax_report_data)

    async def _create_new_tax_report(self, tax_report_data: Dict):
        logger.info(f'Creating new tax report from detection: {tax_report_data}')
        tax_report_db = await db_api.create_client_tax_report(tax_report_data)
        logger.info(f'Successfully created tax report {tax_report_db.id}')
        return tax_report_db

    async def _update_existing_tax_report(self, existing_tax_report, new_data: Dict):
        # Define fields that can be updated (exclude core identifying fields)
        updatable_fields = ['status', 'filed_date', 'form_info', 'filed_by', 'note', 'file_id']

        # Build update dictionary with only changed fields
        update_data = {}
        for field in updatable_fields:
            if field in new_data:
                new_value = new_data[field]
                current_value = getattr(existing_tax_report, field, None)

                # Only update if value has changed
                if new_value != current_value:
                    update_data[field] = new_value

        if update_data:
            logger.info(
                f'Updating existing tax report {existing_tax_report.id} '
                f'for client {existing_tax_report.client_id}, '
                f'fiscal_year {existing_tax_report.fiscal_year}, '
                f'type {existing_tax_report.type} with: {update_data}'
            )

            updated_tax_report = await db_api.update_client_tax_report(
                existing_tax_report, update_data
            )
            logger.info(f'Successfully updated tax report {existing_tax_report.id}')
            return updated_tax_report
        else:
            logger.info(
                f'No changes needed for existing tax report {existing_tax_report.id} '
                f'for client {existing_tax_report.client_id}'
            )
            return existing_tax_report

    async def _link_upload_to_tax_report(self, upload_id: int, tax_report_id: int):
        existing_attachments = await db_api.list_upload_attached_objects_by_upload_id(upload_id)

        # Look for existing tax report attachment
        tax_report_attachment_exists = any(
            attachment.object_type == UploadObjectType.TAX_REPORT and
            attachment.object_id == str(tax_report_id)
            for attachment in existing_attachments
        )

        if not tax_report_attachment_exists:
            # Create new attachment relationship
            await db_api.create_client_upload_attached_object({
                'upload_id': upload_id,
                'object_type': UploadObjectType.TAX_REPORT,
                'object_id': str(tax_report_id)
            })
            logger.info(f'Successfully linked upload {upload_id} to tax report {tax_report_id}')
        else:
            logger.info(f'Upload {upload_id} already linked to tax report {tax_report_id}')

    async def remerge_upload(self, upload_id: int) -> Dict:
        """Restart merge process for upload."""
        upload_db = await db_api.get_client_upload_by_id(upload_id)
        upload_db = await self._clear_merge_info(upload_db)
        upload_dict = await self.get_upload_with_status(upload_id)
        upload_dicts = await self.merge_uploads_with_clients([upload_dict])
        upload_dict = upload_dicts[0]
        return upload_dict

    async def _get_detection_item_safe(self, detection_id: int, item_id: int) -> Dict:
        """Safely get detection item, returning empty dict on error."""
        try:
            if detection_id and item_id:
                return await detections.get_detection_item(detection_id, item_id)
        except Exception as e:
            logger.warning(f"Failed to get detection item {item_id}: {e}")
        return {}

    async def _get_detection_item_for_type(self, upload_dict: Dict, detection_type: str,
                                           detection_item_map: Dict = None) -> Dict:
        """Get detection item for a specific type with caching optimization."""
        detection_config = DetectionTypeConfig.get_config(detection_type)
        if not detection_config:
            return {}

        # Check cache first
        cached_item = self._get_cached_detection_item(upload_dict, detection_type)
        if cached_item and self._is_terminal_status(cached_item):
            return cached_item

        # Get from provided map or fetch individually
        if detection_item_map is not None:
            detection_item_id = upload_dict[detection_config['detection_item_id_field']]
            detection_item = detection_item_map.get(detection_item_id, {})
        else:
            detection_id = upload_dict[detection_config['detection_id_field']]
            detection_item_id = upload_dict[detection_config['detection_item_id_field']]
            detection_item = await self._get_detection_item_safe(detection_id, detection_item_id)

        return detection_item

    async def _process_detection_item_caching(self, upload_dict: Dict, detection_type: str,
                                              detection_item: Dict, current_cache: Dict = None) -> Dict:
        """Process caching for a detection item if needed."""
        if self._needs_caching(upload_dict, detection_type, detection_item):
            current_cache = await self._cache_detection_item(
                upload_dict['id'], detection_type, detection_item, current_cache
            )
            # Update upload_dict cache for immediate use
            if upload_dict.get('detection_items_cache') is None:
                upload_dict['detection_items_cache'] = {}
            upload_dict['detection_items_cache'][detection_type] = detection_item

        return current_cache

    def _is_terminal_status(self, detection_item: Dict) -> bool:
        """Check if detection item status is terminal (SUCCESS or ERROR)."""
        status = detection_item.get('status', '') if detection_item else ''
        return status in ['SUCCESS', 'ERROR']

    def _get_cached_detection_item(self, upload_dict: Dict, detection_type: str) -> Dict:
        """Get cached detection item data for the specified type."""
        cache = upload_dict.get('detection_items_cache') or {}
        return cache.get(detection_type, {})

    def _needs_caching(self, upload_dict: Dict, detection_type: str, detection_item: Dict) -> bool:
        """Check if detection item needs to be cached."""
        if not detection_item or not self._is_terminal_status(detection_item):
            return False

        # Check if already cached with same data
        cached_item = self._get_cached_detection_item(upload_dict, detection_type)
        return cached_item != detection_item

    async def _cache_detection_item(
            self,
            upload_id: int,
            detection_type: str,
            detection_item: Dict,
            current_cache: Dict = None
    ):
        """Cache detection item data in the database."""
        if not detection_item or not self._is_terminal_status(detection_item):
            return

        # Use provided cache or fetch from DB
        if current_cache is None:
            upload_db = await db_api.get_client_upload_by_id(upload_id)
            current_cache = upload_db.detection_items_cache or {}
        else:
            current_cache = current_cache.copy()  # Don't modify the original

        # Check if already cached with same data
        if current_cache.get(detection_type) == detection_item:
            return current_cache  # No need to update

        # Update cache with new data
        current_cache[detection_type] = detection_item

        # Save to database
        await db_api.update_client_upload(
            models.ClientUpload(id=upload_id),
            {'detection_items_cache': current_cache}
        )

        return current_cache  # Return updated cache for reuse

    async def _get_detection_item_with_cache(
            self, upload_dict: Dict, detection_type: str,
            detection_id: int, item_id: int
    ) -> Dict:
        """Get detection item with caching optimization."""
        # Check cache first
        cached_item = self._get_cached_detection_item(upload_dict, detection_type)
        if cached_item and self._is_terminal_status(cached_item):
            return cached_item

        # Fetch from IRA
        detection_item = await self._get_detection_item_safe(detection_id, item_id)

        # Cache if terminal status
        if detection_item and self._is_terminal_status(detection_item):
            await self._cache_detection_item(upload_dict['id'], detection_type, detection_item)
            # Update the upload_dict cache for immediate use
            if upload_dict['detection_items_cache'] is None:
                upload_dict['detection_items_cache'] = {}
            upload_dict['detection_items_cache'][detection_type] = detection_item

        return detection_item

    async def invalidate_detection_cache(self, upload_id: int, detection_type: str = None):
        """Invalidate cached detection item data."""
        upload_db = await db_api.get_client_upload_by_id(upload_id)
        current_cache = upload_db.detection_items_cache or {}

        if detection_type:
            # Clear specific detection type
            if detection_type in current_cache:
                del current_cache[detection_type]
        else:
            # Clear all cache
            current_cache = {}

        await db_api.update_client_upload(
            models.ClientUpload(id=upload_id),
            {'detection_items_cache': current_cache}
        )

    def _extract_detection_output(self, detection_item: Dict) -> Dict:
        """Extract output from detection item results."""
        results = detection_item.get('results', [{'output': '{}'}])
        if results:
            output_text = results[-1].get('output', '{}')
            return json_utils.extract_json(output_text)
        return {}

    def _extract_detection_error(self, detection_item: Dict) -> str:
        """Extract error message from detection item results."""
        results = detection_item.get('results', [{'output': 'Unknown error'}])
        if results:
            return results[-1].get('output', 'Unknown error')
        return 'Unknown error'

    async def _update_file_metadata(self, upload_dict: Dict, doc_type: str = None, date: str | datetime.date = None):
        """Update file metadata in both database and upload_dict."""
        # Get current file metadata
        file_meta = upload_dict['file'].get('meta', {})

        # Update metadata with new values
        if date:
            file_meta['date'] = base.date_to_string(date)
        if doc_type:
            file_meta['type'] = doc_type

        # Prepare database update data
        update_data = {'meta': file_meta}
        if doc_type:
            update_data['doc_type'] = doc_type
        if date:
            update_data['date'] = date

        # Update database
        await db_api.update_client_file(models.File(id=upload_dict['file_id']), update_data)

        # Update upload_dict to reflect changes
        upload_dict['file']['meta'] = file_meta
        upload_dict['file']['doc_type'] = file_meta.get('type')
        upload_dict['file']['date'] = file_meta.get('date')
        upload_dict['doc_type'] = file_meta.get('type')
        upload_dict['date'] = file_meta.get('date')

    async def generate_download_link(self, file_id: str):
        # Generate / get temporary link
        download_token = await global_cache.get_cache().aget(f'file_ids/{file_id}')
        if not download_token:
            download_token = utils.generate_unicode_uuid()
            await global_cache.get_cache().aset(f'file_access/{download_token}', file_id)
            await global_cache.get_cache().aset(f'file_ids/{file_id}', download_token)

        return f'/api/v1/file_access/download?token={download_token}'


def get_client_upload_service():
    return ClientUploadService()
