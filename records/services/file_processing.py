import asyncio
import datetime
import mimetypes
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union

from fastapi import HTTPException

from records.config.shared_config import SharedConfig
from records.db import api as db_api, base
from records.db import models
from records.ira_chat_client.api_client import APIClient
from records.services import jobs, schemas, detections
from records.utils import utils


logger = logging.getLogger(__name__)


class IRAResource(ABC):
    """Base class for IRA resources (timeline, extractor, etc.)"""
    
    def __init__(self, resource_db: Union[models.ClientTimeline, models.ClientExtractor], ira_client: APIClient):
        self.resource_db = resource_db
        self.ira_client = ira_client
        self.external_id = str(resource_db.external_id)
        
    @classmethod
    async def ensure_created(cls, client_db: models.Client, ira_client, type_: str):
        """Ensures the resource exists, creating it if necessary"""
        pass

    @classmethod
    async def ensure_deleted(cls, client_db: models.Client, ira_client, type_: str):
        """Ensures the resource is deleted"""
        pass
    
    @abstractmethod
    async def upload_file(self, file_db: models.File, file_data: bytes, file_hash: str) -> Dict:
        """Upload file to the resource"""
        pass
    
    @abstractmethod
    async def get_file_by_hash(self, file_hash: str) -> Optional[Dict]:
        """Get file by hash"""
        pass
    
    @abstractmethod
    async def replace_file(self, file_external_id: str, file_name: str, file_data: bytes, **kwargs) -> Dict:
        """Replace existing file"""
        pass
    
    @abstractmethod
    async def delete_file(self, file_external_id: str, **kwargs) -> None:
        """Delete file from the resource"""
        pass

    @abstractmethod
    async def delete_resource(self) -> None:
        """Delete the resource"""
        pass

    @abstractmethod
    async def get_external_resource(self) -> Dict:
        """Get external resource"""
        pass
    
    @abstractmethod
    async def trigger_processing(self) -> None:
        """Trigger processing for the resource"""
        pass


class TimelineResource(IRAResource):
    """Timeline resource implementation"""
    
    @classmethod
    async def ensure_created(cls, client_db: models.Client, ira_client, type_=models.TimelineType.MAIN):
        timeline = await db_api.get_timeline_by_type(client_id=client_db.id, type_=type_)
        schema_by_type = {
            models.TimelineType.MAIN: schemas.client_report_schema,
            models.TimelineType.CLIENT_DATA: schemas.global_client_schema,
            models.TimelineType.APPROVED_DATA: schemas.api_client_schema,
        }
        
        if not timeline:
            name = get_timeline_name(type_, client_db)
            description = get_timeline_description(type_, client_db)
            timeline_ira = await ira_client.atimeline_create(
                name,
                schema_by_type[type_],
                config={
                    'prompts': {'system_prompt': detections.EXTRACTOR_PROMPT}
                },
                description=description
            )
            timeline = await db_api.create_timeline({
                'client_id': client_db.id,
                'type': type_,
                'external_id': timeline_ira['id'],
                'note': description,
            })
        
        return cls(timeline, ira_client)

    @classmethod
    async def ensure_deleted(cls, client_db: models.Client, ira_client, type_=models.TimelineType.MAIN):
        timeline = await db_api.get_timeline_by_type(client_id=client_db.id, type_=type_)
        if timeline:
            try:
                await ira_client.atimeline_delete(timeline.external_id)
            except ValueError as e:
                if 'not found' in str(e).lower():
                    pass
                else:
                    raise e
            await db_api.delete_timeline(timeline.id)
    
    async def get_file_by_hash(self, file_hash: str) -> Optional[Dict]:
        return await self.ira_client.atimeline_get_file_by_hash(self.external_id, file_hash)
    
    async def replace_file(self, file_external_id: str, file_name: str, file_data: bytes, **kwargs) -> Dict:
        point_id = kwargs.get('point_id')
        if not point_id:
            raise ValueError("point_id is required for timeline file replacement")
        return await self.ira_client.atimeline_file_replace(
            self.external_id, point_id, file_external_id, file_name, file_data
        )
    
    async def upload_file(self, file_db: models.File, file_data: bytes, file_hash: str) -> Dict:
        external_id = await db_api.get_external_id_by(
            internal_type='file',
            internal_id=file_db.id,
            external_app_type='timeline',
            external_app_id=self.external_id,
            external_item_type='file',
            notfoundok=True
        )

        external_file = await self.get_file_by_hash(file_hash)
        point = None
        
        if not external_file:
            if external_id:
                point = external_id.additional_info.get('point')
                external_file = await self.replace_file(
                    external_id.external_item_id, file_db.name, file_data, point_id=point['id']
                )
            else:
                point = await self.ira_client.apoint_get_by_date(self.external_id, str(datetime.date.today()))
                if not point:
                    today = datetime.date.today()
                    point = await self.ira_client.apoint_create(
                        self.external_id, 'File changes ' + utils.date_to_human_str(today),
                        str(today), description='New set of files on ' + utils.date_to_human_str(today)
                    )
                external_file = await self.ira_client.atimeline_file_upload(
                    self.external_id, point['id'], file_db.name, file_data
                )
        else:
            if external_id:
                point = external_id.additional_info.get('point')
            else:
                # Find corresponding point for the file
                points = await self.ira_client.apoint_list(self.external_id)
                point = None
                for p in points:
                    files = p['files']
                    for f in files:
                        if f['id'] == external_file['id']:
                            point = p
                            break
                if not point:
                    raise HTTPException(404, f'File {file_db.name} not found in timeline')

        await ensure_external_id(
            int_type='file',
            int_id=file_db.id,
            ext_app_type='timeline',
            ext_app_id=self.external_id,
            ext_item_type='file',
            ext_item_id=external_file['id'],
            additional_info={'point': point, 'point_id': point['id']}
        )
        
        return external_file
    
    async def get_external_resource(self) -> Dict:
        return await self.ira_client.atimeline_get(self.external_id)
    
    async def delete_file(self, file_external_id: str, **kwargs) -> None:
        point_id = kwargs.get('point_id')
        if not point_id:
            raise ValueError("point_id is required for timeline file deletion")
        await self.ira_client.atimeline_file_delete(
            self.external_id, point_id, file_external_id
        )

    async def delete_resource(self) -> None:
        await self.ira_client.atimeline_delete(self.external_id)
    
    async def trigger_processing(self) -> None:
        timeline_ira = await self.ira_client.atimeline_get(self.external_id)
        if timeline_ira.get('has_updates', True):
            await self.ira_client.atimeline_process(self.external_id)


class ExtractorResource(IRAResource):
    """Extractor resource implementation"""
    
    @classmethod
    async def ensure_created(cls, client_db: models.Client, ira_client, type_=models.ExtractorType.MAIN):
        extractor = await db_api.get_client_extractor_by_client_and_type(
            client_id=client_db.id, type=type_, notfoundok=True
        )
        
        if not extractor:
            name = get_timeline_name(type_, client_db)
            description = f'Extractor for client {client_db.name} [type={type_}]'
            extractor_ira = await ira_client.aextractor_create(
                name,
                data_schema=schemas.global_client_schema,
                config={
                    'prompts': {'system_prompt': detections.EXTRACTOR_PROMPT}
                },
                description=description
            )
            extractor = await db_api.create_client_extractor({
                'client_id': client_db.id,
                'external_id': extractor_ira['id'],
                'type': type_,
                'note': description,
            })
        
        return cls(extractor, ira_client)

    @classmethod
    async def ensure_deleted(cls, client_db: models.Client, ira_client, type_=models.ExtractorType.MAIN):
        extractor = await db_api.get_client_extractor_by_client_and_type(
            client_id=client_db.id, type=type_, notfoundok=True
        )
        if extractor:
            try:
                await ira_client.aextractor_delete(extractor.external_id)
            except ValueError as e:
                if 'not found' in str(e).lower():
                    pass
                else:
                    raise e
            await db_api.delete_client_extractor(extractor.id)
    
    async def get_file_by_hash(self, file_hash: str) -> Optional[Dict]:
        return await self.ira_client.aextractor_get_file_by_hash(self.external_id, file_hash)
    
    async def replace_file(self, file_external_id: str, file_name: str, file_data: bytes, **kwargs) -> Dict:
        return await self.ira_client.aextractor_replace_file(
            self.external_id, file_external_id, file_name, file_data
        )
    
    async def upload_file(self, file_db: models.File, file_data: bytes, file_hash: str) -> Dict:
        external_id = await db_api.get_external_id_by(
            internal_type='file',
            internal_id=file_db.id,
            external_app_type='extractor',
            external_app_id=self.external_id,
            external_item_type='file',
            notfoundok=True
        )
        
        # Check if file with same hash already exists
        external_file = await self.get_file_by_hash(file_hash)
        
        if not external_file:
            # File doesn't exist with this hash, proceed with upload/replace
            if external_id:
                external_file = await self.replace_file(
                    external_id.external_item_id, file_db.name, file_data
                )
            else:
                external_file = await self.ira_client.aextractor_upload_file(
                    self.external_id, file_db.name, file_data
                )

        await ensure_external_id(
            int_type='file',
            int_id=file_db.id,
            ext_app_type='extractor',
            ext_app_id=self.external_id,
            ext_item_type='file',
            ext_item_id=external_file['id']
        )
        
        return external_file
    
    async def delete_file(self, file_external_id: str, **kwargs) -> None:
        await self.ira_client.aextractor_delete_file(
            self.external_id, file_external_id
        )

    async def delete_resource(self) -> None:
        await self.ira_client.aextractor_delete(self.external_id)
    
    async def trigger_processing(self) -> None:
        extractor_ira = await self.ira_client.aextractor_get(self.external_id)
        if extractor_ira.get('has_updates', True):
            await self.ira_client.aextractor_process(
                self.external_id,
                wait_files=True,
            )
    
    async def list_files(self) -> List[Dict]:
        return await self.ira_client.aextractor_list_files(self.external_id)
    
    async def get_file(self, file_external_id: str) -> Dict:
        return await self.ira_client.aextractor_get_file(self.external_id, file_external_id)
    
    async def get_external_resource(self) -> Dict:
        return await self.ira_client.aextractor_get(self.external_id)
    
    async def download_file(self, file_external_id: str, inline: bool = False) -> bytes:
        return await self.ira_client.aextractor_download_file(self.external_id, file_external_id, inline=inline)


def get_timeline_name(type_, client):
    return f'{client.name}--{type_}'


def get_timeline_description(type_, client):
    return f'client_id = {client.id}; type = {type_}'


async def ensure_external_id(
    int_type, int_id,
    ext_app_type, ext_app_id,
    ext_item_type, ext_item_id,
    additional_info: dict = None
):
    external_id = await db_api.get_external_id_by(
        internal_type=int_type,
        internal_id=int_id,
        external_app_type=ext_app_type,
        external_app_id=str(ext_app_id),
        external_item_type=ext_item_type,
        external_item_id=str(ext_item_id),
        notfoundok=True
    )
    if not external_id:
        external_id = await db_api.create_external_id({
            'internal_id': int_id,
            'internal_type': int_type,
            'external_app_type': ext_app_type,
            'external_app_id': str(ext_app_id),
            'external_item_type': ext_item_type,
            'external_item_id': str(ext_item_id),
            'additional_info': additional_info,
        })

    return external_id


async def get_resource_from_external_id(external_id: models.ExternalID) -> IRAResource:
    """Create an appropriate resource object from an external ID"""
    ira_client = SharedConfig().ira_client
    
    if external_id.external_app_type == 'extractor':
        extractor = await db_api.get_client_extractor_by_external_id(int(external_id.external_app_id))
        return ExtractorResource(extractor, ira_client)
    elif external_id.external_app_type == 'timeline':
        timeline = await db_api.get_timeline_by_external_id(int(external_id.external_app_id))
        return TimelineResource(timeline, ira_client)
    else:
        raise ValueError(f'Unsupported external app type: {external_id.external_app_type}')


@jobs.job_tracker
async def process_file(
    client: models.Client,
    file_name,
    file_data,
    doc_type: str = None,
    manager_id: str = None,
    file_db: models.File = None
):
    ira_client = SharedConfig().ira_client

    async with base.session_context():
        await db_api.client_select_for_update(client.id)
        # Ensure timeline / extractors created
        timeline = await TimelineResource.ensure_created(client, ira_client, type_=models.TimelineType.CLIENT_DATA)
        t = await TimelineResource.ensure_created(client, ira_client, type_=models.TimelineType.APPROVED_DATA)
        extractor = await ExtractorResource.ensure_created(client, ira_client, type_=models.ExtractorType.MAIN)

    file_hash = utils.hash_sha256(file_data)
    if not file_db:
        file_db = await db_api.get_client_file_by_hash(client.id, hash=file_hash)
    if not file_db:
        file_db = await db_api.create_client_file({
            'client_id': client.id,
            'name': file_name,
            'hash': file_hash,
            'file_type': mimetypes.guess_type(file_name)[0],
            'manager_id': manager_id,
            'doc_type': doc_type,
        })

    # Process file
    resources = [extractor, timeline]
    await upload_file_to_ira(
        resources,
        file_db,
        file_data,
        file_hash,
    )
    # Trigger processing
    asyncio.create_task(trigger_processing_ira(resources))


async def list_external_files(client_id: str):
    extractor = await ExtractorResource.ensure_created(
        await db_api.get_client_by_id(client_id),
        SharedConfig().ira_client,
        type_=models.ExtractorType.MAIN
    )

    external_files = await db_api.list_external_id_by(
        internal_type='file',
        external_app_type='extractor',
        external_app_id=extractor.external_id,
        external_item_type='file',
    )
    extractor_files = await extractor.list_files()

    external_to_internal = {f.external_item_id: f.internal_id for f in external_files}
    internal_to_external = {}
    for ext_file in extractor_files:
        int_id = external_to_internal.get(ext_file['id'])
        if int_id:
            internal_to_external[int_id] = ext_file

    return internal_to_external


async def get_external_file(file_db: models.File):
    extractor = await ExtractorResource.ensure_created(
        await db_api.get_client_by_id(file_db.client_id),
        SharedConfig().ira_client,
        type_=models.ExtractorType.MAIN
    )

    file_external_id = await db_api.get_external_id_by(
        internal_type='file',
        internal_id=file_db.id,
        external_app_type='extractor',
        external_app_id=extractor.external_id,
        external_item_type='file',
    )
    return await extractor.get_file(file_external_id.external_item_id)


async def download_file(file_db: models.File, inline: bool = False):
    extractor = await ExtractorResource.ensure_created(
        await db_api.get_client_by_id(file_db.client_id),
        SharedConfig().ira_client,
        type_=models.ExtractorType.MAIN
    )

    file_external_id = await db_api.get_external_id_by(
        internal_type='file',
        internal_id=file_db.id,
        external_app_type='extractor',
        external_app_id=extractor.external_id,
        external_item_type='file',
    )
    return await extractor.download_file(file_external_id.external_item_id, inline=inline)


@jobs.job_tracker
async def delete_file(file_db: models.File):
    # Get all external IDs for this file
    file_external_ids = await db_api.list_external_id_by(
        internal_type='file',
        internal_id=file_db.id,
    )
    
    # Delete files from all resources
    tasks = []
    for external_id in file_external_ids:
        try:
            resource = await get_resource_from_external_id(external_id)
            if external_id.external_app_type == 'timeline':
                kwargs = {'point_id': external_id.additional_info.get('point_id')}
            else:
                kwargs = {}
            tasks.append(resource.delete_file(external_id.external_item_id, **kwargs))
        except ValueError as e:
            # Skip unsupported resources
            continue
    
    await asyncio.gather(*tasks)
    
    # Delete external IDs
    await db_api.delete_external_id_by(
        internal_type='file',
        internal_id=file_db.id,
    )
    
    # Reprocess
    await trigger_processing_ira(file_external_ids)


async def delete_resources(client: models.Client):
    files = await db_api.list_client_files(client_id=client.id)
    # Get all external IDs for files for this client
    file_external_ids = await db_api.list_external_id_by(
        internal_type='file',
        internal_id=[f.id for f in files],
    )
    ira_client = SharedConfig().ira_client
    await TimelineResource.ensure_deleted(client, ira_client, type_=models.TimelineType.CLIENT_DATA)
    await TimelineResource.ensure_deleted(client, ira_client, type_=models.TimelineType.APPROVED_DATA)
    await ExtractorResource.ensure_deleted(client, ira_client, type_=models.ExtractorType.MAIN)

    if files:
        # Delete external IDs
        await db_api.delete_external_id_by(
            internal_type='file',
            internal_id=[f.id for f in files],
        )


async def upload_file_to_ira(
    resources: List[IRAResource],
    file_db: models.File,
    file_data: bytes,
    file_hash: str,
):
    """Upload file to all provided IRA resources"""
    tasks = [
        resource.upload_file(file_db, file_data, file_hash) 
        for resource in resources
    ]
    await asyncio.gather(*tasks)


async def trigger_processing_ira(
    resources: List[Union[IRAResource, models.ExternalID]],
):
    """Trigger processing for all resources"""
    tasks = []
    
    for resource in resources:
        try:
            if isinstance(resource, IRAResource):
                tasks.append(resource.trigger_processing())
            elif isinstance(resource, models.ExternalID):
                # Convert ExternalID to corresponding resource
                resource_obj = await get_resource_from_external_id(resource)
                tasks.append(resource_obj.trigger_processing())
            else:
                raise ValueError(f'Unsupported resource type: {type(resource)}')
        except ValueError as e:
            # Skip unsupported resources
            continue
    
    await asyncio.gather(*tasks)


async def process_init_upload(
    upload_db: models.ClientUpload,
    file_db: models.File,
    file_data: bytes,
    file_hash: str,
):
    ira_client = SharedConfig().ira_client
    detection_ids = await detections.ensure_client_init_detections()
    detection_item = await ira_client.adetection_item_create(
        detection_ids['client_init_detection'],
        fp=file_data,
        file_name=file_db.name,
        title=file_db.name,
        description=file_db.description,
    )
    upload_db = await db_api.update_client_upload(upload_db, {
        'client_init_detection_id': int(detection_ids['client_init_detection']),
        'smart_merge_detection_id': int(detection_ids['smart_merge_detection']),
        'client_init_detection_item_id': int(detection_item['id']),
    })

    return upload_db, detection_item
