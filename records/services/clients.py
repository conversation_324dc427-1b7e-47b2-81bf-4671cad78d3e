import copy
import datetime
import logging
from collections import defaultdict
from typing import Type, List

from records.api import common_utils
from records.config.shared_config import SharedConfig
from records.context import context as ctx
from records.db import api as db_api
from records.db import base
from records.db import models
from records.policies import policies
from records.services import schemas, pending_changes, file_processing, detections
from records.services.constants import UploadObjectType
from records.services.file_processing import TimelineResource
from records.utils import json_utils, utils

logger = logging.getLogger(__name__)
default_data_source = {'type': 'manual', 'id': None}


async def get_client_data(client_id: str) -> tuple[models.Client, dict]:
    async with base.session_context():
        db_client = await db_api.get_client_by_id(client_id)

        client_dict = db_client.to_dict()
        # Attach all connected data
        client_dict['addresses'] = common_utils.to_dict(await db_api.list_client_addresses(client_id=client_id))
        # client_dict['authorized_signers'] = common_utils.to_dict(await db_api.list_client_authorized_signers(client_id=client_id))
        client_dict['bank_accounts'] = common_utils.to_dict(await db_api.list_bank_accounts(client_id=client_id))
        client_dict['contacts'] = common_utils.to_dict(await db_api.list_client_contacts(client_id=client_id))
        # client_dict['files'] = common_utils.to_dict(await db_api.list_client_files(client_id=client_id))
        if ctx.current_role() in [policies.RoleName.OWNER, policies.RoleName.MANAGER]:
            client_dict['payment_cards'] = common_utils.to_dict(await db_api.list_debit_cards(client_id=client_id))
        client_dict['payment_services'] = common_utils.to_dict(await db_api.list_client_payment_systems(client_id=client_id))
        client_dict['primary_registration'] = common_utils.to_dict(
            (await db_api.get_primary_client_registration(client_id=client_id)) or db_api.default_registration()
        )
        client_dict['secondary_registrations'] = common_utils.to_dict(await db_api.list_client_registrations(client_id=client_id))
        # client_dict['services'] = common_utils.to_dict(await db_api.list_client_services(client_id=client_id))
        client_dict['shares'] = common_utils.to_dict(await db_api.list_client_shares(client_id=client_id))
        client_dict['llc_shareholders'] = common_utils.to_dict(await db_api.list_client_shareholders(client_id=client_id))
        # client_dict['tasks'] = common_utils.to_dict(await db_api.list_client_tasks(client_id=client_id))
        client_dict['tax_reports'] = common_utils.to_dict(await db_api.list_client_tax_reports(client_id=client_id))
        client_dict['capitalization_table'] = common_utils.to_dict(await db_api.list_client_capitalizations(client_id=client_id))

        client_dict['manager'] = common_utils.to_dict(await db_api.get_manager_by_id(db_client.manager_id, notfoundok=True))
        client_dict['source'] = common_utils.to_dict(await db_api.get_source_by_id(db_client.source_id, notfoundok=True))

    return db_client, client_dict


async def get_last_approved_client_data(client_id: str):
    async with base.session_context():
        db_client = await db_api.get_client_by_id(client_id)
        ira_client = SharedConfig().ira_client
        timeline = await TimelineResource.ensure_created(db_client, ira_client, type_=models.TimelineType.APPROVED_DATA)

        timeline_ira = await timeline.get_external_resource()
        if len(timeline_ira['points']) == 0:
            _, client_data = await get_client_data(client_id)
            return client_data

        # Get last result
        result = await ira_client.atimeline_get_result(timeline_ira['id'])
        return result['output']


def list_client_tabs():
    return [
        {'name': 'addresses', 'display_name': 'Addresses', 'permissions': ['read', 'write', 'delete']},
        # {'name': 'authorized_signers', 'display_name': 'Authorized Signers', 'permissions': ['read', 'write', 'delete']},
        {'name': 'bank_accounts', 'display_name': 'Bank Accounts', 'permissions': ['read', 'write', 'delete']},
        {'name': 'contacts', 'display_name': 'Contacts', 'permissions': ['read', 'write', 'delete']},
        {'name': 'files', 'display_name': 'Files', 'permissions': ['read', 'write', 'delete']},
        {'name': 'payment_cards', 'display_name': 'Payment Cards'},
        {'name': 'payment_services', 'display_name': 'Payment Systems', 'permissions': ['read', 'write', 'delete']},
        {'name': 'primary_registration', 'display_name': 'Primary Registration', 'permissions': ['read', 'write', 'delete']},
        {'name': 'secondary_registrations', 'display_name': 'Secondary Registrations', 'permissions': ['read', 'write', 'delete']},
        {'name': 'services', 'display_name': 'Services', 'permissions': ['read', 'write', 'delete']},
        {'name': 'shares', 'display_name': 'Shares', 'permissions': ['read', 'write', 'delete']},
        {'name': 'llc_shareholders', 'display_name': 'LLC Shareholders', 'permissions': ['read', 'write', 'delete']},
        {'name': 'capitalization_table', 'display_name': 'Capitalization Table', 'permissions': ['read', 'write', 'delete']},
        {'name': 'tasks', 'display_name': 'Tasks', 'permissions': ['read', 'write', 'delete']},
        {'name': 'tax_reports', 'display_name': 'Tax Reports', 'permissions': ['read', 'write', 'delete']},
    ]


def clean_for_create(obj: dict, additional_keys: list[str] = None):
    obj.pop('id', None)
    obj.pop('client_id', None)
    obj.pop('created_at', None)
    obj.pop('updated_at', None)
    if additional_keys:
        for key in additional_keys:
            obj.pop(key, None)
    return obj


@base.session_aware()
async def update_client_collection_with_inner_obj(
    client, old, new,
    db_model: Type[models.ClientContact | models.ClientRegistration | models.ClientLLCShareholder | models.ClientShare],
    delete_func,
    create_func,
    update_func,
    inner_id_key=None,
    inner_obj_key=None,
    session=None
):
    old_items = {o['id']: o for o in old if o.get('id')}
    items_to_update = [item for item in new if item.get('id')]
    items_to_create = [item for item in new if not item.get('id')]
    ids_to_delete = set(old_items.keys()) - {item['id'] for item in items_to_update}

    if ids_to_delete:
        logger.info(f'Deleting {db_model.__name__} with ids: {ids_to_delete}')

        await delete_func(ids=list(ids_to_delete), client_id=client.id)

    # Create items
    for item in items_to_create:
        item['client_id'] = client.id
        if inner_id_key:
            item[inner_id_key] = (item.get(inner_obj_key) or {}).get('id') or item.get(inner_id_key)
            item.pop(inner_obj_key, None)

        logger.info(f'Creating {db_model(**item)}')

        await create_func(item)

    # Update items
    for item in items_to_update:
        item_id = item.pop('id', None)
        item['client_id'] = client.id
        if inner_id_key:
            if item[inner_id_key] is None:
                # Try to get id from inner object
                item[inner_id_key] = (item.get(inner_obj_key) or {}).get('id')
            item.pop(inner_obj_key, None)

        old_item = old_items.get(item_id, {}).copy()
        old_item.pop(inner_obj_key, None)
        if json_utils.is_json_db_different(old_item, item):
            logger.info(f'Updating {db_model(**item)}')

            await update_func(db_model(id=item_id), item)


@base.session_aware()
async def update_client_addresses(client: models.Client, old: list, new: list, session=None):
    """
    Update client addresses (2 layers: client_address and address)
    """
    # Can create/update address
    # Can create/update/delete client address
    old_ids = {o["id"] for o in old}
    new_ids = {n["id"] for n in new if n.get("id")}
    common_ids = old_ids & new_ids
    old_map = {o["id"]: o for o in old}

    # Prepare lists for operations
    client_address_id_to_delete = list(old_ids - new_ids)
    client_address_to_update = [n.copy() for n in new if n.get('id') in common_ids]
    client_address_to_create_only = [n.copy() for n in new if not n.get('id') and n.get('address_id')]
    address_to_update = [n.copy() for n in new if n.get('address_id')]
    address_to_create = [n.copy() for n in new if not n.get('address_id')]

    if client_address_id_to_delete:
        await db_api.delete_client_addresses(client_address_id_to_delete, client_id=client.id)

    for to_update in client_address_to_update:
        addr_id = to_update.pop("id", None)
        to_update.pop('address', None)
        to_update['client_id'] = client.id
        old_client_addr = old_map.get(addr_id, {}).copy()
        old_client_addr.pop('address', None)
        if json_utils.is_json_db_different(old_client_addr, to_update):
            await db_api.update_client_address(models.ClientAddress(id=addr_id), to_update)

    for to_create in client_address_to_create_only:
        to_create.pop('address', None)
        to_create['client_id'] = client.id
        await db_api.create_client_address(to_create)

    for addr_to_update in address_to_update:
        address_db = models.Address(id=addr_to_update['address_id'])
        addr_values = addr_to_update.get('address')
        if addr_values:
            addr_values['full_address'] = common_utils.build_full_address(addr_values)
            old_addr = old_map.get(addr_to_update['id'], {}).copy()
            if json_utils.is_json_db_different(old_addr.get('address'), addr_values):
                await db_api.update_address(address_db, addr_values)

    for addr_to_create in address_to_create:
        client_addr_create = 'id' not in addr_to_create
        addr_values = addr_to_create.get('address')
        new_addr = None
        if addr_values:
            addr_values['full_address'] = common_utils.build_full_address(addr_values)
            existing_addr = await db_api.get_address_by_full_address(addr_values['full_address'])
            new_addr = existing_addr if existing_addr else await db_api.create_address(addr_values)
        if client_addr_create:
            client_addr_to_create = dict(
                client_id=client.id,
                address_id=new_addr.id if addr_values and new_addr else None,
                **clean_for_create(addr_to_create, additional_keys=['address'])
            )
            await db_api.create_client_address(client_addr_to_create)


# async def update_client_authorized_signers(client: models.Client, old: list, new: list):
#     """
#     Update client authorized signers (2 layers: client_authorized_signer and person)
#     """
#     inner_id_key = 'client_person_id'
#     inner_obj_key = 'person'
#
#     await update_client_collection_with_inner_obj(
#         client, old, new,
#         db_model=models.ClientAuthorizedSigner,
#         delete_func=db_api.delete_client_authorized_signers,
#         create_func=db_api.create_authorized_signer,
#         update_func=db_api.update_authorized_signer,
#         inner_id_key=inner_id_key,
#         inner_obj_key=inner_obj_key
#     )


async def update_client_bank_accounts(client: models.Client, old: list, new: list):
    """
    Update client bank accounts (2 layers: 1. bank_accounts, 2. authorized_signers and bank_cards)
    """
    inner_id_key = None
    inner_obj_key = None
    inner_arr_key = 'authorized_signers'
    inner_arr_cards_key = 'bank_cards'
    # Delete also deletes connected authorized_signer_persons
    delete_func = db_api.delete_client_bank_accounts
    create_func = db_api.create_bank_account
    update_func = db_api.update_bank_account
    db_model = models.BankAccount

    old_items = {o['id']: o for o in old if o.get('id')}
    items_to_update = [item for item in new if item.get('id')]
    items_to_create = [item for item in new if not item.get('id')]
    ids_to_delete = set(old_items.keys()) - {item['id'] for item in items_to_update}

    if ids_to_delete:
        logger.info(f'Deleting {db_model.__name__} with ids: {ids_to_delete}')

        await delete_func(ids=list(ids_to_delete), client_id=client.id)

    # Create item
    for item in items_to_create:
        item['client_id'] = client.id
        if inner_id_key:
            item[inner_id_key] = item.get(inner_obj_key, {}).get('id')
            item.pop(inner_obj_key, None)
        if inner_arr_cards_key:
            item.pop(inner_arr_cards_key, None)
        if inner_arr_key:
            item.pop(inner_arr_key, None)

        logger.info(f'Creating {db_model.__name__}: {item}')

        created_item = await create_func(item)
        item['id'] = created_item.id

    # Update item
    for item in items_to_update:
        item_id = item.pop('id', None)
        item['client_id'] = client.id
        if inner_id_key:
            if item[inner_id_key] is None:
                # Try to get id from inner object
                item[inner_id_key] = (item.get(inner_obj_key) or {}).get('id')
            item.pop(inner_obj_key, None)

        old_item = old_items.get(item_id, {}).copy()

        # Update inner records
        if inner_arr_key:
            with_ids = [item for item in item.get(inner_arr_key, []) if item.get('id')]
            old_with_ids = [item for item in old_item.get(inner_arr_key, []) if item.get('id')]
            without_ids = [item for item in item.get(inner_arr_key, []) if not item.get('id')]
            inner_to_create = without_ids
            inner_to_update = with_ids
            old_inner_map = {item['id']: json_utils.clean_keys(item, ['person']) for item in old_with_ids}
            ids_to_delete = list(set([item['id'] for item in old_with_ids]) - set([item['id'] for item in with_ids]))

            if ids_to_delete:
                logger.info(f'Deleting {db_model.__name__} {inner_arr_key} with ids: {ids_to_delete}')
                await db_api.delete_bank_account_authorized_signers(ids=ids_to_delete, bank_account_id=item_id)
            for inner_item in inner_to_create:
                inner_item['bank_account_id'] = item_id
                inner_item['client_person_id'] = inner_item.get('client_person_id') or inner_item.get('person', {}).get('id')
                inner_item.pop('person', None)
                logger.info(f'Creating {db_model.__name__} {inner_arr_key}: {inner_item}')
                await db_api.create_bank_account_authorized_signer(inner_item)
            for inner_item in inner_to_update:
                obj = models.BankAccountAuthorizedSigner(id=inner_item['id'])
                inner_item['bank_account_id'] = item_id
                inner_item['client_person_id'] = inner_item.get('client_person_id') or inner_item.get('person', {}).get('id')
                inner_item.pop('person', None)
                if json_utils.is_json_db_different(old_inner_map.get(inner_item['id'], {}), inner_item):
                    inner_item.pop('id', None)
                    logger.info(f'Updating {db_model.__name__} {inner_arr_key}: {inner_item}')
                    await db_api.update_bank_account_authorized_signer(obj, inner_item)

        if inner_arr_cards_key:
            with_ids = [item for item in item.get(inner_arr_cards_key, []) if item.get('id')]
            old_with_ids = [item for item in old_item.get(inner_arr_cards_key, []) if item.get('id')]
            without_ids = [item for item in item.get(inner_arr_cards_key, []) if not item.get('id')]
            inner_to_create = without_ids
            inner_to_update = with_ids
            old_inner_map = {item['id']: json_utils.clean_keys(item, ['person']) for item in old_with_ids}
            ids_to_delete = list(set([item['id'] for item in old_with_ids]) - set([item['id'] for item in with_ids]))
            if ids_to_delete:
                logger.info(f'Deleting {db_model.__name__} {inner_arr_cards_key} with ids: {ids_to_delete}')
                await db_api.delete_bank_account_bank_cards(card_ids=ids_to_delete, bank_account_id=item_id)
            for inner_item in inner_to_create:
                inner_item['bank_account_id'] = item_id
                inner_item['client_id'] = client.id
                logger.info(f'Creating {db_model.__name__} {inner_arr_cards_key}: {models.BankCard(**inner_item)}')
                await db_api.create_bank_account_bank_card(inner_item)
            for inner_item in inner_to_update:
                obj = models.BankCard(id=inner_item['id'])
                inner_item['bank_account_id'] = item_id
                inner_item['client_id'] = client.id
                if json_utils.is_json_db_different(old_inner_map.get(inner_item['id'], {}), inner_item):
                    inner_item.pop('id', None)
                    logger.info(f'Updating {db_model.__name__} {inner_arr_cards_key}: {models.BankCard(**inner_item)}')
                    await db_api.update_bank_account_bank_card(obj, inner_item)

        # Done updating inner records

        old_item.pop(inner_arr_key, None)
        old_item.pop(inner_arr_cards_key, None)
        item.pop(inner_arr_key, None)
        item.pop(inner_arr_cards_key, None)
        if json_utils.is_json_db_different(old_item, item):
            logger.info(f'Updating {db_model.__name__}: {item}')
            await update_func(db_model(id=item_id), item)


async def update_client_payment_cards(client: models.Client, old: list, new: list):
    """
    Update client payment cards (1 layer: debit_cards)
    """
    inner_id_key = None
    inner_obj_key = None
    await update_client_collection_with_inner_obj(
        client, old, new,
        db_model=models.DebitCard,
        delete_func=db_api.delete_debit_cards,
        create_func=db_api.create_debit_card,
        update_func=db_api.update_debit_card,
        inner_id_key=inner_id_key,
        inner_obj_key=inner_obj_key
    )


async def update_client_payment_services(client: models.Client, old: list, new: list):
    """
    Update client payment services (1 layer: payment_systems)
    """
    inner_id_key = None
    inner_obj_key = None
    await update_client_collection_with_inner_obj(
        client, old, new,
        db_model=models.PaymentService,
        delete_func=db_api.delete_client_payment_systems,
        create_func=db_api.create_client_payment_system,
        update_func=db_api.update_client_payment_system,
        inner_id_key=inner_id_key,
        inner_obj_key=inner_obj_key
    )


async def update_client_shares(client: models.Client, old: list, new: list):
    """
    Update client share classes (1 layer: client_share)
    """
    inner_id_key = None
    inner_obj_key = None

    await update_client_collection_with_inner_obj(
        client, old, new,
        db_model=models.ClientShare,
        delete_func=db_api.delete_client_shares,
        create_func=db_api.create_client_share,
        update_func=db_api.update_client_share,
        inner_id_key=inner_id_key,
        inner_obj_key=inner_obj_key
    )


async def update_client_tasks(client: models.Client, old: list, new: list):
    """
    Update client tasks (1 layer: tasks)
    """
    inner_id_key = 'manager_id'
    inner_obj_key = 'manager'

    await update_client_collection_with_inner_obj(
        client, old, new,
        db_model=models.ClientTask,
        delete_func=db_api.delete_client_tasks,
        create_func=db_api.create_client_task,
        update_func=db_api.update_client_task,
        inner_id_key=inner_id_key,
        inner_obj_key=inner_obj_key
    )


async def review_create_client_persons(new: list[dict], person_key='person', person_id_key='client_person_id'):
    # Review and create not existing persons
    for n in new:
        if not n.get(person_key):
            continue
        person = n.get(person_key)
        if person.get('id') or n.get(person_id_key):
            continue
        person_db = await db_api.get_person_by_first_last_name(person['firstname'], person['lastname'])
        if not person_db:
            logger.info(f'Creating person: {person}')
            person['full_title'] = utils.build_full_title(person['firstname'], person['lastname'])
            person_db = await db_api.create_person(person)
        else:
            if json_utils.is_json_db_different(person_db.to_dict(), person, exclude=['full_title']):
                update_dict = {k: v for k, v in person.items() if v is not None}
                update_dict.pop('id', None)
                update_dict.pop('firstname', None)
                update_dict.pop('lastname', None)
                if update_dict:
                    logger.info(f'Updating person: {update_dict}')
                    person['full_title'] = utils.build_full_title(person['firstname'], person['lastname'])
                    person_db = await db_api.update_person(person_db, person)

        n[person_id_key] = person_db.id
        n[person_key]['id'] = person_db.id

    return new


async def update_client_contacts(client: models.Client, old: list, new: list):
    """
    Update client contacts (2 layers: contacts and persons)
    """
    inner_id_key = 'client_person_id'
    inner_obj_key = 'person'

    new = await review_create_client_persons(new, person_key=inner_obj_key, person_id_key=inner_id_key)

    await update_client_collection_with_inner_obj(
        client, old, new,
        db_model=models.ClientContact,
        delete_func=db_api.delete_client_contacts,
        create_func=db_api.create_client_contact,
        update_func=db_api.update_client_contact,
        inner_id_key=inner_id_key,
        inner_obj_key=inner_obj_key
    )


async def review_create_reg_agents(new: list[dict], reg_agent_key='registered_agent', reg_agent_id_key='registered_agent_id'):
    # Review and create not existing reg agents
    for n in new:
        if not n.get(reg_agent_key):
            continue
        reg_agent = n.get(reg_agent_key)
        if reg_agent.get('id') or n.get(reg_agent_id_key):
            continue
        reg_agent_db = await db_api.get_reg_agent_by(title=reg_agent['title'])

        reg_agent['address_id'] = reg_agent.get('address', {}).get('id') or reg_agent.get('address_id')
        reg_agent.pop('address')
        if not reg_agent_db:
            logger.info(f'Creating reg agent: {reg_agent}')
            reg_agent_db = await db_api.create_reg_agent(reg_agent)
        else:
            if json_utils.is_json_db_different(reg_agent_db.to_dict(), reg_agent):
                update_dict = {k: v for k, v in reg_agent.items() if v is not None}
                update_dict.pop('id', None)
                if update_dict:
                    logger.info(f'Updating reg agent: {update_dict}')
                    reg_agent_db = await db_api.update_reg_agent(reg_agent_db, reg_agent)

        n[reg_agent_id_key] = reg_agent_db.id
        n[reg_agent_key]['id'] = reg_agent_db.id

    return new


@base.session_aware()
async def update_client_registrations(client: models.Client, old: list, new: list, session=None):
    """
    Update client registrations (3 layers: registration, reg_agent and address)
    """
    inner_id_key = 'registered_agent_id'
    inner_obj_key = 'registered_agent'

    # Make sure all registrations are not primary
    for n in new:
        n['is_primary'] = False

    new = await review_create_reg_agents(new, reg_agent_key=inner_obj_key, reg_agent_id_key=inner_id_key)

    await update_client_collection_with_inner_obj(
        client, old, new,
        db_model=models.ClientRegistration,
        delete_func=db_api.delete_client_registrations,
        create_func=db_api.create_client_registration,
        update_func=db_api.update_client_registration,
        inner_id_key=inner_id_key,
        inner_obj_key=inner_obj_key
    )


async def update_client_services(client: models.Client, old: list, new: list):
    """
    Update client services (special case: client_service and service)
    """
    inner_id_key = 'service_id'
    inner_obj_key = 'service'

    await update_client_collection_with_inner_obj(
        client, old, new,
        db_model=models.ClientService,
        delete_func=db_api.delete_client_services,
        create_func=db_api.create_client_service,
        update_func=db_api.update_client_service,
        inner_id_key=inner_id_key,
        inner_obj_key=inner_obj_key
    )


async def update_client_llc_shareholders(client: models.Client, old: list, new: list):
    """
    Update client shareholders (2 layers: client_shareholder and person)
    """
    inner_id_key = 'client_person_id'
    inner_obj_key = 'person'

    new = await review_create_client_persons(new, person_key=inner_obj_key, person_id_key=inner_id_key)

    await update_client_collection_with_inner_obj(
        client, old, new,
        db_model=models.ClientLLCShareholder,
        delete_func=db_api.delete_client_shareholders,
        create_func=db_api.create_client_shareholder,
        update_func=db_api.update_client_shareholder,
        inner_id_key=inner_id_key,
        inner_obj_key=inner_obj_key
    )


async def update_client_capitalizations(client: models.Client, old: list, new: list):
    """
    Update client capitalizations (3 layers: client_capitalization, person, and share)
    """
    inner_id_key = 'person_id'
    inner_obj_key = 'person'

    new = await review_create_client_persons(new, person_key=inner_obj_key, person_id_key=inner_id_key)

    await update_client_collection_with_inner_obj(
        client, old, new,
        db_model=models.ClientCapitalization,
        delete_func=db_api.delete_client_capitalizations,
        create_func=db_api.create_client_capitalization,
        update_func=db_api.update_client_capitalization,
        inner_id_key=inner_id_key,
        inner_obj_key=inner_obj_key
    )


async def update_tax_reports(client: models.Client, old: list, new: list):
    """
    Update client tax reports (1 layer: tax_reports)
    """
    inner_id_key = None
    inner_obj_key = None

    # Validate new data and keep it only if we receive the last file as "Tax Report"
    new_no_ids = [n for n in new if not n.get('id')]
    last_updated_client_file = await db_api.get_last_updated_client_file(client.id)
    if last_updated_client_file and last_updated_client_file.doc_type != 'Tax Report':
        return

    await update_client_collection_with_inner_obj(
        client, old, new,
        db_model=models.TaxReport,
        delete_func=db_api.delete_client_tax_reports,
        create_func=db_api.create_client_tax_report,
        update_func=db_api.update_client_tax_report,
        inner_id_key=inner_id_key,
        inner_obj_key=inner_obj_key
    )


async def noop(client: models.Client, old: list, new: list):
    pass


async def _process_manager_data(manager_data):
    """Process manager data and return the manager ID."""
    if not manager_data:
        return None

    manager_id = manager_data.get('id')
    if manager_id:
        db_manager = await db_api.get_manager_by_id(manager_id)
    else:
        db_manager = await db_api.get_manager_by(email=manager_data.get('email'))

    return db_manager.id if db_manager else None


async def _process_source_data(source_data):
    """Process source data and return the source ID."""
    if not source_data:
        return None

    source_id = source_data.get('id')
    if source_id:
        db_source = await db_api.get_source_by(id=source_id)
    else:
        db_source = await db_api.get_source_by(title=source_data.get('title'))
        if not db_source and source_data.get('title'):
            db_source = await db_api.create_source({'title': source_data['title']})

    return db_source.id if db_source else None


async def _process_primary_registration_data(client_id, primary_registration_data):
    """Process primary registration data and update the client."""
    if not primary_registration_data:
        return db_api.default_registration()

    primary_registration_data['client_id'] = client_id
    primary_registration_data['is_primary'] = True
    current_primary = await db_api.get_primary_client_registration(client_id=client_id)

    if current_primary:
        primary_registration_data['registered_agent_id'] = (
            (primary_registration_data.get('registered_agent') or {}).get('id') or
            primary_registration_data.get('registered_agent_id')
        )
        primary_registration_data.pop('registered_agent', None)
        current_primary_dict = current_primary.to_dict()
        current_primary_dict.pop('registered_agent', None)
        if not json_utils.is_json_db_different(current_primary_dict, primary_registration_data):
            return current_primary
        primary_registration_data.pop('id', None)
        logger.info(f'Updating primary registration: {primary_registration_data}')
        updated_primary = await db_api.update_client_registration(current_primary, primary_registration_data)
        return updated_primary
    else:
        primary_registration_data.pop('id', None)
        primary_registration_data['registered_agent_id'] = (
            (primary_registration_data.get('registered_agent') or {}).get('id') or
            primary_registration_data.get('registered_agent_id')
        )
        primary_registration_data.pop('registered_agent', None)
        logger.info(f'Creating primary registration: {primary_registration_data}')
        return await db_api.create_client_registration(primary_registration_data)


def _preprocess_client_data_for_db(client_dict: dict) -> dict:
    # Format datetime
    for key, value in client_dict.items():
        if isinstance(value, datetime.datetime):
            client_dict[key] = base.datetime_to_string(value)
        elif isinstance(value, datetime.date):
            client_dict[key] = base.date_to_string(value)
        elif isinstance(value, dict):
            client_dict[key] = _preprocess_client_data_for_db(value)
        elif isinstance(value, list):
            client_dict[key] = [_preprocess_client_data_for_db(item) for item in value]
    return client_dict


async def _process_internal_data_source(client_id: str, old_data, new_data, internal_data_source):
    """Process internal data source and update the history."""
    post_process_actions = []
    if not internal_data_source:
        return post_process_actions

    manager: models.Manager = ctx.current_manager()

    old_data_copy = _preprocess_client_data_for_db(old_data.copy())
    new_data_copy = _preprocess_client_data_for_db(new_data.copy())
    await db_api.create_change_record({
        'client_id': client_id,
        'manager_id': manager.id,
        'type': internal_data_source.get('type'),
        'object_id': str(internal_data_source.get('id')),
        'old_state': old_data_copy,
        'new_state': new_data_copy,
    })
    if internal_data_source.get('type') == models.ChangeRecordType.PENDING_CHANGE:
        await pending_changes.mark_as_applied(client_id, internal_data_source['id'])
    if internal_data_source.get('type') == models.ChangeRecordType.CLIENT_UPLOAD:
        upload_id = int(internal_data_source['id'])
        # Apply upload:
        # 1. Re-attach file to the client
        # 2. Send file to IRA for indexing (file_processing.process_file)
        # 3. Delete client upload, detection_item etc., all we do when deleting a client upload except a file_db
        # 4. Delete file physically from here (SharedConfig().file_manager.delete_file)
        upload_db = await db_api.get_client_upload_by_id(upload_id)
        apply_func = await apply_upload_for_file_process(upload_db, client_id, new_data['name'])
        post_process_actions.append(apply_func)

    return post_process_actions


async def apply_upload_for_file_process(upload_db: models.ClientUpload, client_id: str, client_name: str) -> callable:
    file_db = await db_api.get_client_file_by_id(upload_db.file_id)
    file_db = await db_api.update_client_file(file_db, {'client_id': client_id})
    file_path = detections.get_upload_file_path(file_db)

    read_file = await SharedConfig().file_manager.read_file(file_path)
    file_data = await SharedConfig().file_manager.get_data(read_file)

    await file_processing.process_file(
        models.Client(id=client_id, name=client_name), file_db.name, file_data, file_db=file_db
    )

    async def post_process():
        client_data = {'name': client_name, 'id': client_id}
        change = await pending_changes.get_pending_change_by_file(client_data, file_db)
        if change:
            await pending_changes.mark_as_applied(client_id, change['id'])
        await db_api.update_client_upload(
            upload_db, {'status': models.ClientUploadStatus.APPLIED}
        )
        # Update attached objects
        await db_api.delete_upload_attached_objects_by_object(UploadObjectType.CLIENT, client_id)
        await db_api.create_client_upload_attached_object({
            'upload_id': upload_db.id,
            'object_type': UploadObjectType.CLIENT,
            'object_id': client_id
        })


    return post_process


async def _process_adjacent_data(client_id, adjacent_data, old_client_dict, update_client_dict):
    """Process all adjacent data and update the client dictionary."""
    # Process manager data
    post_process_actions = []
    manager_id = await _process_manager_data(adjacent_data.get('manager'))
    if manager_id is not None:
        update_client_dict['manager_id'] = manager_id

    # Process source data
    source_id = await _process_source_data(adjacent_data.get('source'))
    if source_id is not None:
        update_client_dict['source_id'] = source_id

    # Process primary registration data
    primary_registration = adjacent_data.get('primary_registration')
    if primary_registration:
        await _process_primary_registration_data(
            client_id, primary_registration
        )

    # Process internal data source
    internal_data_source = adjacent_data.get('internal_data_source', default_data_source.copy())
    if internal_data_source:
        new_post_process_actions = await _process_internal_data_source(
            client_id, old_client_dict, update_client_dict, internal_data_source
        )
        post_process_actions.extend(new_post_process_actions)

    return update_client_dict, post_process_actions


# No-op by default
update_data_funcs = defaultdict(lambda: noop, {
    'addresses': update_client_addresses,
    # 'authorized_signers': update_client_authorized_signers,
    'bank_accounts': update_client_bank_accounts,
    'contacts': update_client_contacts,
    # 'files': update_client_files,
    'payment_cards': update_client_payment_cards,
    'payment_services': update_client_payment_services,
    'secondary_registrations': update_client_registrations,
    'services': update_client_services,
    'shares': update_client_shares,
    'llc_shareholders': update_client_llc_shareholders,
    'tasks': update_client_tasks,
    'tax_reports': update_tax_reports,
    'capitalization_table': update_client_capitalizations,
})


async def create_client_data(client_dict: dict) -> dict:
    async with base.session_context():
        # Create client first to get a client id
        db_client = await db_api.create_client({'name': client_dict['name']})
        try:
            created_client_dict = await update_client_data(db_client.id, client_dict)
        except Exception as e:
            logger.error(f'Error creating client: {e}')
            logger.error(f'Cleanup client: {db_client.id}')
            try:
                await file_processing.delete_resources(db_client)
            except Exception as e2:
                logger.error(f'Error deleting client resources: {e2}')
            raise e

    return created_client_dict


async def update_client_data(client_id: str, update_client_dict: dict):
    list_data = {}
    adjacent_data = {
        'manager': None,
        'source': None,
        'primary_registration': None,
        'internal_data_source': None,
    }
    # Create a copy of the keys to avoid dictionary changed size during iteration
    original_update = copy.deepcopy(update_client_dict)

    def exclude_unnecessary_keys(data_dict):
        keys_to_process = list(data_dict.keys())
        for key in keys_to_process:
            value = data_dict[key]
            if isinstance(value, list):
                list_data[key] = value
                data_dict.pop(key)
            elif key in adjacent_data:
                adjacent_data[key] = value
                data_dict.pop(key)
        return data_dict, list_data, adjacent_data

    _, _, adjacent_data = exclude_unnecessary_keys(update_client_dict)

    async with base.session_context():
        db_client, client_data = await get_client_data(client_id)
        # Process adjacent data
        update_client_dict, post_process_actions = await _process_adjacent_data(client_id, adjacent_data, client_data, original_update)
        update_client_dict, list_data, _ = exclude_unnecessary_keys(update_client_dict)

        if update_client_dict:
            # Update base client data
            db_client = await db_api.update_client(db_client, update_client_dict)
        # Update list data
        for key, value in list_data.items():
            await update_data_funcs[key](
                db_client, old=client_data.get(key, []), new=value
            )
            client_data[key] = value

        # Get data again
        db_client, new_client_dict = await get_client_data(client_id)

    for action in post_process_actions:
        await action()

    return new_client_dict


async def approve_client_data(client_id: str, approved_by: str, note: str = None):
    async with base.session_context():
        db_client = await db_api.get_client_by_id(client_id)
        await db_api.update_client(db_client, {'internal_draft_flag': False})

        _, client_data = await get_client_data(client_id)

        # Before return client_data, need to send it to IRA
        ira_client = SharedConfig().ira_client
        approved_tl = await TimelineResource.ensure_created(db_client, ira_client, type_=models.TimelineType.APPROVED_DATA)
        main_files_tl = await TimelineResource.ensure_created(db_client, ira_client, type_=models.TimelineType.CLIENT_DATA)

        # Add approved data to timeline
        await _add_approved_data_to_timeline(
            ira_client,
            approved_tl,
            client_id,
            client_data,
            approved_by,
            note
        )
        await _add_approved_data_to_timeline(
            ira_client,
            main_files_tl,
            client_id,
            client_data,
            approved_by,
            note
        )

        return client_data


async def _add_approved_data_to_timeline(
    ira_client,
    target_timeline: TimelineResource,
    client_id: str,
    client_data: dict,
    approved_by: str,
    note: str = None
):
    # Get or create point by date
    today = datetime.date.today()
    target_point = await ira_client.apoint_get_by_date(
        target_timeline.external_id,
        date=str(today)
    )
    desc = f'Approved by {approved_by}{"\n/ " + note if note else ""}'
    if not target_point:
        target_point = await ira_client.apoint_create(
            target_timeline.external_id,
            date=str(today),
            title=f'{note or f"Approved data by {approved_by}"}',
            description=desc
        )

    # Remove files from client data
    client_data.pop('files', None)
    dummy = generate_dummy_client_data()
    resolved_client_data = await json_utils.amerge_jsons([dummy, client_data])

    await ira_client.apoint_add_change(
        target_timeline.external_id,
        target_point['id'],
        resolved_client_data,
        description=desc
    )


async def client_uploads_find_by_eins(client_uploads: List[dict], enrich_client_data: bool = False):
    detected_eins = set()
    for upload in client_uploads:
        tmp_ein = utils.clean_ein((upload.get('output') or {}).get('ein'))
        if tmp_ein:
            detected_eins.add(tmp_ein)
    detected_eins = list(detected_eins)
    if not detected_eins:
        # No EINs detected, skip
        # Add empty client, client_output fields
        for upload in client_uploads:
            upload['client'] = None
            upload['client_output'] = None
            upload['exists'] = False
        return client_uploads

    clients = await db_api.list_clients_by_eins(detected_eins)
    if not clients:
        # No clients found, skip
        # Add empty client, client_output fields
        for upload in client_uploads:
            upload['client'] = None
            upload['client_output'] = None
            upload['exists'] = False
        return client_uploads

    # map a client_upload ein to a client
    client_map_by_ein = {utils.clean_ein(c.ein): c.to_dict() for c in clients}
    if enrich_client_data:
        for ein, client in client_map_by_ein.items():
            _, client_data = await get_client_data(client['id'])
            client_map_by_ein[ein] = client_data

    return client_map_by_ein


async def merge_client_uploads_with_clients(client_uploads: List[dict]):
    """
    Legacy function - now delegates to the simplified client upload service.
    This maintains backward compatibility while using the new simplified logic.
    """
    from records.services.client_upload_service import get_client_upload_service
    return await get_client_upload_service().merge_uploads_with_clients(client_uploads)


def generate_dummy_client_data():
    example_structure = json_utils.generate_dummy_json(schemas.global_client_schema)
    for key, value in example_structure.items():
        if isinstance(value, list):
            example_structure[key] = []
    return example_structure
