"""
Constants used across the services module.
"""
import inspect


class UploadObjectType:
    CLIENT = 'client'
    CLIENT_CONNECTION = 'client_connection'
    TAX_REPORT = 'tax_report'

    @classmethod
    def is_valid(cls, status):
        return status in cls.__dict__.values()

    @classmethod
    def all(cls):
        items = []
        for k in dir(cls):
            if k.startswith('__'):
                continue

            v = getattr(cls, k)
            if not inspect.ismethod(v) and not inspect.isfunction(v):
                items.append(v)

        return items
