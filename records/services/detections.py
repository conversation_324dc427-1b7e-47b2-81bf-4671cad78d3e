import json
import logging
import os

from records.config.shared_config import SharedConfig
from records.db import api as db_api
from records.db import models
from records.services import schemas

EXTRACTOR_PROMPT = """
Rules:
- If a phone is not available or not known or supplied as "0", leave it null.
- status in root should be null
- name in root should be the full name of the company (no CAPS)
- address_type is one of: ["Mailing Address", "Foreign Address", "Tax return Address", "Legal Address", "Physical Address"]
- business_model is one of: ["Mobile App", "SaaS", "E-Commerce", "S/W outsourcing", "Custom S/W development", "Hardware", "Other"]
- legal_ent_type is one of: ["LLC", "S-CORP", "C-CORP", "SMLLC", "LP"]
- position (contact / shareholder) is one of: ["Main contact", "Contact person", "Corp. Officer", "Director", "Foreign Shareholder", "Manager", "Member", "Secretary", "Shareholder", "CEO", "Other officer"]
 - enumerate all mentioned directors/officers as contacts
- financial year end is one of: ["Jan.", "Feb.", "Mar.", "Apr.", "May.", "June.", "July.", "Aug.", "Sept.", "Oct.", "Nov.", "Dec."]
- share percentage is a number from 0 to 100
- shares.type is one of ["Common", "Preferred"], "Common" if not specified
- state should be correct abbreviation of the state if USA, example "DE" for Delaware, "WY" for Wyoming etc.
- for tax report, type is one of ["Federal Income Tax", "State Income Tax", "FTB"]
- for tax report, form_info is should be in form "Form <form number>" (for Federal Income Tax or FTB) or "Tax Income Form: <state>" for State Income Tax
- for tax report, filed_by is one of: ["Client DIY", "ISC"]
- for tax report, status is "COMPLETED"
- if document is not tax report, do not generate tax_reports section

Work with addresses:
- Compile the full_address in form [street], [city], [state] [zip], [country]. For example: "365 Main St, Anytown CA 90210, USA"
- For addresses, no extra spaces and punctuation, Capitalize first letter of each word in street names and cities"""


COLD_START_PROMPT = """
Extract company's (client) data according to the suppliant schema.

The output should be formatted as a JSON instance that conforms to the JSON schema below.
If a value can not be determined, then put there null.
Do not fill in the placeholders where specific data is not available.

EXTRACTOR_PROMPT_PLACEHOLDER

As an example, for the schema 
{
  "properties": {
    "foo": {
      "title": "Foo",
      "description": "a list of strings",
      "type": "array",
      "items": {
        "type": "string"
      }
    },
    "bar": {
      "title": "Bar",
      "description": "a string",
      "type": ["string", "null"]
    }
  },
  "required": [
    "foo"
  ]
}
the object 
{"foo": ["ber", "baz"], "bar": null} 
is a well-formatted instance of the schema. The object 
{"properties": {"foo": ["bar", "baz"], "bar": 123}} 
is not well-formatted.

Here is the output schema:
```
GLOBAL_CLIENT_SCHEMA_PLACEHOLDER
```"""

METADATA_EXTRACTION_PROMPT = """
Extract comprehensive metadata from the document.
Fields to extract:
- type: Category or type of the document.
- name: Full title or name of the document.
- date: Document date, which can be a signature, seal, or issue date (format: YYYY-MM-DD, e.g., 2023-10-25) or null if not available.
- summary: A brief summary highlighting the document's key points.
- keywords: A list of relevant keywords.
- author: Name of the document's author, if available.

Guidelines for document type:
- If the document is a tax report, type should be "Tax Report" (exactly), then date should be the date of the report (presumably in the next year of the fiscal year; if there is no such date in the document, set it to null)
- If the document is a questionnaire, type should be "Tax Questionnaire"
- If the document is a 1099 form, type should be "1099 Form"
- If the document is a CP575, type should be "CP575 Letter"
- If the document is a By Laws, type should be "By Laws"
- If the document is a person's Passport, type should be "Passport"
- If the document is a incorporation certificate, type should be "Certificate of Incorporation"
- If the document is not any of the above, write the actual type as written in the document.

Output must be in valid JSON format enclosed within triple backticks:
```
{
  "type": "document type",
  "name": "document name",
  "date": "YYYY-MM-DD",
  "summary": "brief summary",
  "keywords": ["keyword1", "keyword2"],
  "author": "author name"
}
```

This is the document:
"""

TAX_REPORT_PROMPT = """
You are a tax report data extraction AI. Extract tax report information from the provided document.

Extract the following information and return it in JSON format:
- type: Type of tax report (Federal Income Tax or State Income Tax or FTB)
- form_info: Form information (e.g., "Form 1120", "Form 1065", "Tax Income Form: CA")
- fiscal_year: The fiscal year for this tax report
- filed_by: Who filed the report ("Client DIY" or "ISC")
- filed_date: Date the report was filed (YYYY-MM-DD format)
- due_date: Due date for the report (YYYY-MM-DD format)
- status: Status of the report (should be "COMPLETED" for filed reports)

In addition, extract the following information from the document and return it in JSON format:
- ein: Employer Identification Number (EIN) of the company
- name: Name of the company

Important: Fill in only 1 tax report item.

Output must be in valid JSON format enclosed within triple backticks:
```
{
  "ein": "12-3456789",
  "name": "Test Company",
  "tax_reports": [
    {
      "type": "Federal Income Tax",
      "form_info": "Form 1120",
      "fiscal_year": "2023",
      "filed_by": "ISC",
      "filed_date": "2024-03-15",
      "due_date": "2024-03-15",
      "status": "COMPLETED"
    }
  ]
}
```

This is the tax report document:
"""

logger = logging.getLogger(__name__)


async def list_detection_items(detection_id: str, item_ids=None):
    if not detection_id:
        logger.warning('No detection id provided')
        return []
    ira_client = SharedConfig().ira_client
    det_items = await ira_client.adetection_item_list(detection_id, limit=5000, page=1, item_ids=item_ids)
    return det_items['items']


async def get_detection_item(detection_id: int, item_id: int):
    ira_client = SharedConfig().ira_client
    return await ira_client.adetection_item_get(detection_id, item_id)


async def delete_detection_item(detection_id: int, item_id: int):
    ira_client = SharedConfig().ira_client
    try:
        await ira_client.adetection_item_delete(detection_id, item_id)
    except ValueError as e:
        if 'not found' in str(e).lower():
            pass
        else:
            raise e


async def ensure_client_init_detections():
    ira_client = SharedConfig().ira_client
    # Client init detection
    detection_client_init_db = await db_api.get_external_id_by(
        internal_type=models.DetectionType.CLIENT_DATA,
        external_app_type='detection',
        notfoundok=True
    )

    if not detection_client_init_db:
        detection_ira = await ira_client.adetection_create(
            title=models.DetectionName.CLIENT_DATA,
            type='prompt',
            description='Detection for client data initialization',
            config=get_client_upload_detection_config()
        )
        detection_client_init_db = await db_api.create_external_id({
            'internal_type': models.DetectionType.CLIENT_DATA,
            'external_app_type': 'detection',
            'external_app_id': str(detection_ira['id']),
        })

    # Smart merge detection
    detection_merge_db = await db_api.get_external_id_by(
        internal_type=models.DetectionType.SMART_MERGE,
        external_app_type='detection',
        notfoundok=True
    )
    if not detection_merge_db:
        detection_ira = await ira_client.adetection_create(
            title=models.DetectionName.SMART_MERGE,
            type='smart_merge',
            description='Detection for client data smart merge',
            config=get_client_upload_detection_config()
        )
        detection_merge_db = await db_api.create_external_id({
            'internal_type': models.DetectionType.SMART_MERGE,
            'external_app_type': 'detection',
            'external_app_id': str(detection_ira['id']),
        })

    # Metadata detection
    detection_metadata_db = await db_api.get_external_id_by(
        internal_type=models.DetectionType.METADATA_EXTRACTION,
        external_app_type='detection',
        notfoundok=True
    )
    if not detection_metadata_db:
        detection_ira = await ira_client.adetection_create(
            title=models.DetectionName.METADATA_EXTRACTION,
            type='prompt',
            description='Detection for metadata extraction',
            config=get_metadata_extraction_detection_config()
        )
        detection_metadata_db = await db_api.create_external_id({
            'internal_type': models.DetectionType.METADATA_EXTRACTION,
            'external_app_type': 'detection',
            'external_app_id': str(detection_ira['id']),
        })

    # Tax report detection
    detection_tax_report_db = await db_api.get_external_id_by(
        internal_type=models.DetectionType.TAX_REPORT_DETECTION,
        external_app_type='detection',
        notfoundok=True
    )
    if not detection_tax_report_db:
        detection_ira = await ira_client.adetection_create(
            title=models.DetectionName.TAX_REPORT_DETECTION,
            type='prompt',
            description='Detection for tax report data extraction',
            config=get_tax_report_detection_config()
        )
        detection_tax_report_db = await db_api.create_external_id({
            'internal_type': models.DetectionType.TAX_REPORT_DETECTION,
            'external_app_type': 'detection',
            'external_app_id': str(detection_ira['id']),
        })

    return {
        'client_init_detection': detection_client_init_db.external_app_id,
        'smart_merge_detection': detection_merge_db.external_app_id,
        'metadata_detection': detection_metadata_db.external_app_id,
        'tax_report_detection': detection_tax_report_db.external_app_id,
    }


def get_upload_file_path(file_db: models.File):
    name, ext = os.path.splitext(file_db.name)
    return f'client_uploads/{file_db.name}/{file_db.id}{ext}'


def get_client_upload_detection_config():
    return {
        'prompts': {
            'system_prompt': get_client_data_prompt()
        }
    }


def get_client_smart_merge_detection_config():
    return {
        'prompts': {
            'system_prompt': None
        },
        "model_name": "gpt-4o-mini",
        "vision_model": "gpt-4o-mini",
        "temperature": 0.5,
    }


def get_metadata_extraction_detection_config():
    return {
        'prompts': {
            'system_prompt': METADATA_EXTRACTION_PROMPT
        }
    }


def get_tax_report_detection_config():
    return {
        'prompts': {
            'system_prompt': get_tax_report_detection_prompt()
        }
    }


def get_tax_report_detection_prompt():
    return TAX_REPORT_PROMPT


def get_client_data_prompt():
    # TODO provide catalogs (address_type etc) and fix placeholders for them
    return COLD_START_PROMPT.replace(
        'GLOBAL_CLIENT_SCHEMA_PLACEHOLDER', json.dumps(schemas.global_client_schema, indent=2)
    ).replace(
        'EXTRACTOR_PROMPT_PLACEHOLDER', EXTRACTOR_PROMPT
    )
