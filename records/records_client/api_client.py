import asyncio
import functools
import logging
import os
import time

import requests
import retry

logger = logging.getLogger(__name__)


class APIClient:
    def __init__(self, session_token: str, base_url: str, verbose=False):
        self.base_url = base_url
        self.key = session_token
        self.headers = {'X-Session': self.key}
        self.session = WrappedSession(verbose=verbose)
        self.session.headers = self.headers

    def __getattribute__(self, item):
        if item.startswith('a') and hasattr(self, item[1:]):
            return functools.partial(self.as_async, getattr(self, item[1:]))
        return super().__getattribute__(item)

    def _url(self, u):
        return os.path.join(self.base_url, 'api/v1', u.lstrip("/"))

    @staticmethod
    async def as_async(func, *args, **kwargs):
        loop = asyncio.get_event_loop()
        if kwargs:
            func = functools.partial(func, **kwargs)
        return await loop.run_in_executor(None, func, *args)

    def auth_info(self):
        resp = self.session.get(self._url('/auth/info'))
        return resp.json()

    def client_list(self, q: str = None, limit: int = 25, page: int = 1):
        url = self._url('/clients')
        params = []
        if q:
            params.append(f'q={q}')
        if limit:
            params.append(f'limit={limit}')
        if page:
            params.append(f'page={page}')

        if params:
            url = f'{url}?{"&".join(params)}'

        resp = self.session.get(url)
        return resp.json()

    def client_get(self, client_id):
        resp = self.session.get(self._url(f'/clients/{client_id}'))
        return resp.json()

    def file_list(self):
        resp = self.session.get(self._url('/files'))
        return resp.json()

    def file_get(self, client_id, file_id):
        resp = self.session.get(self._url(f'/clients/{client_id}/files/{file_id}'))
        return resp.json()

    def file_delete(self, client_id, file_id):
        resp = self.session.delete(self._url(f'/clients/{client_id}/files/{file_id}'))
        return resp

    def file_upload(self, client_id, file_name, file_data, doc_type=None, raw=False):
        data = {}
        if doc_type:
            data['doc_type'] = doc_type
        resp = self.session.post(
            self._url(f'/clients/{client_id}/files'),
            files={'file': (file_name, file_data)},
            data=data,
            raw=raw,
        )
        if raw:
            return resp
        return resp.json()

    def file_update(self, client_id, file_id, file_name, file_data):
        resp = self.session.put(
            self._url(f'/clients/{client_id}/files/{file_id}'),
            files={'file': (file_name, file_data)}
        )
        return resp.json()

    def extractor_list_files(self, extractor_id):
        resp = self.session.get(self._url(f'/extractors/{extractor_id}/files'))
        return resp.json()

    def extractor_get_file(self, extractor_id, file_id):
        resp = self.session.get(self._url(f'/extractors/{extractor_id}/files/{file_id}'))
        return resp.json()

    def extractor_get_file_by_hash(self, extractor_id, hash):
        resp = self.session.get(self._url(f'/extractors/{extractor_id}/files/by-hash/{hash}'))
        return resp.json()

    def extractor_get_file_contents(self, extractor_id, file_id):
        resp = self.session.get(self._url(f'/extractors/{extractor_id}/files/{file_id}/contents'))
        return resp.json()

    def extractor_delete_file(self, extractor_id, file_id):
        resp = self.session.delete(self._url(f'/extractors/{extractor_id}/files/{file_id}'))
        return resp

    def extractor_reindex_file(self, extractor_id, file_id):
        resp = self.session.put(self._url(f'/extractors/{extractor_id}/files/{file_id}/reindex'))
        return resp.json()

    def extractor_download_file(self, extractor_id, file_id):
        resp = self.session.get(self._url(f'/extractors/{extractor_id}/files/{file_id}/download'))
        return resp

    def extractor_search(self, extractor_id, query, limit: int = 10):
        params = [f'query={query}']
        if limit:
            params.append(f'limit={limit}')

        url = self._url(f'/extractors/{extractor_id}/search')
        if params:
            url = f'{url}?{"&".join(params)}'

        resp = self.session.get(url)

        return resp.json()

    def timeline_create(self, title, data_schema, description=None):
        data = {
            'title': title,
            'data_schema': data_schema,
            'description': description,
        }
        resp = self.session.post(self._url(f'/timelines'), json=data)
        return resp.json()

    def timeline_get(self, id: int):
        resp = self.session.get(self._url(f'/timelines/{id}'))
        return resp.json()

    def timeline_get_file(self, timeline_id, file_id):
        resp = self.session.get(self._url(f'/timelines/{timeline_id}/files/{file_id}'))
        return resp.json()

    def timeline_get_file_download(self, timeline_id, file_id):
        resp = self.session.get(self._url(f'/timelines/{timeline_id}/files/{file_id}/download'))
        # Allow chunking
        return resp

    def timeline_file_upload(self, timeline_id, point_id, file_name, file_data):
        resp = self.session.post(
            self._url(f'/timelines/{timeline_id}/points/{point_id}/files'),
            files={'file': (file_name, file_data)}
        )
        return resp.json()

    def timeline_file_replace(self, timeline_id, point_id, file_id, file_name, file_data):
        resp = self.session.put(
            self._url(f'/timelines/{timeline_id}/points/{point_id}/files/{file_id}'),
            files={'file': (file_name, file_data)}
        )
        return resp.json()

    def timeline_file_delete(self, timeline_id, point_id, file_id):
        resp = self.session.delete(self._url(f'/timelines/{timeline_id}/points/{point_id}/files/{file_id}'))
        return resp

    def timeline_get_historical_result(self, id: int):
        resp = self.session.get(self._url(f'/timelines/{id}/historical_result'))
        return resp.json()

    def timeline_delete(self, id: int):
        resp = self.session.delete(self._url(f'/timelines/{id}'))
        return resp

    def timeline_get_file_by_hash(self, timeline_id, hash):
        resp = self.session.get(self._url(f'/timelines/{timeline_id}/files/check_hash?hash={hash}'))
        return resp.json()

    def point_create(self, timeline_id: int, title: str, date: str, description: str = None):
        data = {
            'title': title,
            'date': date,
            'description': description,
        }
        resp = self.session.post(self._url(f'/timelines/{timeline_id}/points'), json=data)
        return resp.json()

    def point_get(self, timeline_id: int, point_id: int):
        resp = self.session.get(self._url(f'/timelines/{timeline_id}/points/{point_id}'))
        return resp.json()

    def point_get_by_date(self, timeline_id: int, date: str):
        resp = self.session.get(self._url(f'/timelines/{timeline_id}/points/by-date/{date}'))
        return resp.json()

    def point_add_change(self, timeline_id: int, point_id: int, data: dict, description: str = None):
        data = {
            'description': description,
            'data': data,
        }
        resp = self.session.post(self._url(f'/timelines/{timeline_id}/points/{point_id}/changes'), json=data)
        return resp.json()

    def timeline_process(self, timeline_id: int):
        resp = self.session.post(self._url(f'/timelines/{timeline_id}/process'))
        return resp.json()

    def point_wait_process(self, timeline_id: int, point_id: int, timeout: int = 120):
        point = self.point_get(timeline_id, point_id)
        time.sleep(1)
        t = time.time()
        while point['status']['status'] in ['PREPROCESSING', 'PROCESSING']:
            time.sleep(1)
            point = self.point_get(timeline_id, point_id)
            if time.time() - t > timeout:
                raise TimeoutError('Timeout exceeded while waiting for point processing')

        return point

    def chat_list(self):
        resp = self.session.get(self._url('/chats'))
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def chat_create(self, title, config=None):
        resp = self.session.post(self._url('/chats'), json={'title': title, 'config': config})
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        chat = resp.json()
        return chat

    def message_create(self, chat_id, content):
        resp = self.session.post(self._url(f'/chats/{chat_id}/messages'), json={'content': content})
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())
        message = resp.json()
        return message

    def message_get(self, chat_id, message_id):
        url = f'/chats/{chat_id}/messages/{message_id}'
        resp = self.session.get(self._url(url))
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())
        message = resp.json()
        return message

    def message_wait_for_completion(self, message, result_id=None, timeout=180):
        completed = False
        start = time.time()
        while not completed:
            message = self.message_get(message['chat_id'], message['id'])
            if result_id:
                res = [r for r in message['results'] if r['id'] == result_id][0]
            else:
                res = message
            status = res["status"]
            completed = status in {'SUCCESS', 'ERROR', 'ERROR_REASON'}
            if time.time() - start > timeout:
                raise TimeoutError(f"Timed out waiting for the message id={message['id']}")
            time.sleep(1)
        return message

    def message_list(self, chat_id):
        url = f'/chats/{chat_id}/messages'
        resp = self.session.get(self._url(url))
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def chat_delete(self, message):
        url = f'/chats/{message["chat_id"]}'
        resp = self.session.delete(self._url(url))
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

    def detection_item_list(self, detection_id: int, limit: int = 50, page=1):
        params = []
        if limit:
            params.append(f'limit={limit}')
        if page:
            params.append(f'page={page}')

        url = self._url(f'/detections/{detection_id}/items')
        if params:
            url = f'{url}?{"&".join(params)}'

        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def detection_file_download(self, detection_file_id) -> bytes:
        url = self._url(f'/detection_files/{detection_file_id}')
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.content

    def detection_file_get_meta(self, detection_file_id):
        url = self._url(f'/detection_files/{detection_file_id}/meta')
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def detection_file_delete(self, detection_file_id):
        url = self._url(f'/detection_files/{detection_file_id}')
        resp = self.session.delete(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp

    def dataset_file_upload(self, dataset_id, fp, name, wait: bool = True):
        url = self._url(f'/datasets/{dataset_id}/files')
        params = []
        if wait:
            params.append('wait=true')

        if params:
            url = f'{url}?{"&".join(params)}'

        resp = self.session.post(url, files={'file': (name, fp)}, timeout=300)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())
        res = resp.json()
        return res

    def dataset_file_get(self, dataset_id, file_id):
        url = self._url(f'/datasets/{dataset_id}/files/{file_id}')
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.json()

    def dataset_file_download(self, dataset_id, file_id) -> bytes:
        url = self._url(f'/datasets/{dataset_id}/files/{file_id}/download')
        resp = self.session.get(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp.content

    def dataset_file_delete(self, dataset_id, file_id):
        url = self._url(f'/datasets/{dataset_id}/files/{file_id}')
        resp = self.session.delete(url)
        if resp.status_code >= 400:
            raise ValueError(resp.content.decode())

        return resp


class ServerError(Exception):
    pass


class WrappedSession(requests.Session):
    def __init__(self, verbose=False):
        super().__init__()
        self.verbose = verbose

    @retry.retry(exceptions=(ServerError, requests.exceptions.ConnectionError), tries=10, delay=1, backoff=1, jitter=2, logger=logger)
    def request(
        self,
        method: str | bytes,
        url: str | bytes,
        *args, **kwargs
    ):
        raw = kwargs.pop('raw', False)
        url_path = '/' + '/'.join(url.split('/')[3:])
        if self.verbose:
            logger.info(f"[RECORDS] {method} {url_path}")
        resp = super().request(method, url, *args, **kwargs)
        if self.verbose:
            logger.info(f"[RECORDS] {method} {url_path} -> {resp.status_code}")

        should_retry = resp.status_code in [502, 503, 504]
        # Retry on Bad Gateway, Service Unavailable, Gateway Timeout
        if should_retry:
            raise ServerError(f"Retry request {method} {url_path}")

        if raw:
            return resp

        raise_for_status(resp)
        return resp

    def post(self, url, data=None, json=None, **kwargs):
        return self.request("POST", url, data=data, json=json, **kwargs)


def raise_for_status(resp):
    if resp.status_code >= 400:
        raise ValueError(resp.content.decode())
