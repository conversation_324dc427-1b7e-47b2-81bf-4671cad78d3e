import abc
import typing


class FileManager:
    @abc.abstractmethod
    async def save_file(self, name, data):
        pass

    @abc.abstractmethod
    def save_file_stream(self, name, stream):
        pass

    @abc.abstractmethod
    async def read_file(self, name):
        pass

    @abc.abstractmethod
    async def get_data(self, read_file_result) -> bytes:
        pass

    async def read_and_get_data(self, name):
        read_file_result = await self.read_file(name)
        return await self.get_data(read_file_result)

    @abc.abstractmethod
    async def get_data_generator(self, read_file_result) -> typing.AsyncIterable:
        pass

    @abc.abstractmethod
    async def copy_file(self, name, new_name):
        pass

    @abc.abstractmethod
    async def delete_file(self, name):
        pass
