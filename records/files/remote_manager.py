import asyncio
import functools
import logging
import os
import typing

import requests
from fastapi import HTT<PERSON>Exception

from records.files.base import FileManager

logger = logging.getLogger(__name__)


class RemoteManager(FileManager):
    base_url = os.getenv("FILE_API_URL")
    session = requests.Session()

    async def save_file(self, name, data):
        logger.info(f"[RemoteManager] Save file {name}")
        u = os.path.join(self.base_url, 'api/v1/files')

        loop = asyncio.get_event_loop()
        post = functools.partial(self.session.post, data=data, params={'filename': name})
        resp = await loop.run_in_executor(None, post, u)
        self._raise_for_status(resp)
        resp.close()

    def save_file_stream(self, name, stream):
        u = os.path.join(self.base_url, 'api/v1/files')
        resp = self.session.post(u, data=stream, params={'filename': name})
        self._raise_for_status(resp)
        resp.close()

    async def read_file(self, name):
        logger.info(f"[RemoteManager] Read file {name}")
        u = os.path.join(self.base_url, 'api/v1/files')

        loop = asyncio.get_event_loop()
        get = functools.partial(self.session.get, params={'filename': name}, stream=True)
        resp = await loop.run_in_executor(None, get, u)

        self._raise_for_status(resp)

        # resp = self.session.get()
        return resp.iter_content(chunk_size=65536)

    async def get_data(self, read_file_result):
        data = b''
        for chunk in read_file_result:
            data += chunk

        return data

    async def get_data_generator(self, read_file_result) -> typing.AsyncIterable:
        # for chunk in read_file_result:
        #     yield chunk
        done = object()
        it = iter(read_file_result)
        while (value := await asyncio.to_thread(next, it, done)) is not done:
            yield value

    async def copy_file(self, name, new_name):
        u = os.path.join(self.base_url, 'api/v1/files/copy')

        loop = asyncio.get_event_loop()
        post = functools.partial(
            self.session.post,
            params={'filename': name, 'new_filename': new_name}
        )
        resp = await loop.run_in_executor(None, post, u)
        self._raise_for_status(resp)
        resp.close()

    async def delete_file(self, name):
        logger.info(f"[RemoteManager] Delete file {name}")
        u = os.path.join(self.base_url, 'api/v1/files')

        try:
            loop = asyncio.get_event_loop()
            delete = functools.partial(self.session.delete, params={'filename': name})
            resp = await loop.run_in_executor(None, delete, u)
            self._raise_for_status(resp)
            resp.close()
        except Exception as e:
            logger.warning(f'[RemoteManager] Delete file: {str(e)}')

    def _raise_for_status(self, resp: requests.Response):
        if resp.status_code < 400:
            return
        # {"status":404,"reason":"","msg":"msg"}
        try:
            error_data = resp.json()
        except:
            error_data = {'status': 500, 'msg': resp.content}

        raise HTTPException(error_data['status'], error_data['msg'])
