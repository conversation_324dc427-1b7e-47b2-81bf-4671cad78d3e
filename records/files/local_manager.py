import logging
import os
import shutil
import typing

import aiofiles

from records.files.base import FileManager

CHUNK_SIZE = 65536
logger = logging.getLogger(__name__)


class LocalManager(FileManager):
    data_dir = os.path.expanduser(os.getenv("DATA_DIR", "~/.records-data"))

    async def save_file(self, name, data: bytes):
        name = os.path.join(self.data_dir, name)
        logger.info(f'Save file {name}')

        dirname = os.path.dirname(name)
        os.makedirs(dirname, exist_ok=True)

        with open(name, 'wb') as f:
            f.write(data)

    def save_file_stream(self, name, stream):
        name = os.path.join(self.data_dir, name)
        dirname = os.path.dirname(name)
        os.makedirs(dirname, exist_ok=True)

        with open(name, 'wb') as f:
            f.write(stream)

    async def read_file(self, name):
        name = os.path.join(self.data_dir, name)
        logger.info(f'Read file {name}')

        f = aiofiles.open(name, 'rb')
        return f

    async def get_data(self, read_file_result):
        async with read_file_result as f:
            data = await f.read()
            return data

    async def get_data_generator(self, read_file_result) -> typing.AsyncIterable:
        async with read_file_result as f:
            while chunk := await f.read(CHUNK_SIZE):
                yield chunk

    async def copy_file(self, name, new_name):
        name = os.path.join(self.data_dir, name)
        new_name = os.path.join(self.data_dir, new_name)

        dirname = os.path.dirname(new_name)
        os.makedirs(dirname, exist_ok=True)

        logger.info(f'Copy file {name} -> {new_name}')
        shutil.copy(name, new_name)

    async def delete_file(self, name):
        name = os.path.join(self.data_dir, name)
        logger.info(f'Delete file {name}')

        try:
            os.remove(name)
        except Exception as e:
            logger.warning(f'[LocalManager] Delete file: {str(e)}')
