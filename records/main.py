import logging
import os

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.middleware.base import BaseHTTPMiddleware

from records.api import api
from records.api import handle_error

logger = logging.getLogger(__name__)


def get_app() -> FastAPI:
    fast_app = FastAPI(lifespan=api.lifespan)
    fast_app.include_router(api.get_router())

    fast_app.add_exception_handler(StarletteHTTPException, handle_error.handle_exception)
    fast_app.add_exception_handler(HTTPException, handle_error.handle_exception)
    fast_app.add_exception_handler(Exception, handle_error.handle_exception)
    fast_app.add_middleware(BaseHTTPMiddleware, dispatch=api.req_middleware)
    fast_app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    fast_app.add_middleware(BaseHTTPMiddleware, dispatch=api.time_middleware)
    # fast_app.add_event_handler('startup', api.startup)

    return fast_app


app = get_app()
if __name__ == "__main__":
    port = os.environ.get("PORT", "8084")
    workers = os.environ.get("WORKERS", "1")
    uvicorn.run(
        'records.main:app',
        host="0.0.0.0",
        port=int(port),
        access_log=False,
        workers=int(workers),
        loop='uvloop',
    )
