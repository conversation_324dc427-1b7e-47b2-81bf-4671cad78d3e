from contextvars import ContextVar
from pydantic import BaseModel


class NoneValue(BaseModel):
    pass


class ContextWrapper:
    def __init__(self, value):
        self.__value: ContextVar = value

    def set(self, value):
        return self.__value.set(value)

    def reset(self, token):
        self.__value.reset(token)
        return

    def __module__(self):
        return self.__value.get()

    @property
    def value(self):
        return self.__value.get()


def current_session():
    return session.value


def current_workspace():
    return workspace.value


def current_org():
    return org.value


def current_org_user():
    return getattr(request.value.state, 'org_user', None)


def current_permissions():
    return getattr(request.value.state, 'permissions', 0)


def current_role():
    return getattr(request.value.state, 'role_name', None)


def current_groups():
    return getattr(request.value.state, 'group_ids')


def current_user():
    return getattr(request.value.state, 'user', None)


def current_manager():
    return getattr(request.value.state, 'manager', None)


def current_org_permissions():
    if current_session().admin:
        # As Org owner
        return 1 << 1

    org_permissions = getattr(request.value.state, 'org_permissions', None)
    if org_permissions:
        return org_permissions

    user = getattr(request.value.state, 'user', None)
    org_user = getattr(request.value.state, 'org_user', None)
    org_user_role = getattr(request.value.state, 'org_user_role', None)
    if user and org_user and org_user_role:
        return org_user_role['permissions']
    else:
        return 1


org: ContextWrapper = ContextWrapper(ContextVar("org", default=None))
workspace: ContextWrapper = ContextWrapper(ContextVar("workspace", default=None))
request: ContextWrapper = ContextWrapper(ContextVar("request", default=None))
session: ContextWrapper = ContextWrapper(ContextVar("session", default=None))
db: ContextWrapper = ContextWrapper(ContextVar("db", default=None))
