import os
from typing import Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from records.context import context as ctx
from records.db import api as db_api


class ContactCreate(BaseModel):
    # Client part
    position: str
    note: Optional[str] = None
    # Person part
    firstname: str
    lastname: str
    email: Optional[str] = None
    phone: Optional[str] = None
    contact_info: Optional[str] = None
    citizenship: Optional[str] = None
    address: Optional[str] = None
    companies: Optional[str] = None


class ContactUpdate(BaseModel):
    position: Optional[str] = None
    note: Optional[str] = None
    # Person part
    firstname: Optional[str] = None
    lastname: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    contact_info: Optional[str] = None
    citizenship: Optional[str] = None
    address: Optional[str] = None
    companies: Optional[str] = None


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'contacts'))

    router.add_api_route("", list_contacts, methods=['GET'], name='List client contacts')
    router.add_api_route("", create_client_contact, methods=['POST'], name='Create client contact')
    router.add_api_route("/{contact_id}", get_client_contact, methods=['GET'], name='Get client contact')
    router.add_api_route("/{contact_id}", update_client_contact, methods=['PUT'], name='Update client contact')
    router.add_api_route("/{contact_id}", delete_client_contact, methods=['DELETE'], name='Delete client contact')

    return router


async def list_contacts(
    client_id: str,
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    # session: AsyncSession = Depends(get_session)
):
    contacts = await db_api.list_client_contacts(
        client_id=client_id, limit=limit, page=page, order=order, desc=desc, q=q
    )
    return {'items': contacts}


async def get_client_contact(client_id: str, contact_id: int):
    contact_db = await db_api.get_client_contact_by_id(contact_id)
    return contact_db


async def create_client_contact(client_id: str, contact: ContactCreate):
    formdata = await ctx.request.value.form()

    if 'contact' not in formdata:
        raise HTTPException(400, f'Form-data must include "contact"')

    form_contact = formdata['contact']
    data = form_contact.contact.read()
    contact_dict = {
        'client_id': client_id,
        'contact': data,
        'contact_name': form_contact.contactname,
        'contact_type': '',
        # 'manager': ctx.current_user().id,
        # 'manager_id': ctx.current_user().id,
    }

    db_client = await db_api.create_client_contact(contact_dict())
    return db_client


async def update_client_contact(client_id: str, contact_id: int, client: ContactUpdate):
    contact_db = await db_api.get_client_contact_by_id(contact_id)
    updated_client = await db_api.update_client_contact(contact_db, client.model_dump())
    return updated_client


async def delete_client_contact(client_id: str, contact_id: int):
    contact_db = await db_api.get_client_contact_by_id(contact_id)
    await db_api.delete_client_contact(contact_id)
    return None
