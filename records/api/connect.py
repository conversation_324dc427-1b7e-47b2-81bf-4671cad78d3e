import logging
import re
from typing import Optional

from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

import pydantic
from urllib import parse
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from starlette.responses import RedirectResponse
from google.oauth2.credentials import Credentials

from records.api import auth_utils, common_utils, handle_error, confirm
from records.cache import global_cache
from records import oauth
from records.context import context as ctx
from records.db import api as db_api
from records.e_mail import send
from records.utils import utils
from records.utils.blowfish_utils import encrypt_dict

logger = logging.getLogger(__name__)
email_re = re.compile('^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+.[a-z]{2,14}$')


def get_router():
    auth_router = APIRouter(prefix='/connections')

    auth_router.add_api_route("/available", list_available, methods=['GET'], name='list available connections')
    auth_router.add_api_route("/active", list_active, methods=['GET'], name='list active connections for the user')
    auth_router.add_api_route("/callback/{service}", service_callback, methods=['GET'], name='Service callback (e.g. google)')
    auth_router.add_api_route("/login/{service}", service_login, methods=['GET'], name='Service login (e.g. google)')
    # auth_router.add_api_route("/workspace_info", current_workspace, methods=['GET'], name='current workspace')

    return auth_router


class ChangeUserInfoData(pydantic.BaseModel):
    info: dict


def _get_state_key(state):
    return f'state/{state}'


async def list_available():
    services = {'services': list(oauth.all_providers.keys())}
    return JSONResponse(content=services, status_code=200)


async def list_active():
    ses = ctx.current_session()
    if auth_utils.ephemeral_user(ses):
        raise HTTPException(401, 'Unauthenticated')

    connections = await db_api.list_user_sa(user_id=ses.user_id)
    conn_dicts = []
    for conn in connections:
        conn_dict = conn.to_dict()
        conn_dict.pop('token')
        conn_dicts.append(conn_dict)
    result = {'connections': conn_dicts}

    return JSONResponse(content=result, status_code=200)


async def service_login(service: str, invite_id: Optional[str] = None, redirect_to: Optional[str] = None):
    provider = oauth.get_provider(service)
    if not provider:
        raise HTTPException(400, f'Provider {service} is not supported')

    flow = InstalledAppFlow.from_client_config(
        provider.config, scopes=provider.scopes()
    )
    base_url = utils.get_global_base_url()
    flow.redirect_uri = f'{base_url}/api/v1/connections/callback/{service}'

    authorization_url, state = flow.authorization_url(
        access_type='offline',
        prompt='select_account'
    )

    # Save the state so we can verify the request later
    state_key = _get_state_key(state)

    domain = common_utils.get_org_host(ctx.request.value)
    # org = await db_api.get_org_by_domain(domain)
    # if org is None:
    #     return handle_error.error_response(400, f'Organization not found for: {domain}')

    await global_cache.get_cache().aset(state_key, (domain, invite_id, redirect_to), 15 * 60)

    return RedirectResponse(authorization_url)


async def service_callback(service: str, state: str):
    # Verify the request state
    base_url = utils.get_base_url(ctx.request.value)
    ses = ctx.current_session()

    provider = oauth.get_provider(service)
    if not provider:
        msg = f'Provider {service} is not supported'
        return RedirectResponse(f'{base_url}/message?error={parse.quote(msg)}')

    state_key = _get_state_key(state)
    state_result = await global_cache.get_cache().aget(state_key)
    if not state_result:
        msg = f'Not valid secret key'
        return RedirectResponse(f'{base_url}/message?error={parse.quote(msg)}')
    domain, invite_id, redirect_to = state_result

    # TODO Begin Generalize this
    # Create the OAuth flow object
    flow = InstalledAppFlow.from_client_config(provider.config, scopes=provider.scopes(), state=state)
    flow.redirect_uri = f'{base_url}/api/v1/connections/callback/{service}'

    # Exchange the authorization code for an access token
    tokens = flow.fetch_token(code=ctx.request.value.query_params['code'])

    # Save the credentials to the session
    credentials: Credentials = flow.credentials

    # View the email address of the authenticated user.
    user_info_service = build('oauth2', 'v2', credentials=credentials, cache_discovery=False)
    user_info = user_info_service.userinfo().get().execute()
    login = user_info['email']
    # TODO End Generalize this

    # Process invite
    if invite_id:
        invite = await db_api.get_user_invite(invite_id)
        group_user = await confirm.get_invite_group(invite)
        # Check user login matches
        u = await db_api.get_user_by_login(login)
        msg = 'Invite email does not match user login, please use the email where you received the invite.'
        if invite.to_user_id != 0:
            if invite.to_user_id != u.id:
                return RedirectResponse(f'{base_url}/message?error={parse.quote(msg)}')
        if invite.to_user_login:
            if invite.to_user_login != login:
                return RedirectResponse(f'{base_url}/message?error={parse.quote(msg)}')

    user, user_sa = await ensure_user_service_account(login, provider.service_name, user_info, tokens)
    ses.user_id = user_sa.user_id
    await auth_utils.login_session(ses)

    if invite_id:
        await confirm.perform_invite(ses, invite, group_user)

    if not redirect_to:
        # org_base_url = await common_utils.resolve_org_base_url(org.id, req_port=ctx.request.value.url.port)
        return RedirectResponse('/')
    else:
        return RedirectResponse(redirect_to)


async def ensure_user_service_account(login, provider, user_info, tokens: dict):
    # Ensure user first
    name = user_info['name']
    user = await db_api.get_user_by_login(login, notfoundok=True)
    if not user:
        # Register user
        new_password = utils.generate_pass_from_uuid(utils.generate_unicode_uuid())
        values = {
            'login': login,
            'password': new_password,
            'confirmed': True,
            'name': name,
        }
        user = await db_api.create_user(values)
        base_url = utils.get_base_url(ctx.request.value)

        send.send_confirmation(user, confirm=None, base_url=base_url, new_password=new_password)

    # User exists at this point, ensure user service account
    user_sa = await db_api.get_user_sa(user_id=user.id, service=provider, notfoundok=True)
    if not user_sa:
        user_sa_dict = {
            'user_id': user.id,
            'service': provider,
            'service_id': user_info['id'],
            'service_picture': user_info.get('picture'),
            'service_name': user_info.get('name'),
            'token': encrypt_dict(tokens),
        }
        user_sa = await db_api.create_user_sa(user_sa_dict)
    else:
        vals = {
            'token': encrypt_dict(tokens),
            'service_id': user_info['id'],
            'service_picture': user_info.get('picture'),
            'service_name': user_info.get('name'),
        }
        user_sa = await db_api.update_user_sa(user_sa, vals)

    return user, user_sa
