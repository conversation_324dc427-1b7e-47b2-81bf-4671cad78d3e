import logging
from typing import List

from fastapi import Request

from records.cache import global_cache
from records.db import api as db_api, base
from records.db import models

logger = logging.getLogger(__name__)


russia_ukraine_related = [
    "RU",
    "RUSSIA",
    "Russia",
    "Russian",
    "Russian Federation"
    "The Russian Federation",
    "UKRAINE",
    "Ukraine",
    "UA",
    "Россия",
    "Украина",
]


def to_dict(items: List[base.ModelBase] | base.ModelBase | None):
    if items is None:
        return None
    if isinstance(items, base.ModelBase):
        return items.to_dict()

    return [item.to_dict() for item in items]


def get_org_host(req: Request):
    org_host = req.url.hostname
    return org_host


def get_ws_cache_key(ses: models.Session, ws_id):
    return f'session/{ses.id}/ws/{ws_id}'


def get_cache_key(sess_id, request: Request, domain=None):
    domain = domain or get_org_host(request)
    cache_key = f'session/{sess_id}/domain/{domain}'
    return cache_key


async def clear_cache_for_session(sess_id):
    await global_cache.get_cache().adelete_prefix(f'session/{sess_id}')


async def clear_cache_for_user(user_id):
    sessions = await db_api.get_sessions(user_id=user_id)
    for session in sessions:
        await clear_cache_for_session(session.id)


def build_full_address(address_dict: dict):
    # Build address depending on country
    if address_dict.get('country') in russia_ukraine_related:
        return (
            f"{address_dict.get('city', '')}, {address_dict.get('state', '')} {address_dict.get('street', '')}, "
            f"{address_dict.get('zip', '')}"
        )
    else:
        return (
            f"{address_dict['street']} {address_dict.get('city', '')} "
            f"{address_dict.get('state', '')}, {address_dict.get('zip', '')}"
        )
