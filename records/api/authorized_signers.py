import os
from typing import Optional

from fastapi import APIRouter
from pydantic import BaseModel

from records.api import client_persons
from records.db import api as db_api


class AuthorizedSignerCreate(BaseModel):
    signer_name: Optional[str] = None
    note: Optional[str] = None


class AuthorizedSignerUpdate(BaseModel):
    id: Optional[int] = None
    client_person_id: Optional[str] = None
    note: Optional[str] = None

    person: Optional[client_persons.PersonCreate] = None


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'authorized_signers'))

    router.add_api_route("", list_signers, methods=['GET'], name='List client signers')
    router.add_api_route("", create_authorized_signer, methods=['POST'], name='Create client signer')
    router.add_api_route("/{signer_id}", get_authorized_signer, methods=['GET'], name='Get client signer')
    router.add_api_route("/{signer_id}", update_authorized_signer, methods=['PUT'], name='Update client signer')
    router.add_api_route("/{signer_id}", delete_authorized_signer, methods=['DELETE'], name='Delete client signer')

    return router


async def list_signers(
    client_id: str,
    # session: AsyncSession = Depends(get_session)
):
    signers = await db_api.list_authorized_signers(
        client_id=client_id,
    )
    return {'items': signers}


async def get_authorized_signer(client_id: str, signer_id: str):
    signer_db = await db_api.get_authorized_signer_by_id(signer_id)
    return signer_db


async def create_authorized_signer(client_id: str, signer: AuthorizedSignerCreate):
    signer_dict = {
        'client_id': client_id,
        'signer_name': signer.signer_name,
        'note': signer.note,
    }

    db_client = await db_api.create_authorized_signer(signer_dict)
    return db_client


async def update_authorized_signer(client_id: str, signer_id: str, signer: AuthorizedSignerUpdate):
    signer_db = await db_api.get_authorized_signer_by_id(signer_id)
    updated_client = await db_api.update_authorized_signer(signer_db, signer.model_dump())
    return updated_client


async def delete_authorized_signer(client_id: str, signer_id: str):
    await db_api.delete_authorized_signer(signer_id)
    return None
