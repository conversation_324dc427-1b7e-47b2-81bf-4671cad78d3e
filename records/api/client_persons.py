import os

from fastapi import APIRouter, Depends

from records.api import auth_utils
from records.db import api as db_api
from records.services.client_structs import PersonCreate
from records.services.client_structs import PersonUpdate


def get_router(prefix='/'):
    router = APIRouter(
        prefix=os.path.join(prefix, 'client_persons'),
        dependencies=[Depends(auth_utils.basic_access)]
    )

    router.add_api_route("", list_client_persons, methods=['GET'], name='List client_persons')
    router.add_api_route("", create_client_person, methods=['POST'], name='Create client_person')
    router.add_api_route("/{client_person_id}", get_client_person, methods=['GET'], name='Get client_person')
    router.add_api_route("/{client_person_id}", update_client_person, methods=['PUT'], name='Update client_person')
    router.add_api_route("/{client_person_id}", delete_client_person, methods=['DELETE'], name='Delete client_person')

    return router


async def list_client_persons(
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    # session: AsyncSession = Depends(get_session)
):
    client_persons, count = await db_api.list_persons(limit=limit, page=page, order=order, desc=desc, q=q)
    return {'items': client_persons, 'count': count, 'limit': limit, 'page': page}


async def get_client_person(client_person_id: str):
    db_client_person = await db_api.get_person_by_id(client_person_id)
    return db_client_person


async def create_client_person(client_person: PersonCreate):
    client_person_dict = client_person.model_dump(exclude_unset=True)
    client_person_dict['full_title'] = f"{client_person_dict['firstname']} {client_person_dict['lastname']}"

    db_client_person = await db_api.create_person(client_person_dict)
    return db_client_person


async def update_client_person(client_person_id: str, client_person: PersonUpdate):
    db_client_person = await db_api.get_person_by_id(client_person_id)
    update_dict = client_person.model_dump(exclude_unset=True)

    updated_client_person = await db_api.update_person(db_client_person, update_dict)
    return updated_client_person


async def delete_client_person(client_person_id: str):
    db_client_person = await db_api.get_person_by_id(client_person_id)
    await db_api.delete_person(client_person_id)
    return None
