import os
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException
from fastapi import Response
from fastapi.responses import ORJSONResponse
from pydantic import ValidationError

from records.api import auth_utils, search
from records.api import authorized_signers as authorized_signers_api
from records.api import bank_accounts as bank_accounts_api
from records.api import changes_pending
from records.api import client_addresses
from records.api import client_contacts as contact_api
from records.api import client_persons
from records.api import client_registrations
from records.api import client_reports
from records.api import client_services
from records.api import client_subscriptions
from records.api import client_tasks
from records.api import client_tax_reports
from records.api import files
from records.api import questions
from records.context import context as ctx
from records.db import api as db_api, base
from records.db import models
from records.services import clients, schemas, file_processing
from records.services.client_structs import ClientApprove
from records.services.client_structs import ClientCreate, ClientUpdate
from records.utils import json_utils, utils


def get_router(prefix='/'):
    router = APIRouter(
        prefix=os.path.join(prefix, 'clients'),
        dependencies=[Depends(auth_utils.basic_access)]
    )

    router.add_api_route("", list_clients, methods=['GET'], name='List clients')
    router.add_api_route("/by-ein/{ein}", get_client_by_ein, methods=['GET'], name='See if client exists by ein')
    router.add_api_route("/by-name/{name}", get_client_by_name, methods=['GET'], name='See if client exists by name')
    router.add_api_route("/tabs_available", list_client_tabs, methods=['GET'], name='List clients')
    router.add_api_route("/validate", validate_client, methods=['POST'], name='Validate client')
    router.add_api_route("", create_client, methods=['POST'], name='Create client')
    router.add_api_route("/{client_id}", get_client, methods=['GET'], name='Get client')
    router.add_api_route("/{client_id}", update_client, methods=['PUT'], name='Update client')
    router.add_api_route("/{client_id}/validate", validate_client, methods=['POST'], name='Validate client')
    router.add_api_route("/{client_id}/approve", approve_client, methods=['PUT'], name='Approve client')
    router.add_api_route("/{client_id}/cancel_changes", cancel_changes, methods=['PUT'], name='Cancel changes')
    router.add_api_route("/{client_id}/approved_data", get_approved_client_data, methods=['GET'], name='Get approved client data')
    router.add_api_route("/{client_id}", delete_client, methods=['DELETE'], name='Delete client')

    client_prefix = '/{client_id}'
    router.include_router(files.get_router(prefix='/'), prefix=client_prefix, tags=['Files'])
    router.include_router(contact_api.get_router(prefix='/'), prefix=client_prefix, tags=['Contacts'])
    router.include_router(client_addresses.get_router(prefix='/'), prefix=client_prefix, tags=['Client Addresses'])
    router.include_router(client_registrations.get_router(prefix='/'), prefix=client_prefix, tags=['Registrations'])
    router.include_router(client_persons.get_router(prefix='/'), prefix=client_prefix, tags=['Persons'])
    router.include_router(authorized_signers_api.get_router(prefix='/'), prefix=client_prefix, tags=['Authorized Signers'])
    router.include_router(bank_accounts_api.get_router(prefix='/'), prefix=client_prefix, tags=['Bank Accounts'])
    router.include_router(client_tasks.get_router(prefix='/'), prefix=client_prefix, tags=['Client Tasks'])
    router.include_router(client_services.get_router(prefix='/'), prefix=client_prefix, tags=['Client Services'])
    router.include_router(client_subscriptions.get_router(prefix='/'), prefix=client_prefix, tags=['Client Subscriptions'])
    router.include_router(client_tax_reports.get_router(prefix='/'), prefix=client_prefix, tags=['Client Tax Reports'])
    router.include_router(client_reports.get_router(prefix='/'), prefix=client_prefix, tags=['Client Reports'])
    for r in changes_pending.get_routers(prefix='/'):
        router.include_router(r, prefix=client_prefix, tags=['Pending Changes'])

    router.include_router(search.get_router(prefix='/'), prefix=client_prefix, tags=['Client search'])
    router.include_router(questions.get_router(prefix='/'), prefix=client_prefix, tags=['Questions'])

    return router


async def list_clients(
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    internal_draft_flag: bool = None,
    # session: AsyncSession = Depends(get_session)
):
    clients, count = await db_api.list_clients(
        limit=limit, page=page, order=order, desc=desc, q=q, internal_draft_flag=internal_draft_flag
    )
    clients_dict = [c.to_dict() for c in clients]
    for client in clients_dict:
        if 'addresses' in client:
            for addr in client['addresses']:
                if 'address' in addr and addr['address']:
                    addr['address']['full_title'] = addr['address'].get('full_address')

    return {'items': clients_dict, 'count': count, 'limit': limit, 'page': page}


async def list_client_tabs():
    return clients.list_client_tabs()


async def get_client(client_id: str):
    _, client_dict = await clients.get_client_data(client_id)

    for addr in client_dict.get('addresses', []):
        if 'address' in addr and addr['address']:
            addr['address']['full_title'] = addr['address'].get('full_address')

    return client_dict


async def get_client_by_ein(ein: str):
    async with base.session_context():
        ein = utils.clean_ein(ein)
        client = await db_api.get_client_by_ein(ein)
        if not client:
            return None
        _, client_dict = await clients.get_client_data(client.id)

        return client_dict


async def get_client_by_name(name: str):
    async with base.session_context():
        name = utils.clean_name(name)
        client = await db_api.search_client_by_name(name=name, notfoundok=True)
        if not client:
            return None
        _, client_dict = await clients.get_client_data(client.id)

        return client_dict


async def validate_client(client: dict, client_id=None):
    # Take schema
    schema = schemas.api_client_schema
    # Validate client data
    result = {
        'valid': True,
        'error': None,
        'message': None,
        'details': []
    }

    try:
        client_data = ClientCreate.model_validate(client)
    except ValidationError as e:
        result = json_utils.parse_pydantic_error(e)
        return result

    # Validate without raising an exception
    validation_error = json_utils.validate_schema(client, schema, raise_exception=False)

    if validation_error:
        # Use the parse_pydantic_error function to get a comprehensive error message
        result = json_utils.parse_validation_error(validation_error)

    return result


async def create_client(client: ClientCreate):
    # check for duplicate name / ein
    if await db_api.get_client_by(name=client.name, notfoundok=True):
        raise HTTPException(status_code=409, detail=f"Client with name {client.name} already exists")
    if await db_api.get_client_by_ein(ein=utils.clean_ein(client.ein)):
        raise HTTPException(status_code=409, detail=f"Client with ein {client.ein} already exists")

    client_dict = client.model_dump(exclude_unset=True)
    client_dict['internal_draft_flag'] = True

    updated_client = await clients.create_client_data(client_dict)

    for addr in updated_client.get('addresses', []):
        if 'address' in addr and addr['address']:
            addr['address']['full_title'] = addr['address'].get('full_address')

    return ORJSONResponse(content=updated_client)


async def update_client(client_id: str, client: ClientUpdate):
    client_dict = client.model_dump(exclude_unset=True)

    # Set draft flag if client is not in draft
    client_dict['internal_draft_flag'] = True
    updated_client = await clients.update_client_data(client_id, client_dict)

    for addr in updated_client.get('addresses', []):
        if 'address' in addr and addr['address']:
            addr['address']['full_title'] = addr['address'].get('full_address')

    return ORJSONResponse(content=updated_client)


async def approve_client(client_id: str, approve_data: Optional[ClientApprove] = None):
    manager: models.Manager = ctx.current_manager()
    return await clients.approve_client_data(client_id, manager.title, approve_data.note if approve_data else None)


async def get_approved_client_data(client_id: str):
    return await clients.get_last_approved_client_data(client_id)


async def cancel_changes(client_id: str):
    async with base.session_context():
        last_data = await clients.get_last_approved_client_data(client_id)
        last_data['internal_draft_flag'] = False
        updated_client = await clients.update_client_data(client_id, last_data)

    return updated_client


async def delete_client(client_id: str):
    db_client = await db_api.get_client_by_id(client_id)

    async with base.session_context():
        await file_processing.delete_resources(db_client)

        # Delete client connected data
        await db_api.delete_client_addresses(client_id=client_id)
        await db_api.delete_client_bank_accounts(client_id=client_id)
        await db_api.delete_client_contacts(client_id=client_id)
        await db_api.delete_client_files(client_id=client_id, keep_from_upload=True)
        await db_api.delete_debit_cards(client_id=client_id)
        await db_api.delete_client_payment_systems(client_id=client_id)
        await db_api.delete_client_registrations(client_id=client_id)
        await db_api.delete_client_services(client_id=client_id)
        await db_api.delete_client_shares(client_id=client_id)
        await db_api.delete_client_shareholders(client_id=client_id)
        await db_api.delete_client_tasks(client_id=client_id)
        await db_api.delete_client_tax_reports(client_id=client_id)
        await db_api.delete_client_capitalizations(client_id=client_id)

        # System data
        await db_api.delete_change_records(client_id=client_id)

        # Delete client
        await db_api.delete_client(client_id)

    return Response(status_code=204)
