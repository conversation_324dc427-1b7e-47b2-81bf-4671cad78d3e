import os
import datetime
from typing import Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, field_validator

from records.utils import json_utils

from records.context import context as ctx
from records.db import api as db_api


class AddressCreate(BaseModel):
    # Address part
    street: str
    pobox: Optional[str] = None
    city: str
    state: str
    zip: str
    country: str

    # Client connection part
    address_type: Optional[str] = None
    renewal_date: Optional[datetime.datetime] = None
    phone: Optional[str] = None
    paid_by: Optional[str] = None
    note: Optional[str] = None

    _date_validator = field_validator(
        *['renewal_date'],
        mode='before'
    )(json_utils.date_validator)


class AddressUpdate(BaseModel):
    pass


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'addresses'))

    router.add_api_route("", list_addresses, methods=['GET'], name='List client addresses')
    router.add_api_route("", create_client_address, methods=['POST'], name='Create client address')
    router.add_api_route("/{address_id}", get_client_address, methods=['GET'], name='Get client address')
    router.add_api_route("/{address_id}", update_client_address, methods=['PUT'], name='Update client address')
    router.add_api_route("/{address_id}", delete_client_address, methods=['DELETE'], name='Delete client address')

    return router


async def list_addresses(
    client_id: str,
    # session: AsyncSession = Depends(get_session)
):
    addresses = await db_api.list_client_addresses(client_id=client_id)
    return {'items': addresses}


async def get_client_address(client_id: str, address_id: int):
    address_db = await db_api.get_client_address_by_id(address_id)
    return address_db


async def create_client_address(client_id: str, address: AddressCreate):
    formdata = await ctx.request.value.form()

    if 'address' not in formdata:
        raise HTTPException(400, f'Form-data must include "address"')

    form_address = formdata['address']
    data = form_address.address.read()
    address_dict = {
        'client_id': client_id,
        'address': data,
        'address_name': form_address.addressname,
        'address_type': '',
        # 'manager': ctx.current_user().id,
        # 'manager_id': ctx.current_user().id,
    }

    db_client = await db_api.create_client_address(address_dict)
    return db_client


async def update_client_address(client_id: str, address_id: int, client: AddressUpdate):
    address_db = await db_api.get_client_address_by_id(address_id)
    updated_client = await db_api.update_client_address(address_db, client.dict())
    return updated_client


async def delete_client_address(client_id: str, address_id: int):
    address_db = await db_api.get_client_address_by_id(address_id)
    await db_api.delete_client_address(address_id)
    return None
