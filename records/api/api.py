import asyncio
import contextlib
import json
import logging
import time
from typing import Optional

import starlette.responses
from fastapi import APIRouter, HTTPException
from fastapi import FastAP<PERSON>
from fastapi import Request
from fastapi import Response
from fastapi.responses import JSONResponse

from records import oauth
from records.api import auth
from records.api import auth_utils, catalogs, client_persons, addresses, reg_agents, search
from records.api import clients
from records.api import common_utils
from records.api import clients_uploads
from records.api import confirm
from records.api import connect
from records.api import files_access
from records.api import handle_error
from records.api import managers
from records.api import sources
from records.api import services
from records.api import subscriptions
from records.api.auth_utils import get_cookie_domain
from records.cache import global_cache
from records.config import shared_config
from records.context import context as ctx
from records.credentials import credentials
from records.db import api as db_api, base
from records.db import models
from records.e_mail import send
from records.policies import policies
from records.services import jobs
from records.utils import utils, alembic_utils

OK_RESULT = '{"message": "OK"}\n'
API_PREFIX = '/api/v1'
API_WS_PREFIX = '/api/v1/workspaces/'
logger = logging.getLogger(__name__)
without_auth = {
    '/api/v1/auth/login',
    '/api/v1/auth/info',
    '/api/v1/auth/register',
    '/api/v1/auth/reset_password',
    '/api/v1/confirm',
    '/api/v1/workspaces',
    '/api/v1/organization/info',
}
skip_session = {
    '/api/v1/new_session_id',
    '/api/v1/example',
    '/api/v1/connections/available',
}
skip_auth = {
    '/api/v1/message',
}
skip_auth_prefix = {
    '/api/v1/connections/login/',
    '/api/v1/connections/callback/',
}


def get_router():
    root = APIRouter()
    api_router = APIRouter(prefix=API_PREFIX)

    api_router.include_router(auth.get_router(), tags=["Auth"])
    api_router.include_router(connect.get_router(), tags=["Connections"])
    api_router.include_router(confirm.get_router(), tags=["Confirm"])

    api_router.include_router(clients.get_router(), tags=["Clients"])
    api_router.include_router(clients_uploads.get_router(), tags=["Client Uploads"])
    api_router.include_router(client_persons.get_router(), tags=["Persons"])
    api_router.include_router(catalogs.get_router(), tags=["Catalogs"])
    api_router.include_router(addresses.get_router(), tags=["Addresses"])
    api_router.include_router(reg_agents.get_router(), tags=["RegAgents"])
    api_router.include_router(managers.get_router(), tags=["Managers"])
    api_router.include_router(search.get_router(), tags=["Search"])
    api_router.include_router(sources.get_router(), tags=["Sources"])
    api_router.include_router(services.get_router(), tags=["Services"])
    api_router.include_router(subscriptions.get_router(), tags=["Subscriptions"])
    api_router.include_router(files_access.get_router(), tags=["File Access"])

    api_router.add_api_route("/example", example_api_endpoint, methods=['GET'], tags=['Example'])
    api_router.add_api_route("/example", example_api_endpoint_post, methods=['POST'], tags=['Example'])
    api_router.add_api_route("/message", message_show, methods=['GET'], tags=['Example'])

    root.include_router(api_router)
    root.add_route("/health", health, methods=['GET'], name="health")

    return root


async def time_middleware(request: Request, call_next):
    if request.url.path == '/health':
        return await call_next(request)

    t = time.time()
    response = await call_next(request)
    elapsed_ms = f'{(time.time() - t) * 1000:9.3f}ms'
    urlpath = request.url.path + (('?' + request.url.query) if request.url.query else '')
    message = f'{response.status_code} | {elapsed_ms} | {request.method:7} | {urlpath}'

    logger.info(message)

    return response


async def last_seen_process(request: Request):
    if request.method not in ['POST', 'PUT', 'DELETE']:
        return

    u = ctx.current_user()
    if not u:
        return
    _ = asyncio.create_task(
        utils.log_exceptions(db_api.update_user(u, {'last_activity_at': models.now()}))
    )


async def req_middleware(request: Request, call_next):
    ctx.request.set(request)

    if not request.url.path.startswith(API_PREFIX):
        return await call_next(request)

    if request.url.path in skip_session:
        return await call_next(request)

    async with base.session_context():
        # Session handling start
        session_created, cache_key, cache_result = await handle_session(request)
        # Session handling end

        if request.url.path in skip_auth or request.url.path.startswith(tuple(skip_auth_prefix)):
            return await call_next(request)

        # Auth handling start
        user, manager, permissions = await handle_user(request, cache_result)
        # Auth handling end

    # Permissions handling start
    # ---
    # Permissions handling end

    if not cache_result:
        await global_cache.get_cache().aset(cache_key, (ctx.current_session(), user, manager))

    response: starlette.responses.Response = await call_next(request)
    if session_created:
        response.set_cookie(
            key=auth_utils.COOKIE_KEY,
            value=ctx.session.value.id,
            expires=ctx.session.value.ttl,
            httponly=True,
            domain=get_cookie_domain(request)
        )

    await last_seen_process(request)

    return response


async def handle_session(request: Request):
    domain = common_utils.get_org_host(request)
    try:
        session_created, cache_key, cache_result = await auth_utils.session_handler(request, domain)
    except HTTPException as e:
        return handle_error.handle_exception(request, e)

    return session_created, cache_key, cache_result


async def handle_user(request: Request, cache_result):
    user = get_user_from_cache(cache_result)
    manager = get_manager_from_cache(cache_result)
    permissions = 0
    role = None
    if auth_utils.ephemeral_user(ctx.session.value):
        return user, manager, permissions

    if not user:
        user = await db_api.get_user_by_id(ctx.session.value.user_id)
    if not manager:
        manager = await db_api.get_manager_by_user_id(user.id)
        role = policies.get_role_by_name(manager.role_name)
        permissions = role['permissions'] if role else 0
    else:
        role = policies.get_role_by_name(manager.role_name)
        permissions = role['permissions'] if role else 0

    if user.id in credentials.ADMIN_IDS:
        ctx.current_session().admin = True
        permissions = policies.AccessMode.OWNER
        role = policies.get_role_by_name(policies.RoleName.OWNER)

    ctx.request.value.state.user = user
    ctx.request.value.state.manager = manager
    ctx.request.value.state.permissions = permissions
    ctx.request.value.state.role_name = role['name']

    return user, manager, permissions


def get_user_from_cache(cache_result):
    if not cache_result:
        return None
    sess, user, manager = cache_result
    return user


def get_manager_from_cache(cache_result):
    if not cache_result:
        return None
    _, _, manager = cache_result
    return manager


def get_session_from_cache(cache_result):
    if not cache_result:
        return None
    sess, user, manager = cache_result
    return sess


@contextlib.asynccontextmanager
async def lifespan(app: FastAPI):
    await startup()
    yield
    await shutdown()


async def startup():
    utils.setup_logging()
    send.init_smtp()
    oauth.init()

    alembic_utils.run_migrations('migrations')
    shared_config.SharedConfig()

    logger.info("Starting the server...")


async def shutdown():
    if jobs.JOB_COUNTER.total() != 0:
        logger.info(f"Wait for {jobs.JOB_COUNTER.total()} jobs to complete...")
        while jobs.JOB_COUNTER.total() > 0:
            await asyncio.sleep(1)
    logger.info("Shutdown the server...")


async def health(request):
    return Response(content=OK_RESULT, status_code=200)


async def message_show(text: Optional[str] = None, error: Optional[str] = None):
    msg = {'message': 'OK'}
    if text:
        msg = {'message': text}
    if error:
        msg = {'error': error}
    return JSONResponse(content=msg, status_code=200)


async def example_api_endpoint(data: Request):
    return JSONResponse(content={
        "query": {k: v for k, v in data.query_params.items()},
        "headers": {k: v for k, v in data.headers.items()},
    }, status_code=200)


async def new_session_id():
    return JSONResponse(content={"id": utils.generate_unicode_uuid()}, status_code=200)


async def example_api_endpoint_post(data: Request):
    data_body = await data.body()
    logger.info(f'[Example] Data: {data_body.decode()}')
    logger.info(f'[Example] Headers: { {k: v for k, v in data.headers.items()} }')
    return JSONResponse(content={
        "message": json.loads(data_body),
        "headers": {k: v for k, v in data.headers.items()},
        "query": {k: v for k, v in data.query_params.items()},
    })
