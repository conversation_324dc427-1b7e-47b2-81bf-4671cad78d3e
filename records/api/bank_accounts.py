import os

from fastapi import APIRouter

from records.db import api as db_api
from records.services.client_structs import BankAccountCreate
from records.services.client_structs import BankAccountUpdate


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'bank_accounts'))

    router.add_api_route("", list_accounts, methods=['GET'], name='List client accounts')
    router.add_api_route("", create_bank_account, methods=['POST'], name='Create client account')
    router.add_api_route("/{account_id}", get_bank_account, methods=['GET'], name='Get client account')
    router.add_api_route("/{account_id}", update_bank_account, methods=['PUT'], name='Update client account')
    router.add_api_route("/{account_id}", delete_bank_account, methods=['DELETE'], name='Delete client account')

    return router


async def list_accounts(
    client_id: str,
    # session: AsyncSession = Depends(get_session)
):
    accounts = await db_api.list_bank_accounts(
        client_id=client_id,
    )
    return {'items': accounts}


async def get_bank_account(client_id: str, account_id: str):
    account_db = await db_api.get_bank_account_by_id(account_id)
    return account_db


async def create_bank_account(client_id: str, account: BankAccountCreate):
    data = account.model_dump(exclude_none=True)
    account_dict = {
        'client_id': client_id,
        'bank_name': data['bank_name'],
        'aba_number': data['aba_number'],
        'account_number': data.get('account_number'),
        'controlled_by': data.get('controlled_by'),
        'authorized_signer_id': data.get('authorized_signer_id'),
        'bank_contact': data['bank_contact'],
        'date_opened': data['date_opened'],
        'last_renewal': data['last_renewal'],
        'notes': data['notes'],
        # 'manager': ctx.current_user().id,
        # 'manager_id': ctx.current_user().id,
    }

    db_client = await db_api.create_bank_account(account_dict)
    return db_client


async def update_bank_account(client_id: str, account_id: str, client: BankAccountUpdate):
    account_db = await db_api.get_bank_account_by_id(account_id)
    updated_client = await db_api.update_bank_account(account_db, client.model_dump(exclude_none=True))
    return updated_client


async def delete_bank_account(client_id: str, account_id: str):
    account_db = await db_api.get_bank_account_by_id(account_id)
    await db_api.delete_bank_account(account_id)
    return None
