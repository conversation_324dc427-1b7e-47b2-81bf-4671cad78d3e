import asyncio
import datetime
import json
import logging
import re
import uuid
from typing import Optional

import fastapi
import pydantic
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from records.api import auth_utils
from records.api import common_utils
from records.api import confirm
from records.context import context as ctx
from records.db import api as db_api, base
from records.db import models
from records.e_mail import send
from records.policies import policies
from records.utils import utils

logger = logging.getLogger(__name__)
email_re = re.compile('^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+.[a-z]{2,14}$')


def get_router():
    auth_router = APIRouter(prefix='/auth')

    auth_router.add_api_route("/info", current_auth, methods=['GET'], name='Auth info')
    auth_router.add_api_route("/info", change_user_info, methods=['PUT'], name='Change user info')
    auth_router.add_api_route("/login", login, methods=['POST'], name='Login')
    # auth_router.add_api_route("/register", register, methods=['POST'], name='Register')
    # auth_router.add_api_route("/create", create, methods=['POST'], name='Create')
    auth_router.add_api_route("/logout", logout, methods=['POST'], name='Logout')
    auth_router.add_api_route("/reset_password", reset_password, methods=['POST'], name='Reset password')
    auth_router.add_api_route("/change_password", change_password, methods=['POST'], name='Change password')
    # auth_router.add_api_route("/workspace_info", current_workspace, methods=['GET'], name='current workspace')

    return auth_router


class ChangeUserInfoData(pydantic.BaseModel):
    info: Optional[dict] = None
    name: Optional[str] = None


class RegisterData(pydantic.BaseModel):
    login: str
    password: str
    name: Optional[str] = None
    invite_id: Optional[str] = None


class LoginData(pydantic.BaseModel):
    login: str
    password: str
    invite_id: Optional[str] = None


class ChangePasswordData(pydantic.BaseModel):
    old_password: str
    new_password: str


class ResetPasswordData(pydantic.BaseModel):
    login: str


async def logout(request: fastapi.Request):
    await auth_utils.logout(request)
    return JSONResponse(content={'status': 'OK'}, status_code=200)


async def create(register_data: RegisterData):
    sess = ctx.current_session()
    if not sess.admin:
        raise HTTPException(403, 'Forbidden')

    values = {
        'login': register_data.login,
        'password': register_data.password,
        'confirmed': False,
    }
    if register_data.name:
        values['name'] = register_data.name
    u = await db_api.create_user(values)

    confirm = await db_api.create_user_confirm({
        'id': str(uuid.uuid4()),
        'user_id': u.id,
        'expiry_time': datetime.datetime.now() + datetime.timedelta(hours=24)
    })
    base_url = utils.get_base_url(ctx.request.value)

    send.send_confirmation(u, confirm, base_url)

    return JSONResponse(content=u.to_dict(), status_code=200)


async def login(login_data: LoginData):
    if login_data.invite_id:
        invite = await db_api.get_user_invite(login_data.invite_id)
        group_user = await confirm.get_invite_group(invite)
        # Check user login matches
        u = await db_api.get_user_by_login(login_data.login)
        if invite.to_user_id != 0:
            if invite.to_user_id != u.id:
                raise HTTPException(400, 'Invite email does not match user login')
        if invite.to_user_login:
            if invite.to_user_login != login_data.login:
                raise HTTPException(400, 'Invite email does not match user login')

    u, sess, created = await auth_utils.login(login_data.login, login_data.password)
    if login_data.invite_id:
        await confirm.perform_invite(sess, invite, group_user)

    # await common_utils.auto_add_in_org(ctx.current_org(), u)

    response = JSONResponse(content=sess.to_dict(), status_code=200)
    if created:
        response.set_cookie(
            key=auth_utils.COOKIE_KEY,
            value=sess.id,
            expires=sess.ttl,
            domain=auth_utils.get_cookie_domain(ctx.request.value),
            httponly=True
        )
    return response


async def register(register_data: RegisterData):
    if not email_re.match(register_data.login):
        raise HTTPException(400, 'Email is invalid')

    async with base.session_context() as sess_db:
        if register_data.invite_id:
            invite = await db_api.get_user_invite(register_data.invite_id)
            group_user = await confirm.get_invite_group(invite)
            # Check user login matches
            if invite.to_user_id:
                raise HTTPException(400, 'User exists, proceed to login')
            if invite.to_user_login != register_data.login:
                raise HTTPException(400, 'Invite email does not match user login')

        values = {
            'login': register_data.login,
            'password': register_data.password,
            'confirmed': bool(register_data.invite_id),
        }
        if register_data.name:
            values['name'] = register_data.name
        u = await db_api.create_user(values)
        await sess_db.flush()

        if register_data.invite_id:
            sess = ctx.current_session()
            await confirm.perform_invite(sess, invite, group_user)
        else:
            user_confirm = await db_api.create_user_confirm({
                'id': str(uuid.uuid4()),
                'user_id': u.id,
                'expiry_time': datetime.datetime.now() + datetime.timedelta(hours=24)
            })
            base_url = utils.get_base_url(ctx.request.value)

            send.send_confirmation(u, user_confirm, base_url)

    return JSONResponse(content=u.to_dict(), status_code=200)


async def reset_password(reset_password_data: ResetPasswordData):
    u = await db_api.get_user_by_login(reset_password_data.login)

    confirm = await db_api.create_user_confirm({
        'id': str(uuid.uuid4()),
        'user_id': u.id,
        'expiry_time': datetime.datetime.now() + datetime.timedelta(hours=24)
    })

    base_url = utils.get_base_url(ctx.request.value)
    send.send_reset_password(u, confirm, base_url)

    return JSONResponse(content={'status': 'OK'}, status_code=200)


async def change_password(data: ChangePasswordData):
    ses = ctx.current_session()
    u = await db_api.get_user_by_id(ses.user_id)
    u = await db_api.get_user_by_login_password(u.login.lower(), data.old_password)

    updated_u = await db_api.update_user(u, {'password': data.new_password})

    return JSONResponse(content=updated_u.to_dict(), status_code=200)


async def current_auth():
    ses = ctx.current_session()
    user_dict = {'login': None, 'info': None, 'name': 'Guest'}
    if not auth_utils.ephemeral_user(ctx.session.value):
        user = ctx.current_user()
        if not user:
            # Reanimate session
            ses = await db_api.update_session(ses, {
                'user_id': auth_utils.get_random_user_id(),
                'admin': False,
            })
            user_dict['id'] = ses.user_id
        user_dict = user.to_dict()

        _ = asyncio.create_task(
            utils.log_exceptions(db_api.update_user(user, {'last_seen_at': models.now()}))
        )
    else:
        user_dict['id'] = ses.user_id

    user_dict['session'] = ses.to_dict()
    user_dict['permissions'] = policies.decode_permissions(ctx.current_permissions())
    user_dict['role_name'] = ctx.current_role()
    return JSONResponse(content=user_dict, status_code=200)


async def change_user_info(info: ChangeUserInfoData):
    user = await db_api.get_user_by_id(ctx.current_user().id)
    update_dict = info.dict(exclude_unset=True)
    if not update_dict:
        raise HTTPException(400, 'No data to update')

    updated = await db_api.update_user(user, update_dict)
    await common_utils.clear_cache_for_user(user.id)
    return JSONResponse(content=updated.to_dict(), status_code=200)
