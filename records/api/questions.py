import os

from fastapi import APIRouter
from fastapi.responses import ORJSONResponse
from pydantic import BaseModel

from records.db import base
from records.services import questions as questions_svc


class QuestionCreate(BaseModel):
    question: str


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'questions'))

    router.add_api_route("", list_questions, methods=['GET'], name='List questions')
    router.add_api_route("", create_question, methods=['POST'], name='Create question')
    router.add_api_route("/{question_id}", get_question_by_id, methods=['GET'], name='Get question by id')
    router.add_api_route("/{question_id}", delete_question, methods=['DELETE'], name='Delete question')

    return router


async def list_questions(client_id: str):
    async with base.session_context():
        questions = await questions_svc.list_questions(client_id)
        return ORJSONResponse(
            content={'items': questions, 'count': len(questions)}
        )
    

async def create_question(client_id: str, question: QuestionCreate):
    async with base.session_context():
        question = await questions_svc.create_question(client_id, question.question)
        return ORJSONResponse(content=question)


async def delete_question(client_id: str, question_id: str):
    async with base.session_context():
        question = await questions_svc.delete_question(client_id, question_id)
        return ORJSONResponse(content=question)


async def get_question_by_id(client_id: str, question_id: str):
    async with base.session_context():
        question = await questions_svc.get_question_by_id(client_id, question_id)
        return ORJSONResponse(content=question)
