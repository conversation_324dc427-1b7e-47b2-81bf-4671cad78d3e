import datetime
import os
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from records.api import auth_utils
from records.db import api as db_api, base


class CatalogCreate(BaseModel):
    name: str
    options: Optional[list] = None


class CatalogUpdate(BaseModel):
    name: Optional[str] = None
    options: Optional[list] = None


def get_router(prefix='/'):
    router = APIRouter(
        prefix=os.path.join(prefix, 'catalogs'),
        dependencies=[Depends(auth_utils.basic_access)]
    )

    router.add_api_route("", list_catalogs, methods=['GET'], name='List catalogs')
    router.add_api_route("/by-name/{catalog_name}", get_catalog_by_name, methods=['GET'], name='Get catalog')
    router.add_api_route("", create_catalog, methods=['POST'], name='Create catalog')
    router.add_api_route("/{catalog_id}", get_catalog, methods=['GET'], name='Get catalog')
    router.add_api_route("/{catalog_id}", update_catalog, methods=['PUT'], name='Update catalog')
    router.add_api_route("/{catalog_id}", delete_catalog, methods=['DELETE'], name='Delete catalog')

    return router


async def list_catalogs(
    limit: int = 100,
    page: int = 1,
    order: str = 'name',
    desc: bool = False,
    q: str = None,
    # session: AsyncSession = Depends(get_session)
):
    catalogs, count = await db_api.list_catalogs(limit=limit, page=page, order=order, desc=desc, q=q)
    return {'items': catalogs, 'count': count}


async def get_catalog(catalog_id: int):
    db_catalog = await db_api.get_catalog_by_id(catalog_id)
    return db_catalog


async def get_catalog_by_name(catalog_name: str):
    db_catalog = await db_api.get_catalog_by_name(catalog_name)
    return db_catalog


async def create_catalog(catalog: CatalogCreate):
    async with base.session_context():
        exists = await db_api.get_catalog_by_name(catalog.name, notfoundok=True)
        if exists:
            raise HTTPException(409, 'Catalog with this name already exists')
        db_catalog = await db_api.create_catalog(catalog.model_dump())
    return db_catalog


async def update_catalog(catalog_id: int, catalog: CatalogUpdate):
    async with base.session_context():
        db_catalog = await db_api.get_catalog_by_id(catalog_id)
        update_dict = catalog.model_dump(exclude_unset=True)
        if not update_dict:
            raise HTTPException(400, 'No fields to update')

        if update_dict.get('name'):
            exists = await db_api.get_catalog_by_name(catalog.name, notfoundok=True)
            if exists and exists.id != catalog_id:
                raise HTTPException(409, 'Another catalog with this name already exists')

        updated_catalog = await db_api.update_catalog(db_catalog, update_dict)

    return updated_catalog


async def delete_catalog(catalog_id: int):
    db_catalog = await db_api.get_catalog_by_id(catalog_id)
    await db_api.delete_catalog(catalog_id)
    return None
