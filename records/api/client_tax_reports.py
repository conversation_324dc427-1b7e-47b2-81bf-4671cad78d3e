import collections
import datetime
import mimetypes
import os
from typing import Optional

from fastapi import APIRouter, HTTPException, UploadFile
from fastapi import Response
from pydantic import BaseModel, field_validator

from records.api import clients_uploads, common_utils
from records.config.shared_config import SharedConfig
from records.context import context as ctx
from records.db import api as db_api, base, models
from records.services import client_upload_service, detections
from records.services.constants import UploadObjectType
from records.utils import json_utils, utils


class AssigneeManagerID(BaseModel):
    id: str


class ClientTaxReportCreate(BaseModel):
    type: str
    fiscal_year: str
    form_info: Optional[str] = None
    filed_by: Optional[str] = None
    assignee_manager: Optional[AssigneeManagerID] = None
    assignee_manager_id: Optional[str] = None
    status: Optional[str] = 'DOCUMENT_COLLECTION'  # DOCUMENT_COLLECTION, PREPARATION, REVIEW, AWAITING_SIGNATURE, FILED, COMPLETED
    note: Optional[str] = None
    file_id: Optional[str] = None
    is_burning: Optional[bool] = False
    due_date: Optional[datetime.datetime] = None
    filed_date: Optional[datetime.datetime] = None

    _date_validator = field_validator(
        *['due_date', 'filed_date'],
        mode='before'
    )(json_utils.date_validator)


class ClientTaxReportUpdate(BaseModel):
    type: Optional[str] = None
    fiscal_year: Optional[str] = None
    form_info: Optional[str] = None
    filed_by: Optional[str] = None
    assignee_manager: Optional[AssigneeManagerID] = None
    assignee_manager_id: Optional[str] = None
    status: Optional[str] = None
    note: Optional[str] = None
    file_id: Optional[str] = None
    is_burning: Optional[bool] = None
    due_date: Optional[datetime.datetime] = None
    filed_date: Optional[datetime.datetime] = None

    _date_validator = field_validator(
        *['due_date', 'filed_date'],
        mode='before'
    )(json_utils.date_validator)


class TaxReportFileUpdate(BaseModel):
    description: Optional[str] = None
    doc_type: Optional[str] = None


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'tax_reports'))

    router.add_api_route("", list_client_tax_reports, methods=['GET'], name='List client tax reports')
    router.add_api_route("", create_client_tax_report, methods=['POST'], name='Create client tax report')
    router.add_api_route("/{tax_report_id}", get_client_tax_report, methods=['GET'], name='Get client tax report')
    router.add_api_route("/{tax_report_id}", update_client_tax_report, methods=['PUT'], name='Update client tax report')
    router.add_api_route("/{tax_report_id}", delete_client_tax_report, methods=['DELETE'], name='Delete client tax report')

    # Tax report files API
    router.add_api_route("/{tax_report_id}/files", list_client_tax_report_files, methods=['GET'], name='List client tax report files')
    router.add_api_route("/{tax_report_id}/files", upload_client_tax_report_file, methods=['POST'], name='Create client tax report file')
    router.add_api_route("/{tax_report_id}/files/{file_id}", get_client_tax_report_file, methods=['GET'], name='Get client tax report file')
    router.add_api_route("/{tax_report_id}/files/{file_id}", update_client_tax_report_file, methods=['PUT'], name='Update client tax report file')
    router.add_api_route("/{tax_report_id}/files/{file_id}", delete_client_tax_report_file, methods=['DELETE'], name='Delete client tax report file')
    router.add_api_route("/{tax_report_id}/files/{file_id}/download", get_client_tax_report_file_download, methods=['GET'], name='Download client tax report file')

    return router


async def list_client_tax_reports(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    # session: AsyncSession = Depends(get_session)
):
    async with base.session_context():
        tax_reports = await db_api.list_client_tax_reports(client_id=client_id, order=order, desc=desc, q=q)
        tax_report_dicts = [tax_report.to_dict() for tax_report in tax_reports]
        manager_ids = list(set([tax_report['assignee_manager_id'] for tax_report in tax_report_dicts if tax_report['assignee_manager_id']]))
        managers = await db_api.list_managers_by_id(ids=manager_ids)
        manager_map = {m.id: m.to_dict() for m in managers}

        attaches = await db_api.list_upload_attached_objects_by_object(
            UploadObjectType.TAX_REPORT, object_ids=[str(tax_report.id) for tax_report in tax_reports]
        )
        attaches_by_tax_report = collections.defaultdict(list)
        for attach in attaches:
            attaches_by_tax_report[attach.object_id].append(attach)

        upload_ids = [a.upload_id for a in attaches]
        if upload_ids:
            uploads = await db_api.list_client_uploads_all(ids=upload_ids)
            upload_map = {u.id: u for u in uploads}
        else:
            upload_map = {}

        # need to map tax report to a list of files:
        # 1 tax report -> * uploads
        # 1 upload = 1 file
        file_ids = [upload_map[attach.upload_id].file_id for attach in attaches]
        if file_ids:
            files = await db_api.list_client_files_all(ids=file_ids)
            file_map = {f.id: f.to_dict() for f in files}
        else:
            file_map = {}

        for tax_report in tax_report_dicts:
            tax_report['assignee_manager'] = manager_map.get(tax_report['assignee_manager_id'])
            tax_report['files'] = [
                file_map[upload_map[attach.upload_id].file_id]
                for attach in attaches_by_tax_report[str(tax_report['id'])]
            ]

    return {'items': tax_report_dicts, 'count': len(tax_report_dicts)}


async def get_client_tax_report(client_id: str, tax_report_id: int):
    async with base.session_context():
        client_tax_report_db = await db_api.get_client_tax_report_by_id(tax_report_id)
        if not client_tax_report_db:
            raise HTTPException(status_code=404, detail="Client tax_report not found")
        if client_tax_report_db.client_id != client_id:
            raise HTTPException(status_code=403, detail="Tax report does not belong to this client")

        tax_report_dict = client_tax_report_db.to_dict()
        tax_report_dict['assignee_manager'] = common_utils.to_dict(await db_api.get_manager_by_id(
            tax_report_dict['assignee_manager_id'], notfoundok=True
        ))
        if tax_report_dict['assignee_manager']:
            tax_report_dict['assignee_manager']['user'] = common_utils.to_dict(await db_api.get_user_by_id(
                tax_report_dict['assignee_manager']['user_id'], notfoundok=True
            ))

        tax_report_dict['files'] = (await list_client_tax_report_files(client_id, tax_report_id))['items']

    return tax_report_dict


async def create_client_tax_report(client_id: str, tax_report: ClientTaxReportCreate):
    client_tax_report_dict = tax_report.model_dump(exclude_unset=True)
    client_tax_report_dict['client_id'] = client_id

    mgr_obj = tax_report.assignee_manager
    cur_mgr = ctx.current_manager()

    client_tax_report_dict['assignee_manager_id'] = (mgr_obj.id if mgr_obj else None) or tax_report.assignee_manager_id or cur_mgr.id
    client_tax_report_dict.pop('assignee_manager', None)

    db_client_tax_report = await db_api.create_client_tax_report(client_tax_report_dict)
    return db_client_tax_report


async def update_client_tax_report(client_id: str, tax_report_id: int, tax_report: ClientTaxReportUpdate):
    client_tax_report_db = await db_api.get_client_tax_report_by_id(tax_report_id)
    if not client_tax_report_db:
        raise HTTPException(status_code=404, detail="Client tax_report not found")
    if client_tax_report_db.client_id != client_id:
        raise HTTPException(status_code=403, detail="Tax report does not belong to this client")

    update_dict = tax_report.model_dump(exclude_unset=True)

    mgr_defined = 'assignee_manager' in update_dict
    mgr_id_defined = 'assignee_manager_id' in update_dict
    if mgr_defined:
        mgr_obj = update_dict.pop('assignee_manager')
        if not mgr_obj:
            update_dict['assignee_manager_id'] = None
        else:
            update_dict['assignee_manager_id'] = mgr_obj['id']
    if mgr_id_defined:
        update_dict['assignee_manager_id'] = update_dict.pop('assignee_manager_id')

    if not update_dict:
        raise HTTPException(400, 'No fields to update')

    updated_tax_report = await db_api.update_client_tax_report(client_tax_report_db, update_dict)

    tax_report_dict = updated_tax_report.to_dict()
    tax_report_dict['assignee_manager'] = await db_api.get_manager_by_id(
        tax_report_dict['assignee_manager_id'], notfoundok=True
    )
    return tax_report_dict


async def delete_client_tax_report(client_id: str, tax_report_id: int):
    async with base.session_context():
        client_tax_report_db = await db_api.get_client_tax_report_by_id(tax_report_id)
        if not client_tax_report_db:
            raise HTTPException(status_code=404, detail="Client tax_report not found")
        if client_tax_report_db.client_id != client_id:
            raise HTTPException(status_code=403, detail="Tax report does not belong to this client")

        # Get all uploads attached to this tax report first (before deleting the attached objects)
        upload_attaches = await db_api.list_upload_attached_objects_by_object(UploadObjectType.TAX_REPORT, str(tax_report_id))
        upload_ids = [u.upload_id for u in upload_attaches]

        if upload_ids:
            uploads = await db_api.list_client_uploads_all(ids=upload_ids)
            for upload in uploads:
                # Use the client upload service to properly delete uploads with all cleanup
                await client_upload_service.get_client_upload_service().delete_client_upload(
                    upload_db=upload,
                    delete_file_db=True,
                    deletion_behavior=models.ClientUploadDeletionBehavior.DEFAULT
                )

        # Delete any remaining upload attached objects that reference this tax report
        await db_api.delete_upload_attached_objects_by_object(UploadObjectType.TAX_REPORT, str(tax_report_id))

        # Delete the tax report itself
        await db_api.delete_client_tax_report(tax_report_id)

    return Response(status_code=204)


async def list_client_tax_report_files(
    client_id: str,
    tax_report_id: int,
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
):
    # List client uploads with attachment to this tax report id (object_type='tax report', object_id=tax_report_id)
    # Then take the files by ids
    async with base.session_context():
        upload_attaches = await db_api.list_upload_attached_objects_by_object(UploadObjectType.TAX_REPORT, str(tax_report_id))
        upload_ids = [u.upload_id for u in upload_attaches]
        if upload_ids:
            uploads = await db_api.list_client_uploads_all(ids=upload_ids)
            file_ids = [u.file_id for u in uploads]
            files = await db_api.list_client_files_all(ids=file_ids)
            file_dicts = [f.to_dict() for f in files]
        else:
            file_dicts = []

    return {'items': file_dicts, 'count': len(file_dicts)}


async def upload_client_tax_report_file(client_id: str, tax_report_id: int, file: UploadFile):
    """Upload a file as a client upload with attachments to client & tax report"""
    async with base.session_context():
        # Validate that the tax report belongs to the client
        client_tax_report_db = await db_api.get_client_tax_report_by_id(tax_report_id)
        if not client_tax_report_db:
            raise HTTPException(status_code=404, detail="Tax report not found")
        if client_tax_report_db.client_id != client_id:
            raise HTTPException(status_code=403, detail="Tax report does not belong to this client")

        # Validate that the client exists
        client_db = await db_api.get_client_by_id(client_id)
        if not client_db:
            raise HTTPException(status_code=404, detail="Client not found")

        # Read file data and prepare file metadata
        file_data = file.file.read()
        file_name = file.filename
        manager = ctx.current_manager()

        file_dict = {
            'client_id': client_id,
            'name': file_name,
            'size': len(file_data),
            'hash': utils.hash_sha256(file_data),
            'file_type': mimetypes.guess_type(file_name)[0],
            'manager_id': manager.id,
            'description': f"Tax report file for {client_tax_report_db.type} {client_tax_report_db.fiscal_year}",
            'from_upload': True,
        }

        # Check for duplicate files
        existing_file = await db_api.get_client_file_by_hash(client_id, file_dict['hash'], notfoundok=True)
        if existing_file:
            raise HTTPException(409, f"File already exists with name {existing_file.name}")

        # Create file and save to storage
        file_db = await db_api.create_client_file(file_dict)
        await SharedConfig().file_manager.save_file(detections.get_upload_file_path(file_db), file_data)

        # Create upload and start processing
        upload_db = await client_upload_service.get_client_upload_service().create_upload(
            file_db=file_db,
            file_data=file_data,
            manager_id=manager.id
        )

        # Create attached objects for both client and tax report
        attached_objects = [{
            'upload_id': upload_db.id,
            'object_type': UploadObjectType.CLIENT,
            'object_id': client_id
        }, {
            'upload_id': upload_db.id,
            'object_type': UploadObjectType.TAX_REPORT,
            'object_id': str(tax_report_id)
        }]

        # Create the attached objects
        for attached_obj_data in attached_objects:
            await db_api.create_client_upload_attached_object(attached_obj_data)

        await client_upload_service.get_client_upload_service().apply_upload(
            upload_db.id, skip_override=True, skip_client=client_db.to_dict()
        )

    # Return the upload with file information
    upload_dict = upload_db.to_dict()
    upload_dict['file'] = file_db.to_dict()

    return upload_dict


async def get_client_tax_report_file(client_id: str, tax_report_id: int, file_id: str):
    file_db = await db_api.get_client_file_by_id(file_id)
    if file_db.client_id != client_id:
        raise HTTPException(status_code=403, detail="File does not belong to this client")

    file_dict = file_db.to_dict()
    # Generate download link
    file_dict['download_link'] = await client_upload_service.get_client_upload_service().generate_download_link(file_db.id)

    return file_dict


async def update_client_tax_report_file(client_id: str, tax_report_id: int, file_id: str, file_update: TaxReportFileUpdate):
    async with base.session_context():
        file_db = await db_api.get_client_file_by_id(file_id)
        if file_db.client_id != client_id:
            raise HTTPException(status_code=403, detail="File does not belong to this client")

        update_dict = file_update.model_dump(exclude_unset=True)
        if not update_dict:
            raise HTTPException(400, 'No fields to update')

        if 'doc_type' in update_dict:
            update_meta = {'type': update_dict['doc_type']}
            update_dict['meta'] = file_db.meta or {}
            update_dict['meta'].update(update_meta)

        file_db = await db_api.update_client_file(file_db, update_dict)

    return file_db.to_dict()


async def get_client_tax_report_file_download(client_id: str, tax_report_id: int, file_id: str, inline: bool = False):
    file_db = await db_api.get_client_file_by_id(file_id)
    if file_db.client_id != client_id:
        raise HTTPException(status_code=403, detail="File does not belong to this client")

    return await clients_uploads.download_file(file_db=file_db, inline=inline)


async def delete_client_tax_report_file(client_id: str, tax_report_id: int, file_id: str):
    async with base.session_context():
        file_db = await db_api.get_client_file_by_id(file_id)
        if file_db.client_id != client_id:
            raise HTTPException(status_code=403, detail="File does not belong to this client")

        # Get the client upload associated with this file
        uploads = await db_api.list_client_uploads_by_file_id(file_id)

        # Delete the upload and all its associated data (including attached objects)
        for upload in uploads:
            await client_upload_service.get_client_upload_service().delete_client_upload(
                upload_db=upload,
                delete_file_db=False,  # We'll delete the file separately
                deletion_behavior=models.ClientUploadDeletionBehavior.DEFAULT
            )

        # Delete the file with proper external resource cleanup
        # from records.services import file_processing
        # await file_processing.delete_file(file_db)
        await db_api.delete_client_file(file_id)

    return Response(status_code=204)
