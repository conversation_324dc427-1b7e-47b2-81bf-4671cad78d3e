import os
import datetime
from typing import Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, field_validator

from records.utils import json_utils

from records.context import context as ctx
from records.db import api as db_api


class RegistrationCreate(BaseModel):
    # Client connection part
    registration_type: Optional[str] = None
    renewal_date: Optional[datetime.datetime] = None
    phone: Optional[str] = None
    paid_by: Optional[str] = None
    note: Optional[str] = None

    _date_validator = field_validator(
        *['renewal_date'],
        mode='before'
    )(json_utils.date_validator)


class RegistrationUpdate(BaseModel):
    pass


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'registrations'))

    router.add_api_route("", list_registrations, methods=['GET'], name='List client registrations')
    router.add_api_route("", create_client_registration, methods=['POST'], name='Create client registration')
    router.add_api_route("/{registration_id}", get_client_registration, methods=['GET'], name='Get client registration')
    router.add_api_route("/{registration_id}", update_client_registration, methods=['PUT'], name='Update client registration')
    router.add_api_route("/{registration_id}", delete_client_registration, methods=['DELETE'], name='Delete client registration')

    return router


async def list_registrations(
    client_id: str,
    # session: AsyncSession = Depends(get_session)
):
    registrations = await db_api.list_client_registrations(
        client_id=client_id,
    )
    return {'items': registrations}


async def get_client_registration(client_id: str, registration_id: int):
    registration_db = await db_api.get_client_registration_by_id(registration_id)
    return registration_db


async def create_client_registration(client_id: str, registration: RegistrationCreate):
    formdata = await ctx.request.value.form()

    if 'registration' not in formdata:
        raise HTTPException(400, f'Form-data must include "registration"')

    form_registration = formdata['registration']
    data = form_registration.registration.read()
    registration_dict = {
        'client_id': client_id,
        'registration': data,
        'registration_name': form_registration.registrationname,
        'registration_type': '',
        # 'manager': ctx.current_user().id,
        # 'manager_id': ctx.current_user().id,
    }

    db_client = await db_api.create_client_registration(registration_dict)
    return db_client


async def update_client_registration(client_id: str, registration_id: int, client: RegistrationUpdate):
    registration_db = await db_api.get_client_registration_by_id(registration_id)
    updated_client = await db_api.update_client_registration(registration_db, client.dict())
    return updated_client


async def delete_client_registration(client_id: str, registration_id: int):
    registration_db = await db_api.get_client_registration_by_id(registration_id)
    await db_api.delete_client_registration(registration_id)
    return None
