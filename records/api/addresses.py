import os

from fastapi import APIRouter, Depends

from records.api import auth_utils, common_utils
from records.db import api as db_api
from records.services.client_structs import AddressCreate
from records.services.client_structs import AddressUpdate


def get_router(prefix='/'):
    router = APIRouter(
        prefix=os.path.join(prefix, 'addresses'),
        dependencies=[Depends(auth_utils.basic_access)]
    )

    router.add_api_route("", list_addresses, methods=['GET'], name='List addresses')
    router.add_api_route("", create_address, methods=['POST'], name='Create address')
    router.add_api_route("/{address_id}", get_address, methods=['GET'], name='Get address')
    router.add_api_route("/{address_id}", update_address, methods=['PUT'], name='Update address')
    router.add_api_route("/{address_id}", delete_address, methods=['DELETE'], name='Delete address')

    return router


async def list_addresses(
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    # session: AsyncSession = Depends(get_session)
):
    addresses, count = await db_api.list_addresses(limit=limit, page=page, order=order, desc=desc, q=q)
    addresses_dict = [a.to_dict() for a in addresses]
    for addr in addresses_dict:
        addr['full_title'] = addr['full_address']
    return {'items': addresses_dict, 'count': count, 'limit': limit, 'page': page}


async def get_address(address_id: str):
    db_address = await db_api.get_address_by_id(address_id)
    db_address_dict = db_address.to_dict()
    db_address_dict['full_title'] = db_address_dict['full_address']

    return db_address_dict


async def create_address(address: AddressCreate):
    address_dict = address.model_dump(exclude_unset=True)
    address_dict['full_address'] = common_utils.build_full_address(address_dict)
    db_address = await db_api.create_address(address_dict)
    return db_address


async def update_address(address_id: str, address: AddressUpdate):
    db_address = await db_api.get_address_by_id(address_id)

    address_dict = address.model_dump(exclude_none=True)
    address_dict['full_address'] = common_utils.build_full_address(address_dict)

    updated_address = await db_api.update_address(db_address, address_dict)
    return updated_address


async def delete_address(address_id: str):
    db_address = await db_api.get_address_by_id(address_id)
    await db_api.delete_address(address_id)
    return None
