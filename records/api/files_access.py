import os
import logging
import os

from fastapi import APIRouter, HTTPException
from starlette.responses import StreamingResponse

from records.api import auth_utils
from records.cache import global_cache
from records.config.shared_config import SharedConfig
from records.context import context as ctx
from records.db import api as db_api
from records.services import detections

logger = logging.getLogger(__name__)


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'file_access'))

    router.add_api_route("/download", get_client_file_download, methods=['GET'], name='Download file temporary link')

    return router


async def get_client_file_download(token: str, inline: bool = False):
    try:
        cache_key = f'file_access/{token}'
        file_id = await global_cache.get_cache().aget(cache_key)

        if not file_id:
            raise HTTPException(404, 'Token is not valid')

        file_db = await db_api.get_client_file_by_id(file_id)
        file_path = detections.get_upload_file_path(file_db)

        read_file = await SharedConfig().file_manager.read_file(file_path)
        stream = SharedConfig().file_manager.get_data_generator(read_file)
        file_name = file_db.name.encode('latin-1', 'replace').decode('latin-1')
        content_disposition = f'attachment; filename="{file_name}"' if not inline else f'inline; filename="{file_name}"'
        content_length = file_db.size
    finally:
        # Cleanup session if temporary
        ses = ctx.current_session()
        if ses and ses.user_id >= auth_utils.MIN_ID:
            # logger.info(f'Deleting session {ses.id} for user {ses.user_id}')
            await db_api.delete_session(ses.id)

    return StreamingResponse(
        stream,
        media_type=file_db.file_type,
        headers={
            'Content-Disposition': content_disposition,
            'Content-Length': str(content_length)
        }
    )
