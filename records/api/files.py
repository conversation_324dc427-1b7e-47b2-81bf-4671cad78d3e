import mimetypes
import os
from typing import Optional

from fastapi import APIRouter, HTTPException
from fastapi import Form
from fastapi import Response
from fastapi import UploadFile
from fastapi.responses import ORJSONResponse
from pydantic import BaseModel
from starlette.responses import StreamingResponse

from records.context import context as ctx
from records.db import api as db_api, base
from records.services import file_processing
from records.utils import utils


class ClientCreate(BaseModel):
    client: str
    description: str


class ClientUpdate(BaseModel):
    client: str
    description: str


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'files'))

    router.add_api_route("", list_files, methods=['GET'], name='List client files')
    router.add_api_route("", upload_client_file, methods=['POST'], name='Create client file')
    router.add_api_route("/{file_id}", get_client_file, methods=['GET'], name='Get client file')
    router.add_api_route("/{file_id}/download", get_client_file_download, methods=['GET'], name='Download client file')
    router.add_api_route("/{file_id}", update_client_file, methods=['PUT'], name='Update client file')
    router.add_api_route("/{file_id}", delete_client_file, methods=['DELETE'], name='Delete client file')

    return router


async def list_files(
    client_id: str,
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    # session: AsyncSession = Depends(get_session)
):
    async with base.session_context():
        files, count = await db_api.list_client_files(
            client_id=client_id, limit=limit, page=page, order=order, desc=desc, q=q
        )
        managers = await db_api.list_managers_by_id(ids=list(set([f.manager_id for f in files if f.manager_id])))
        manager_map = {m.id: m.to_dict() for m in managers}
        file_dicts = [f.to_dict() for f in files]
        if files:
            internal_external_map = await file_processing.list_external_files(client_id)
        else:
            internal_external_map = {}

    for file_dict in file_dicts:
        file_dict['status'] = internal_external_map.get(file_dict['id'], {}).get('status')
        file_dict['manager'] = manager_map.get(file_dict['manager_id'])

    return ORJSONResponse({
        'items': file_dicts, 'count': count, 'limit': limit, 'page': page
    })


async def get_client_file(client_id: str, file_id: str):
    async with base.session_context():
        file_db = await db_api.get_client_file_by_id(file_id)
        manager = await db_api.get_manager_by_id(file_db.manager_id) if file_db.manager_id else None
        try:
            external_file = await file_processing.get_external_file(file_db)
        except:
            external_file = {}

    file_dict = file_db.to_dict()
    file_dict['status'] = external_file['status'] if external_file else None

    # Update file meta if not yet set
    if external_file.get('meta') and not file_dict.get('meta'):
        await db_api.update_client_file(
            file_db,
            {'meta': external_file['meta'], 'doc_type': external_file['meta'].get('type')}
        )

    file_dict['manager'] = manager.to_dict() if manager else None
    return ORJSONResponse(file_dict)


async def get_client_file_download(client_id: str, file_id: str, inline: bool = False):
    file_db = await db_api.get_client_file_by_id(file_id)

    stream = await file_processing.download_file(file_db, inline=inline)
    content_disposition = stream.headers.get('Content-Disposition')
    content_length = stream.headers.get('Content-Length')
    return StreamingResponse(
        stream,
        media_type=file_db.file_type,
        headers={
            'Content-Disposition': content_disposition,
            'Content-Length': content_length
        }
    )


async def upload_client_file(
    client_id: str,
    file: UploadFile,
    description: Optional[str] = Form(None),
    doc_type: Optional[str] = Form(None),
):
    async with base.session_context():
        client = await db_api.get_client_by_id(client_id)
        manager = ctx.current_manager()
        file_data = file.file.read()
        file_name = file.filename

        file_dict = {
            'client_id': client.id,
            'name': file_name,
            'size': len(file_data),
            'hash': utils.hash_sha256(file_data),
            'file_type': mimetypes.guess_type(file_name)[0],
            'manager_id': manager.id,
            'doc_type': doc_type,
            'description': description,
            # 'date': base.now(),
        }

        # Check for duplicate
        existing_file = await db_api.get_client_file_by_hash(client.id, file_dict['hash'], notfoundok=True)
        if existing_file:
            raise HTTPException(
                409, f"File already exists with name {existing_file.name} and id {existing_file.id}"
            )

        file_db = await db_api.create_client_file(file_dict)

    await file_processing.process_file(
        client, file_name, file_data, manager_id=manager.id, file_db=file_db
    )
    file_dict = file_db.to_dict()
    file_dict['status'] = {'status': 'PROCESSING'}

    return ORJSONResponse(file_dict)


async def update_client_file(
    client_id: str,
    file_id: str,
    file: Optional[UploadFile] = None,
    description: Optional[str] = Form(None),
    doc_type: Optional[str] = Form(None)
):
    if not description and not file:
        raise HTTPException(422, "Nothing to update")

    client = await db_api.get_client_by_id(client_id)
    file_db = await db_api.get_client_file_by_id(file_id)

    if description:
        file_db = await db_api.update_client_file(file_db, {'description': description})
    if file:
        file_data = file.file.read()
        file_name = file.filename
        update_data = {
            'name': file_name,
            'hash': utils.hash_sha256(file_data),
            'file_type': mimetypes.guess_type(file_name)[0],
        }
        if doc_type:
            update_data['doc_type'] = doc_type
        if description:
            update_data['description'] = description
        file_db = await db_api.update_client_file(file_db, update_data)
        await file_processing.process_file(
            client, file_name, file_data, file_db=file_db
        )

    return ORJSONResponse(file_db.to_dict())


async def delete_client_file(client_id: str, file_id: str):
    file_db = await db_api.get_client_file_by_id(file_id)
    async with base.session_context():
        await file_processing.delete_file(file_db)
        await db_api.delete_client_file(file_id)

    return Response(status_code=204)
