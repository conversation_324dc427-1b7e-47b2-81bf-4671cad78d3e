import os
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Response
from pydantic import BaseModel

from records.api import auth_utils
from records.context import context as ctx
from records.db import api as db_api, base
from records.e_mail import send
from records.policies import policies
from records.utils import utils


class UserCreate(BaseModel):
    login: str
    name: str
    password: Optional[str] = None
    info: Optional[dict] = None


class UserUpdate(BaseModel):
    # login: str
    name: Optional[str]
    info: Optional[dict] = None


class ManagerBase(BaseModel):
    phone: Optional[str] = None
    role_name: Optional[str] = None


class ManagerCreate(ManagerBase):
    user: UserCreate


class ManagerUpdate(ManagerBase):
    user: Optional[UserUpdate] = None


def get_router(prefix='/'):
    router = APIRouter(
        prefix=os.path.join(prefix, 'managers'),
        dependencies=[Depends(auth_utils.owner_access)]
    )

    router.add_api_route("/roles", list_roles, methods=['GET'], name='Get roles')

    router.add_api_route("", list_managers, methods=['GET'], name='List managers')
    router.add_api_route("", create_manager, methods=['POST'], name='Create manager')
    router.add_api_route("/{manager_id}", get_manager, methods=['GET'], name='Get manager')
    router.add_api_route("/{manager_id}", update_manager, methods=['PUT'], name='Update manager')
    router.add_api_route("/{manager_id}", delete_manager, methods=['DELETE'], name='Delete manager')

    return router


async def list_roles():
    roles = policies.get_roles()
    for r in roles:
        r['permissions'] = policies.decode_permissions(r['permissions'])
    
    return {'items': roles}


async def list_managers(
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    # session: AsyncSession = Depends(get_session)
):
    managers, count = await db_api.list_managers(limit=limit, page=page, order=order, desc=desc, q=q)
    users = await db_api.list_users([m.user_id for m in managers])
    user_map = {u.id: u for u in users}
    manager_dicts = [m.to_dict() for m in managers]
    for manager in manager_dicts:
        manager['permissions'] = policies.decode_permissions(manager['permissions'])
        manager['user'] = user_map.get(manager['user_id']).to_dict() if manager['user_id'] in user_map else None

    return {'items': manager_dicts, 'count': count, 'limit': limit, 'page': page}


async def get_manager(manager_id: str):
    db_manager = await db_api.get_manager_by_id(manager_id)
    user = None
    if db_manager.user_id:
        user = await db_api.get_user_by_id(db_manager.user_id)

    manager_dict = db_manager.to_dict()
    manager_dict['user'] = user.to_dict() if user else None

    return manager_dict


async def create_manager(manager: ManagerCreate):
    manager_dict = manager.model_dump(exclude_unset=True)
    manager_user = manager_dict.pop('user')
    async with base.session_context() as session:
        # Check if manager exists
        password = manager_user.pop('password', None)
        info = manager_user.pop('info', {})
        phone = manager_dict.get('phone', None)
        if phone:
            info['phone'] = phone
        if not password:
            password = auth_utils.generate_temp_password()

        db_user = await db_api.create_user({
            'login': manager_user['login'],
            'password': password,
            'name': manager_user['name'],
            'info': info,
            'confirmed': True,
        })
        await session.flush()

        manager_dict['user_id'] = db_user.id
        manager_dict['email'] = db_user.login
        manager_dict['title'] = db_user.name
        role = policies.get_role_by_name(manager_dict.get('role_name') or 'user')
        manager_dict['role_name'] = role['name']
        manager_dict['permissions'] = role['permissions']

        db_manager = await db_api.create_manager(manager_dict)

        # Send password to user
        base_url = utils.get_base_url(ctx.request.value)
        send.send_confirmation(db_user, None, base_url, new_password=password)

    manager_dict = db_manager.to_dict()
    manager_dict['permissions'] = policies.decode_permissions(role['permissions'])
    manager_dict['user'] = db_user.to_dict()
    return manager_dict


async def update_manager(manager_id: str, manager: ManagerUpdate):
    db_manager = await db_api.get_manager_by_id(manager_id)
    user = await db_api.get_user_by_id(db_manager.user_id)

    manager_dict = manager.dict(exclude_unset=True)
    manager_user = manager_dict.pop('user', None)
    manager_dict.pop('password', None)

    if manager_dict.get('phone'):
        user_info = user.info or {}
        user_info['phone'] = manager_dict.pop('phone')
        manager_user['info'] = user_info

    if manager_user:
        user = await db_api.update_user(user, manager_user)
        if manager_user.get('name'):
            manager_dict['title'] = manager_user['name']

    if manager_dict.get('role_name'):
        role = policies.get_role_by_name(manager_dict['role_name'])
        if not role:
            raise HTTPException(400, 'Role not found')
        manager_dict['role_name'] = role['name']
    else:
        role = policies.get_role_by_name(db_manager.role_name)
    if not manager_dict:
        raise HTTPException(400, 'No data to update')

    manager_dict['permissions'] = role['permissions']
    updated_manager = await db_api.update_manager(db_manager, manager_dict)

    role = policies.get_role_by_name(updated_manager.role_name)

    manager_dict = updated_manager.to_dict()
    manager_dict['permissions'] = policies.decode_permissions(role['permissions'])
    manager_dict['user'] = user.to_dict()
    return manager_dict


async def delete_manager(manager_id: str):
    db_manager = await db_api.get_manager_by_id(manager_id)

    async with base.session_context():
        await db_api.delete_manager(manager_id)
        await db_api.delete_user(db_manager.user_id)

    return Response(status_code=204)
