import logging
from typing import Optional
from urllib import parse

from fastapi import APIRouter, HTTPException
from starlette.responses import RedirectResponse, JSONResponse

from records.api import auth_utils, common_utils
from records.context import context as ctx
from records.db import api as db_api, base
from records.db import models
from records.e_mail import send
from records.exceptions import NotFoundException
from records.policies import policies
from records.utils import utils

logger = logging.getLogger(__name__)


def get_router():
    auth_router = APIRouter(prefix='/confirm')

    auth_router.add_api_route("", confirm_email, methods=['GET'], name='Confirm email')
    auth_router.add_api_route("/reset", confirm_reset, methods=['GET'], name='Confirm reset')
    auth_router.add_api_route("/invite_info", invite_info, methods=['GET'], name='Invite info')
    auth_router.add_api_route("/accept_invite", confirm_invite, methods=['GET'], name='Confirm invite')

    return auth_router


async def confirm_email(token: str):
    async with base.session_context():
        confirm = await db_api.get_user_confirm(token)
        user = await db_api.get_user_by_id(confirm.user_id)

        await db_api.update_user(user, {'confirmed': True})
        await db_api.delete_user_confirm(confirm.id)

        base_url = utils.get_base_url(ctx.request.value)

    msg = 'Your email has been successfully verified. Thank you!'
    return RedirectResponse(f'{base_url}/message?text={parse.quote(msg)}')


async def confirm_reset(token: str):
    confirm = await db_api.get_user_confirm(token)

    ses = ctx.current_session()
    ses.user_id = confirm.user_id
    await auth_utils.login_session(ses)

    future_pass = utils.generate_pass_from_uuid(confirm.id)
    u = await db_api.get_user_by_id(ses.user_id)
    u = await db_api.update_user(u, {'password': future_pass})
    await db_api.delete_user_confirm(confirm.id)

    base_url = utils.get_base_url(ctx.request.value)

    msg = 'Your have been logged in.'
    return RedirectResponse(f'{base_url}/message?text={parse.quote(msg)}')


async def invite_info(token: str):
    invite = await db_api.get_user_invite(token)
    invite_dict = invite.to_dict()

    to_user_login = invite.to_user_login
    if invite.to_user_id:
        to_user = await db_api.get_user_by_id(invite.to_user_id)
        to_user_login = to_user.login

    if invite.to_group_id:
        group = await db_api.get_group_by_id(invite.to_group_id)
        invite_dict['to_group_name'] = group.name
    if invite.to_workspace_id:
        ws = await db_api.get_workspace_by_id(invite.to_workspace_id)
        invite_dict['to_workspace_name'] = ws.name
        invite_dict['to_workspace_display_name'] = ws.display_name
        org = await db_api.get_org_by_id(ws.org_id)
    else:
        org = ctx.current_org()
        org_user = await db_api.get_org_user(org.id, user_id=invite.to_user_id, user_login=invite.to_user_login)
        if not org_user:
            raise NotFoundException(
                'Wrong organization. You have been invited to a different organization, please proceed'
                ' to the URL of the correct organization.'
            )
        role = db_api.get_role_by_id(org_user.role_id)
        invite_dict['to_organization_permissions'] = policies.decode_permissions(role['permissions'], org=True)

    from_user = await db_api.get_user_by_id(invite.from_user_id)
    invite_dict['to_user_login'] = to_user_login
    invite_dict['from_user_login'] = from_user.login

    invite_dict['to_organization_name'] = org.name
    invite_dict['to_organization_display_name'] = org.display_name

    return JSONResponse(content=invite_dict, status_code=200)


async def confirm_invite(token: str, do_redirect: Optional[bool] = False):
    invite = await db_api.get_user_invite(token)
    sess = ctx.current_session()

    if invite.to_user_id != 0 and sess.user_id != invite.to_user_id:
        if not auth_utils.ephemeral_user(sess):
            raise HTTPException(
                403,
                'You are logged in as another user, please log in as a user who received the invitation to accept it'
            )

    group_user = None
    if invite.to_group_id:
        group_user = await get_invite_group(invite)
    await perform_invite(sess, invite, group_user)

    await common_utils.clear_cache_for_session(sess.id)
    if do_redirect:
        base_url = utils.get_base_url(ctx.request.value)

        msg = 'Your invitation has been successfully accepted. Thank you!'
        return RedirectResponse(f'{base_url}/message?text={parse.quote(msg)}')
    else:
        return JSONResponse(status_code=200, content={'status': 'OK'})


async def get_invite_group(invite: models.UserInvite):
    if invite.to_user_id != 0:
        group_user = await db_api.get_group_user(invite.to_group_id, invite.to_user_id)
    else:
        group_user = await db_api.get_group_user_by_login(invite.to_group_id, invite.to_user_login)

    return group_user


async def perform_invite(sess, invite: models.UserInvite, group_user, do_login=True):
    if invite.to_user_id == 0:
        user_login = invite.to_user_login
        user = await db_api.get_user_by_login(user_login, notfoundok=True)
        if not user:
            # Register user
            new_password = utils.generate_pass_from_uuid(utils.generate_unicode_uuid())
            values = {
                'login': user_login,
                'password': new_password,
                'confirmed': True,
            }
            user = await db_api.create_user(values)

            # confirm = await db_api.create_user_confirm({
            #     'id': str(uuid.uuid4()),
            #     'user_id': u.id,
            #     'expiry_time': datetime.datetime.now() + datetime.timedelta(hours=24)
            # })
            base_url = utils.get_base_url(ctx.request.value)

            send.send_confirmation(user, confirm=None, base_url=base_url, new_password=new_password)

        if sess.user_id != user.id and do_login:
            sess.user_id = user.id
            await auth_utils.login_session(sess)

    else:
        user = await db_api.get_user_by_id(invite.to_user_id)
        user_login = user.login
        if sess.user_id != user.id and do_login:
            sess.user_id = user.id
            await auth_utils.login_session(sess)

    if group_user:
        await db_api.update_group_user(
            group_user,
            {'confirmed': True, 'user_id': user.id, 'user_login': None},
        )
    else:
        # confirm all groups added so far
        await db_api.update_group_users(
            user_id=user.id,
            new_values={'confirmed': True, 'user_id': user.id, 'user_login': None},
        )
        await db_api.update_group_users(
            user_login=user_login,
            new_values={'confirmed': True, 'user_id': user.id, 'user_login': None},
        )

    # Add user to an Org if he is not there
    await common_utils.ensure_user_in_org(ctx.current_org().id, user_login, user.id)

    await db_api.delete_user_invite(invite.id)
