import os
import logging
from typing import Optional
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
import io

from records.db import api as db_api, base
from records.services.client_report_generator import ClientReportGenerator

logger = logging.getLogger(__name__)


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'reports'))

    router.add_api_route("/download_main_report", download_main_report, methods=['GET'], name='Download main report')
    # router.add_api_route("", list_client_reports, methods=['GET'], name='List client reports')
    # router.add_api_route("", create_client_report, methods=['POST'], name='Create client report')
    # router.add_api_route("/{report_id}", get_client_report, methods=['GET'], name='Get client report')
    # router.add_api_route("/{report_id}", update_client_report, methods=['PUT'], name='Update client report')
    # router.add_api_route("/{report_id}", delete_client_report, methods=['DELETE'], name='Delete client report')

    return router


async def download_main_report(client_id: str, format: str = 'xlsx', inline: bool = False, enhanced_styling: bool = True):
    """
    Generate and download a client report in the specified format.

    Args:
        client_id: The ID of the client to generate the report for
        format: The output format ('xlsx', 'csv', 'pdf')
        inline: Whether to serve the file inline or as an attachment
        enhanced_styling: Whether to use enhanced styling for Excel files (default: True)

    Returns:
        StreamingResponse with the generated report file
    """
    async with base.session_context():
        report_generator = ClientReportGenerator()

        if format == 'xlsx':
            report_data, filename = await report_generator.generate_questionnaire_excel(client_id, use_enhanced_styling=enhanced_styling)
            media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif format == 'csv':
            report_data, filename = await report_generator.generate_questionnaire_csv(client_id)
            media_type = 'text/csv'
        elif format == 'pdf':
            report_data, filename = await report_generator.generate_questionnaire_pdf(client_id)
            media_type = 'application/pdf'
        else:
            raise HTTPException(status_code=400, detail="Unsupported format: {format}")

        # Create streaming response
        content_disposition = f'attachment; filename="{filename}"' if not inline else f'inline; filename="{filename}"'
        return StreamingResponse(
            io.BytesIO(report_data),
            media_type=media_type,
            headers={"Content-Disposition": content_disposition}
        )


async def list_client_reports():
    pass


async def create_client_report():
    pass


async def get_client_report():
    pass


async def update_client_report():
    pass


async def delete_client_report():
    pass
