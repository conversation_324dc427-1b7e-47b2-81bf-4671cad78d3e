import os

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from records.config.shared_config import SharedConfig
from records.context import context as ctx
from records.db import api as db_api
from records.services import search


class SearchResults(BaseModel):
    items: list[search.SearchResult]
    count: int


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'search'))

    router.add_api_route(
        "", perform_search, methods=['GET'],
        name='Search for global/client data', response_model=SearchResults
    )

    return router


async def perform_search(
    q: str,
    client_id: str = None,
    limit: int = 25,
    # page: int = 1,
    # order: str = 'id',
    # desc: bool = False,
    # session: AsyncSession = Depends(get_session)
):
    result = await search.perform_search(
        query=q,
        client_id=client_id,
        limit=limit
    )
    return {
        'items': [r.model_dump() for r in result],
        'count': len(result),
        # 'limit': limit,
        # 'page': page
    }

