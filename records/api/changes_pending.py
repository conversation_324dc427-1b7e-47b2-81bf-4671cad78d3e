import os
from typing import Optional

from fastapi import APIRouter
from fastapi.responses import ORJSONResponse
from pydantic import BaseModel

from records.context import context as ctx
from records.db import base, api as db_api
from records.db import models
from records.services import pending_changes, clients


def get_routers(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'pending_changes'))
    router_applied = APIRouter(prefix=os.path.join(prefix, 'applied_changes'))

    router.add_api_route("", list_pending_changes, methods=['GET'], name='List pending changes')
    router.add_api_route("/{change_id}/approve", approve_pending_change, methods=['POST'], name='Approve pending change')
    router.add_api_route("/{change_id}/process", process_pending_change, methods=['POST'], name='Process pending change')
    router.add_api_route("/by-file/{file_id}", get_pending_change_by_file, methods=['GET'], name='Get pending change by file')

    router_applied.add_api_route("", list_applied_changes, methods=['GET'], name='List applied changes')
    return [router, router_applied]


class ClientApprove(BaseModel):
    note: Optional[str] = None


async def list_pending_changes(client_id: str):
    async with base.session_context():
        _, client_data = await clients.get_client_data(client_id)
        changes = await pending_changes.list_pending_changes(client_data)
        return ORJSONResponse(
            content={'items': changes, 'count': len(changes)}
        )


async def list_applied_changes(client_id: str):
    async with base.session_context():
        _, client_data = await clients.get_client_data(client_id)
        changes = await pending_changes.list_applied_changes(client_data)
        return ORJSONResponse(
            content={'items': changes, 'count': len(changes)}
        )


async def get_pending_change_by_file(client_id: str, file_id: str):
    async with base.session_context():
        file_db = await db_api.get_client_file_by_id(file_id)
        _, client_data = await clients.get_client_data(client_id)
        change = await pending_changes.get_pending_change_by_file(client_data, file_db)
        return ORJSONResponse(content=change)


async def process_pending_change(client_id: str, change_id: str):
    result = await pending_changes.reprocess_pending_change(client_id, change_id)
    return ORJSONResponse(content=result)


async def approve_pending_change(client_id: str, change_id: str, approve_data: Optional[ClientApprove] = None):
    manager: models.Manager = ctx.current_manager()
    note = approve_data.note if approve_data else None

    await pending_changes.mark_as_applied(client_id, change_id)
    result = await clients.approve_client_data(client_id, manager.title, note)
    return ORJSONResponse(content=result)
