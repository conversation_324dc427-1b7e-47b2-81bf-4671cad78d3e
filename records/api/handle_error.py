import logging

from fastapi import HTT<PERSON><PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

logger = logging.getLogger(__name__)


def error_json(status, msg):
    return {"msg": msg, "status": status}


def error_response(status, msg):
    return JSONResponse(content=error_json(status, msg), status_code=status)


class CustomHTTPException(Exception):
    status_code = 500

    def __init__(self, *args):
        super(CustomHTTPException, self).__init__(*args)


def handle_exception(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle HTTPException with generating custom error message.

    Parameters
    ----------
    request: Request
        Request object associated with HTTPException.
    exc: Exception
        Exception object.

    Returns
    -------
        JSONResponse
            Custom error message.
    """

    if isinstance(exc, HTTPException):
        status_code = exc.status_code
        msg = exc.detail
    elif isinstance(exc, StarletteHTTPException):
        status_code = exc.status_code
        msg = exc.detail
    else:
        status_code = 500
        msg = str(exc)

    response_content = error_json(status_code, msg)
    # logger.info(response_content)
    return JSONResponse(content=response_content, status_code=status_code)


def async_handle_error(error_prefix="Error: ", error_status_code=500):
    """
    Decorator function to handle exceptions and raise HTTPException for async functions.

    Parameters
    ----------
    error_prefix: str
        Prefix to be added to the original exception message.
    error_status_code: int
        HTTP status code to be used for the raised HTTPException.

    Returns
    -------
    Callable
        Decorator function that can be used to wrap around async function to handle exceptions.
    """

    def decorator(func):
        async def inner(*args, **kwargs):
            use_logger = logger
            try:
                return await func(*args, **kwargs)
            except CustomHTTPException as e:
                use_logger.error(error_prefix + str(e))
                raise HTTPException(e.status_code, detail=error_prefix + str(e))
            except Exception as e:
                use_logger.error(error_prefix + str(e))
                raise HTTPException(error_status_code, detail=error_prefix + str(e))

        return inner

    return decorator


def handle_error(error_prefix="Error: ", error_status_code=500):
    """
    Decorator function to handle exceptions and raise HTTPException.

    Parameters
    ----------
    error_prefix: str
        Prefix to be added to the original exception message.
    error_status_code: int
        HTTP status code to be used for the raised HTTPException.

    Returns
    -------
    Callable
        Decorator function that can be used to wrap around function to handle exceptions.
    """

    def decorator(func):
        def inner(*args, **kwargs):
            use_logger = logger
            if len(args) > 0 and hasattr(args[0], 'logger'):
                use_logger = args[0].logger
            if 'model_run_id' in kwargs:
                use_logger = logging.get_logger(__name__, kwargs['model_run_id'])
            try:
                return func(*args, **kwargs)
            except Exception as e:
                use_logger.error(error_prefix + str(e))
                raise HTTPException(error_status_code, detail=error_prefix + str(e))

        return inner

    return decorator
