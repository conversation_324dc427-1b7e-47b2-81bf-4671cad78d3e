import asyncio
import datetime
import os
import random

from fastapi import Request, HTTPException
from urllib import parse as urlparse

from records.api import common_utils
from records.cache import global_cache
from records.context import context as ctx
from records.credentials import credentials
from records.db import api as db_api, models, base
from records.policies import policies
from records.utils import utils

COOKIE_KEY = 'RECORDS_SESSION_ID'
AUTH_KEY = 'Authorization'
MAX_ID = int(8e18)
MIN_ID = int(1e17)
# sessions = {}
keys = os.getenv("AUTH_KEY", "")


@base.session_aware()
async def session_handler(request: Request, domain, session=None):
    cache_result = None
    sess_id = request.headers.get('X-Session')
    if not sess_id:
        sess_id = request.cookies.get(COOKIE_KEY)
    if sess_id:
        cache_key = common_utils.get_cache_key(sess_id, request, domain=domain)
        cache_result = await global_cache.get_cache().aget(cache_key)
        if cache_result:
            sess, *_ = cache_result
            created = False
            ctx.session.set(sess)
            return created, cache_key, cache_result
        # Assign session by sess id
        # if sess_id in sessions:
        #     sess = sessions[sess_id]
        # TODO delete expired session
        # else:
        sess, created = await get_or_create_session_for_id(sess_id)

        # sessions[sess_id] = sess
    else:
        # Create session ?
        sess = await db_api.create_session({
            'admin': False,
            'user_id': get_random_user_id(),
            'ttl': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365),
        })
        await session.flush()
        created = True
        cache_key = common_utils.get_cache_key(sess.id, request, domain=domain)
        # sessions[sess_id] = sess
        # raise HTTPException(401, 'Unauthorized')
    # token = get_bearer_token(request)
    # token = await db_api.get_token_by_id(token)
    # if not token:
    #     raise HTTPException(401, f'Unauthorized')
    # else:
    #     sess = models.Session(user_id=token.user_id)

    ctx.session.set(sess)
    return created, cache_key, cache_result


def basic_access():
    if not ctx.current_user():
        raise HTTPException(401, 'Unauthorized')


def admin_access():
    if not ctx.current_user() or not ctx.current_session().admin:
        raise HTTPException(403, 'Forbidden')


def owner_access():
    if ctx.current_role() != policies.AccessName.OWNER:
        raise HTTPException(403, 'Forbidden')


async def get_or_create_session_for_id(sess_id: str) -> tuple[models.Session, bool]:
    if len(sess_id) > models.Session.id.type.length:
        raise HTTPException(400, 'Invalid session id')
    sess = await db_api.get_session_by_id(sess_id)
    created = False
    if sess is None:
        # Re-create session
        sess = await db_api.create_session({
            'id': sess_id,
            'admin': False,
            'user_id': get_random_user_id(),
            'ttl': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365),
        })
        created = True

    return sess, created


def get_random_user_id():
    return random.randint(MIN_ID, MAX_ID)


def ephemeral_user(sess):
    return sess is None or sess.user_id >= MIN_ID


def generate_temp_password():
    charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    length = 8
    return ''.join(random.choice(charset) for _ in range(length))


def get_cookie_domain(req: Request):
    org_host = common_utils.get_org_host(req)
    base_url = os.environ['BASE_URL']
    base_host = urlparse.urlparse(base_url).hostname
    if base_host in org_host:
        return base_host
    return None


async def login(login, password):
    sess = ctx.current_session()

    u = None
    if login == 'admin':
        if keys and password == keys:
            sess.admin = True
            sess.user_id = -1
        else:
            raise HTTPException(401, 'Wrong credentials')
    else:
        u = await db_api.get_user_by_login_password(login, password)
        if not ephemeral_user(sess) and u.id != sess.user_id:
            raise HTTPException(400, "Already authenticated as another user, please Log out first")

        sess.user_id = u.id

    sess, created = await login_session(sess)
    return u, sess, created


async def login_session(sess: models.Session):
    if not sess:
        return sess, False
    # sess.id = str(uuid.uuid4())
    admin = False
    if sess.user_id in credentials.ADMIN_IDS:
        admin = True
    sess = await db_api.update_session(sess, {
        'ttl': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365),
        'user_id': sess.user_id,
        'admin': admin,
    })

    _ = asyncio.create_task(
        utils.log_exceptions(db_api.update_user(models.User(id=sess.user_id), {'last_login_at': models.now()}))
    )
    await common_utils.clear_cache_for_session(sess.id)
    # sessions[sess.id] = sess

    return sess, False


async def logout(request: Request):
    sess_id = request.cookies.get(COOKIE_KEY)
    if sess_id:
        # if sess_id in sessions:
        #     del sessions[sess_id]
        sess = await db_api.get_session_by_id(sess_id)
        if sess:
            sess = await make_session_as_guest(sess)
            # sessions[sess.id] = sess

    await common_utils.clear_cache_for_session(sess_id)


async def make_session_as_guest(sess: models.Session):
    return await db_api.update_session(sess, {
        'ttl': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365),
        'user_id': get_random_user_id(),
        'admin': False,
    })


# def extend_ws_permissions_with_org(permissions, org_permissions):
#     ws_allowed = False
#     if policies.is_allowed(org_permissions, policies.OrgAccessMode.OWNER, policies.OrgAccessMode):
#         permissions = policies.AccessMode.OWNER
#         ws_allowed = True
#     if policies.is_allowed(org_permissions, policies.OrgAccessMode.ALL_WS_VIEW, policies.OrgAccessMode):
#         permissions = permissions | policies.AccessMode.ALL_CHATS_VIEW
#         ws_allowed = True
#     if policies.is_allowed(org_permissions, policies.OrgAccessMode.WORKSPACE_DELETE, policies.OrgAccessMode):
#         permissions = permissions | policies.AccessMode.ALL_CHATS_MANAGE
#     if policies.is_allowed(org_permissions, policies.OrgAccessMode.ORG_MANAGE, policies.OrgAccessMode):
#         permissions = permissions | policies.AccessMode.METRICS_MANAGE
#
#     return permissions, ws_allowed


def get_bearer_token(request: Request) -> tuple[str, str]:
    res = ""
    auth_header = request.headers.get(AUTH_KEY)
    if auth_header:
        split = auth_header.split()
        if len(split) > 1:
            slash_split = split[1].split('/')
            if len(slash_split) > 1:
                return slash_split[0], slash_split[1]
            return split[1], ""
    return res, ""
