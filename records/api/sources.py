import os
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Response
from pydantic import BaseModel

from records.api import auth_utils
from records.db import api as db_api, base


class SourceCreate(BaseModel):
    title: str
    

class SourceUpdate(SourceCreate):
    title: Optional[str] = None


def get_router(prefix='/'):
    router = APIRouter(
        prefix=os.path.join(prefix, 'sources'),
        dependencies=[Depends(auth_utils.owner_access)]
    )

    router.add_api_route("", list_sources, methods=['GET'], name='List sources')
    router.add_api_route("", create_source, methods=['POST'], name='Create source')
    router.add_api_route("/{source_id}", get_source, methods=['GET'], name='Get source')
    router.add_api_route("/{source_id}", update_source, methods=['PUT'], name='Update source')
    router.add_api_route("/{source_id}", delete_source, methods=['DELETE'], name='Delete source')

    return router


async def list_sources(
    limit: int = 100,
    page: int = 1,
    order: str = 'title',
    desc: bool = False,
    q: str = None,
    # session: AsyncSession = Depends(get_session)
):
    sources, count = await db_api.list_sources(limit=limit, page=page, order=order, desc=desc, q=q)
    source_dicts = [m.to_dict() for m in sources]

    return {'items': source_dicts, 'count': count, 'limit': limit, 'page': page}


async def get_source(source_id: str):
    db_source = await db_api.get_source_by_id(source_id)

    source_dict = db_source.to_dict()

    return source_dict


async def create_source(source: SourceCreate):
    source_dict = source.model_dump()
    async with base.session_context() as session:
        db_source = await db_api.get_source_by(title=source_dict['title'])
        if db_source:
            raise HTTPException(400, 'Source already exists')

        db_source = await db_api.create_source(source_dict)

    source_dict = db_source.to_dict()
    return source_dict


async def update_source(source_id: str, source: SourceUpdate):
    db_source = await db_api.get_source_by_id(source_id)

    source_dict = source.model_dump(exclude_unset=True)
    if not source_dict:
        raise HTTPException(400, 'No fields to update')

    updated_source = await db_api.update_source(db_source, source_dict)

    source_dict = updated_source.to_dict()
    return source_dict


async def delete_source(source_id: str):
    db_source = await db_api.get_source_by_id(source_id)

    async with base.session_context():
        await db_api.delete_source(source_id)

    return Response(status_code=204)
