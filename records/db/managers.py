from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_manager(values, session=None):
    manager = models.Manager(**values)

    try:
        session.add(manager)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Manager: %s" % e
        )

    return manager


@base.session_aware()
async def update_manager(manager: models.Manager, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.Manager).where(models.Manager.id == manager.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    manager.update(new_values)

    return manager


@base.session_aware()
async def delete_manager(id: str, session=None):
    delete_q = delete(models.Manager).where(models.Manager.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_managers(
    limit: int = 0,
    page: int = 0,
    order: str = 'user_id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_manager_count(q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.Manager)
    if q:
        query = query.where(models.Manager.title.ilike(f'%{q}%'))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.Manager, order):
        # Set default order
        order = 'user_id'
    order_col = getattr(models.Manager, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def list_managers_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.Manager)
    if ids:
        query = query.where(models.Manager.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_manager_by_id(id: str, notfoundok: bool = False, session=None):
    if not id:
        return None
    return await base.get_by_id(models.Manager, id, notfoundok=notfoundok, session=session)


@base.session_aware()
async def get_manager_by(session=None, **kwargs):
    return await base.get_by_x(models.Manager, session=session, notfoundok=True, **kwargs)


@base.session_aware()
async def list_managers_by_id(ids: list[str], session=None):
    return await base.list_by_x(models.Manager, id=ids, session=session)


@base.session_aware()
async def get_manager_by_user_id(user_id: str, session=None):
    query = select(models.Manager).where(models.Manager.user_id == user_id)
    res = await session.execute(query)
    return res.scalar()


@base.session_aware()
async def get_manager_count(
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('title ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM managers'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
