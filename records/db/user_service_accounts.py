from sqlalchemy import update
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.exceptions import NotFoundException


@base.session_aware()
async def create_user_sa(values, session=None):
    return await base.create_model(models.UserServiceAccount, values, session=session)


@base.session_aware()
async def get_user_sa(user_id: int, service: str, notfoundok=False, session=None):
    query = select(models.UserServiceAccount).where(models.UserServiceAccount.user_id == user_id)
    query = query.where(models.UserServiceAccount.service == service)
    res = await session.execute(query)
    res = res.scalar()

    if not res and not notfoundok:
        raise NotFoundException(f'User SA not found for user_id: {user_id}')
    return res


@base.session_aware()
async def list_user_sa(user_id: int, notfoundok=False, session=None):
    query = select(models.UserServiceAccount).where(models.UserServiceAccount.user_id == user_id)

    query = query.order_by(models.UserServiceAccount.id)
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def update_user_sa(user_sa: models.UserServiceAccount, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.UserServiceAccount).where(models.UserServiceAccount.id == user_sa.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)
    user_sa.update(new_values.copy())

    return user_sa
