from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_change_record(values, session=None):
    change_record = models.ChangeRecord(**values)

    try:
        session.add(change_record)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ChangeRecord: %s" % e
        )

    return change_record


@base.session_aware()
async def update_change_record(change_record: models.ChangeRecord, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ChangeRecord).where(models.ChangeRecord.id == change_record.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    change_record.update(new_values)

    return change_record


@base.session_aware()
async def delete_change_record(id: str, session=None):
    delete_q = delete(models.ChangeRecord).where(models.ChangeRecord.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_change_records(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.ChangeRecord, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_change_records(
    client_id: str,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_change_record_count(client_id=client_id, q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ChangeRecord).where(models.ChangeRecord.client_id == client_id)
    if q:
        query = query.where(models.ChangeRecord.name.ilike(f'%{q}%'))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.ChangeRecord, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.ChangeRecord, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    if count is not None:
        return res.scalars().fetchall(), count
    else:
        return res.scalars().fetchall()


@base.session_aware()
async def list_change_records_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ChangeRecord)
    if ids:
        query = query.where(models.ChangeRecord.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_change_record_by_id(id: str, session=None):
    return await base.get_by_id(models.ChangeRecord, id, session=session)


@base.session_aware()
async def get_change_record_by_hash(client_id: str, hash: str, notfoundok: bool = False, session=None):
    return await base.get_by_x(models.ChangeRecord, client_id=client_id, hash=hash, notfoundok=notfoundok, session=session)


@base.session_aware()
async def get_change_record_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    if q:
        q = f'%{q}%'
        where.append('name ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM files'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
