from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.exceptions import NotFoundException
from records.utils import utils


@base.session_aware()
async def create_catalog(values, session=None):
    catalog = models.Catalog(**values)

    try:
        session.add(catalog)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Catalog: %s" % e
        )

    return catalog


@base.session_aware()
async def update_catalog(catalog: models.Catalog, new_values: dict, session=None):
    # new_values['updated_at'] = base.now()
    update_q = update(models.Catalog).where(models.Catalog.id == catalog.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    catalog.update(new_values)

    return catalog


@base.session_aware()
async def delete_catalog(id: int, session=None):
    delete_q = delete(models.Catalog).where(models.Catalog.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_catalogs(
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_catalog_count(q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.Catalog)
    if q:
        query = query.where(models.Catalog.name.ilike(f'%{q}%'))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.Catalog, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.Catalog, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def list_catalogs_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.Catalog)
    if ids:
        query = query.where(models.Catalog.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_catalog_by_id(id: int, session=None):
    return await base.get_by_id(models.Catalog, id, session=session)


@base.session_aware()
async def get_catalog_by_name(name: str, notfoundok=False, session=None):
    q = select(models.Catalog).where(models.Catalog.name == name)
    # res = await session.get(models.Chat, id)
    res = await session.execute(q)
    res = res.scalar()

    if not res and not notfoundok:
        raise NotFoundException(f'Catalog not found for name: {name}')
    return res


@base.session_aware()
async def get_catalog_count(
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('name ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM catalogs'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
