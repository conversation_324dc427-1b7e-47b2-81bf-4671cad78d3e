from fastapi import HTT<PERSON><PERSON>xception
from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.future import select

from records.credentials import passwords
from records.db import base
from records.db import models
from records.exceptions import NotFoundException


@base.session_aware()
async def list_users(ids: list[int], session=None, ):
    q = select(models.User).where(models.User.id.in_(ids))
    # res = await session.get(models.Chat, id)
    res = await session.execute(q)

    return res.scalars().fetchall()


@base.session_aware()
async def get_user_by_login(login: str, notfoundok=False, session=None, ):
    if not login:
        raise HTTPException(403, 'Wrong credentials')

    q = select(models.User).where(models.User.login == login)
    # res = await session.get(models.Chat, id)
    res = await session.execute(q)
    res = res.scalar()

    if not res and not notfoundok:
        raise HTTPException(403, "Couldn't find your account")

    return res


@base.session_aware()
async def get_user_by_login_password(login, password, session=None):
    if not password:
        raise HTTPException(403, 'Wrong credentials')

    user = await get_user_by_login(login, session=session)

    valid = passwords.Hasher.verify_password(password, user.password)
    if not valid:
        raise HTTPException(403, 'Wrong credentials')

    return user


@base.session_aware()
async def get_user_by_id(id: int, notfoundok=False, session=None):
    query = select(models.User).where(models.User.id == id)
    res = await session.execute(query)
    res = res.scalar()

    if not res and not notfoundok:
        raise NotFoundException(f'User not found for id: {id}')
    return res


@base.session_aware()
async def update_user(user: models.User, new_values, session=None):
    new_values['updated_at'] = base.now()
    if 'password' in new_values:
        new_values['password'] = passwords.Hasher.get_password_hash(new_values['password'])

    update_q = update(models.User).where(models.User.id == user.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    user.update(new_values)

    return user


@base.session_aware()
async def create_user(values, session=None):
    u = await get_user_by_login(values['login'], notfoundok=True, session=session)
    if u is not None:
        raise HTTPException(409, 'User with this login exists')

    values['password'] = passwords.Hasher.get_password_hash(values['password'])
    user = models.User(**values)

    try:
        session.add(user)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for User: %s" % e
        )

    return user


@base.session_aware()
async def delete_user(id: int, session=None):
    delete_q = delete(models.User).where(models.User.id == id)
    await session.execute(delete_q)
