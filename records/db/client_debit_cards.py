from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_debit_card(values, session=None):
    debit_card = models.DebitCard(**values)

    try:
        session.add(debit_card)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for DebitCard: %s" % e
        )

    return debit_card


@base.session_aware()
async def update_debit_card(debit_card: models.DebitCard, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.DebitCard).where(models.DebitCard.id == debit_card.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    debit_card.update(new_values)

    return debit_card


@base.session_aware()
async def delete_debit_card(id: int, session=None):
    delete_q = delete(models.DebitCard).where(models.DebitCard.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_debit_cards(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.DebitCard, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_debit_cards(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.DebitCard).where(models.DebitCard.client_id == client_id)
    # if q:
    #     query = query.where(models.DebitCard.name.ilike(f'%{q}%'))

    # if not hasattr(models.DebitCard, order):
    #     order = 'id'
    # order_col = getattr(models.DebitCard, order)

    # if desc:
    #     order_col = order_col.desc()
    # query = query.order_by(order_col)
    res = await session.execute(query)

    res = res.fetchall()
    return res


@base.session_aware()
async def list_debit_cards_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.DebitCard)
    if ids:
        query = query.where(models.DebitCard.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_debit_card_by_id(id: int, session=None):
    return await base.get_by_id(models.DebitCard, id, session=session)


@base.session_aware()
async def get_debit_card_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM debit_cards'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
