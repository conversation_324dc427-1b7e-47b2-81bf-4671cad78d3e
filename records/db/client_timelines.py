from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_timeline(values, session=None):
    timeline = models.ClientTimeline(**values)

    try:
        session.add(timeline)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientTimeline: %s" % e
        )

    return timeline


@base.session_aware()
async def update_timeline(timeline: models.ClientTimeline, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientTimeline).where(models.ClientTimeline.id == timeline.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    timeline.update(new_values)

    return timeline


@base.session_aware()
async def delete_timeline(id: int, session=None):
    delete_q = delete(models.ClientTimeline).where(models.ClientTimeline.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_timelines(
    client_id: str,
    type_: str = None,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ClientTimeline).where(models.ClientTimeline.client_id == client_id)
    if type_:
        query = query.where(models.ClientTimeline.type == type_)

    # if not hasattr(models.ClientTimeline, order):
    #     order = 'id'
    # order_col = getattr(models.ClientTimeline, order)

    # if desc:
    #     order_col = order_col.desc()
    # query = query.order_by(order_col)
    res = await session.execute(query)

    res = res.fetchall()
    return res


@base.session_aware()
async def list_timelines_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientTimeline)
    if ids:
        query = query.where(models.ClientTimeline.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_timeline_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientTimeline, id, session=session)


@base.session_aware()
async def get_timeline_by_type(client_id: str, type_: str, session=None):
    query = select(models.ClientTimeline).where(models.ClientTimeline.client_id == client_id)
    query = query.where(models.ClientTimeline.type == type_)

    res = await session.execute(query)

    return res.scalar()


@base.session_aware()
async def get_timeline_by_external_id(external_id: int, notfoundok: bool = False, session=None):
    return await base.get_by_x(
        models.ClientTimeline,
        notfoundok=notfoundok,
        external_id=external_id,
        session=session
    )


@base.session_aware()
async def get_timeline_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM timelines'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
