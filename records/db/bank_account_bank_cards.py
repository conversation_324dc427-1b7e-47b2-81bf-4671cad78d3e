from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_bank_account_bank_card(values, session=None):
    bank_account_bank_card = models.BankCard(**values)

    try:
        session.add(bank_account_bank_card)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for BankCard: %s" % e
        )

    return bank_account_bank_card


@base.session_aware()
async def update_bank_account_bank_card(
    bank_account_bank_card: models.BankCard, new_values: dict, session=None
):
    new_values['updated_at'] = base.now()
    keys_to_update = new_values.keys()
    # Set keys to encrypt and then retrieve
    for key in keys_to_update:
        setattr(bank_account_bank_card, key, new_values[key])
        new_values[key] = getattr(bank_account_bank_card, key)
    update_q = update(models.BankCard).where(models.BankCard.id == bank_account_bank_card.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    bank_account_bank_card.update(new_values)

    return bank_account_bank_card


@base.session_aware()
async def delete_bank_account_bank_card(id: int, session=None):
    delete_q = delete(models.BankCard).where(models.BankCard.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_bank_account_bank_cards(card_ids: list[int] = None, bank_account_id: str = None, session=None):
    delete_q = delete(models.BankCard)
    if not card_ids or not bank_account_id:
        raise ValueError('ids must be provided')
    delete_q = delete_q.where(models.BankCard.id.in_(card_ids))
    delete_q = delete_q.where(models.BankCard.bank_account_id == bank_account_id)
    await session.execute(delete_q)


@base.session_aware()
async def list_bank_account_bank_cards(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    offset = None
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.BankCard, models.Person).where(models.BankCard.client_id == client_id)
    query = query.outerjoin(models.Person, models.BankCard.client_person_id == models.Person.id)
    # if q:
    #     query = query.where(models.BankCard.name.ilike(f'%{q}%'))

    if not hasattr(models.BankCard, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.BankCard, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    res = await session.execute(query)

    joined_res = res.fetchall()
    client_signers = []
    for client_signer, person in joined_res:
        client_signer.person = person
        client_signers.append(client_signer)

    return client_signers


@base.session_aware()
async def list_bank_account_bank_cards_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.BankCard)
    if ids:
        query = query.where(models.BankCard.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_bank_account_bank_card_by_id(id: int, session=None):
    return await base.get_by_id(models.BankCard, id, session=session)


@base.session_aware()
async def get_bank_account_bank_card_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM bank_account_bank_cards'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
