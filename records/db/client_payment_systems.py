from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_payment_system(values, session=None):
    client_payment_system = models.PaymentService(**values)

    try:
        session.add(client_payment_system)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for PaymentService: %s" % e
        )

    return client_payment_system


@base.session_aware()
async def update_client_payment_system(client_payment_system: models.PaymentService, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.PaymentService).where(models.PaymentService.id == client_payment_system.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_payment_system.update(new_values)

    return client_payment_system


@base.session_aware()
async def delete_client_payment_system(id: int, session=None):
    delete_q = delete(models.PaymentService).where(models.PaymentService.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_payment_systems(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.PaymentService, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_payment_systems(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.PaymentService).where(models.PaymentService.client_id == client_id)
    # if q:
    #     query = query.where(models.PaymentSystem.name.ilike(f'%{q}%'))

    # if not hasattr(models.PaymentSystem, order):
    #     order = 'id'
    # order_col = getattr(models.PaymentSystem, order)

    # if desc:
    #     order_col = order_col.desc()
    # query = query.order_by(order_col)
    res = await session.execute(query)

    res = res.scalars().fetchall()
    return res


@base.session_aware()
async def list_client_payment_systems_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.PaymentService)
    if ids:
        query = query.where(models.PaymentService.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_payment_system_by_id(id: int, session=None):
    return await base.get_by_id(models.PaymentService, id, session=session)


@base.session_aware()
async def get_client_payment_system_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM payment_services'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
