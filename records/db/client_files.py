from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_file(values, session=None):
    client_file = models.File(**values)

    try:
        session.add(client_file)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for File: %s" % e
        )

    return client_file


@base.session_aware()
async def update_client_file(client_file: models.File, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    # Revalidate date if provided

    update_q = update(models.File).where(models.File.id == client_file.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_file.update(new_values)

    return client_file


@base.session_aware()
async def delete_client_file(id: str, session=None):
    delete_q = delete(models.File).where(models.File.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_files(ids: list[int] = None, client_id: str = None, keep_from_upload: bool = False, session=None):
    if not ids and not client_id:
        raise ValueError('Either ids or client_id must be provided')
    delete_q = delete(models.File)
    if ids:
        delete_q = delete_q.where(models.File.id.in_(ids))
    if client_id:
        delete_q = delete_q.where(models.File.client_id == client_id)
    if keep_from_upload:
        delete_q = delete_q.where(models.File.from_upload == False)
    await session.execute(delete_q)


@base.session_aware()
async def list_client_files(
    client_id: str,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_client_file_count(client_id=client_id, q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.File).where(models.File.client_id == client_id)
    if q:
        query = query.where(models.File.name.ilike(f'%{q}%'))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.File, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.File, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    if count is not None:
        return res.scalars().fetchall(), count
    else:
        return res.scalars().fetchall()


@base.session_aware()
async def list_client_files_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.File)
    if ids is not None:
        query = query.where(models.File.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_file_by_id(id: str, session=None):
    return await base.get_by_id(models.File, id, session=session)


@base.session_aware()
async def get_last_updated_client_file(client_id: str, session=None):
    query = select(models.File).where(models.File.client_id == client_id)
    query = query.order_by(models.File.updated_at.desc()).limit(1)
    res = await session.execute(query)
    return res.scalar()


@base.session_aware()
async def get_client_file_by_hash(client_id: str, hash: str, notfoundok: bool = False, session=None):
    return await base.get_by_x(models.File, client_id=client_id, hash=hash, notfoundok=notfoundok, session=session)


@base.session_aware()
async def get_client_file_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    if q:
        q = f'%{q}%'
        where.append('name ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM files'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
