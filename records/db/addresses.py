from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_address(values, session=None):
    return await base.create_model(models.Address, values, session=session)


@base.session_aware()
async def update_address(address: models.Address, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.Address).where(models.Address.id == address.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    address.update(new_values)

    return address


@base.session_aware()
async def delete_address(id: str, session=None):
    delete_q = delete(models.Address).where(models.Address.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_addresses(
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_address_count(q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.Address)
    if q:
        query = query.where(models.Address.full_address.ilike(f'%{q}%'))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.Address, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.Address, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def list_address_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.Address)
    if ids:
        query = query.where(models.Address.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_address_by_id(id: str, session=None):
    return await base.get_by_id(models.Address, id, session=session)


@base.session_aware()
async def get_address_by_full_address(full_address: str, session=None):
    return await base.get_by_x(models.Address, full_address=full_address, notfoundok=True, session=session)


@base.session_aware()
async def get_address_count(
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('full_address ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM addresses'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
