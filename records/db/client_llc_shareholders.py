from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_shareholder(values, session=None):
    client_shareholder = models.ClientLLCShareholder(**values)

    try:
        session.add(client_shareholder)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientShareholder: %s" % e
        )

    return client_shareholder


@base.session_aware()
async def update_client_shareholder(client_shareholder: models.ClientLLCShareholder, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientLLCShareholder).where(models.ClientLLCShareholder.id == client_shareholder.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_shareholder.update(new_values)

    return client_shareholder


@base.session_aware()
async def delete_client_shareholder(id: int, session=None):
    delete_q = delete(models.ClientLLCShareholder).where(models.ClientLLCShareholder.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_shareholders(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.ClientLLCShareholder, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_shareholders(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ClientLLCShareholder, models.Person).where(models.ClientLLCShareholder.client_id == client_id)
    query = query.join(models.Person, models.ClientLLCShareholder.client_person_id == models.Person.id)
    # if q:
    #     query = query.where(models.ClientShareholder.name.ilike(f'%{q}%'))

    # if not hasattr(models.ClientShareholder, order):
    #     order = 'id'
    # order_col = getattr(models.ClientShareholder, order)

    # if desc:
    #     order_col = order_col.desc()
    # query = query.order_by(order_col)
    res = await session.execute(query)

    joined_res = res.fetchall()
    shareholders = []
    for shareholder, person in joined_res:
        shareholder.person = person
        shareholders.append(shareholder)

    return shareholders


@base.session_aware()
async def list_client_shareholders_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientLLCShareholder)
    if ids:
        query = query.where(models.ClientLLCShareholder.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_shareholder_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientLLCShareholder, id, session=session)


@base.session_aware()
async def get_client_shareholder_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM client_llc_shareholders'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
