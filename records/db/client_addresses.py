from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_address(values, session=None):
    return await base.create_model(models.ClientAddress, values, session=session)


@base.session_aware()
async def update_client_address(client_address: models.ClientAddress, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientAddress).where(models.ClientAddress.id == client_address.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_address.update(new_values)

    return client_address


@base.session_aware()
async def delete_client_address(id: int, session=None):
    delete_q = delete(models.ClientAddress).where(models.ClientAddress.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_addresses(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.ClientAddress, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_addresses(
    client_id: str,
    order: str = 'created_at',
    desc: bool = False,
    session=None
):
    # offset = None
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ClientAddress, models.Address).where(models.ClientAddress.client_id == client_id)
    query = query.outerjoin(models.Address, models.ClientAddress.address_id == models.Address.id)
    # if q:
    #     query = query.where(models.ClientAddress.name.ilike(f'%{q}%'))

    if not hasattr(models.ClientAddress, order):
        # Set default order
        order = 'created_at'
    order_col = getattr(models.ClientAddress, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    res = await session.execute(query)

    joined_res = res.fetchall()
    client_addresses = []
    for client_addr, addr in joined_res:
        client_addr.address = addr
        client_addresses.append(client_addr)

    return client_addresses


@base.session_aware()
async def list_client_addresses_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientAddress)
    if ids:
        query = query.where(models.ClientAddress.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_address_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientAddress, id, session=session)


@base.session_aware()
async def get_client_address_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM client_addresses'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
