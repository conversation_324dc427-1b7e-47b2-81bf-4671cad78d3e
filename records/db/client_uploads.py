from sqlalchemy import delete, update, text, cast
from sqlalchemy import exc
from sqlalchemy.future import select
from sqlalchemy import types as st

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_upload(values, session=None):
    client_upload = models.ClientUpload(**values)

    try:
        session.add(client_upload)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientInitUpload: %s" % e
        )

    return client_upload


@base.session_aware()
async def update_client_upload(client_upload: models.ClientUpload, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientUpload).where(models.ClientUpload.id == client_upload.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_upload.update(new_values)

    return client_upload


@base.session_aware()
async def delete_client_upload(id: int, session=None):
    delete_q = delete(models.ClientUpload).where(models.ClientUpload.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_upload_by_file_id(file_id: str, session=None):
    delete_q = delete(models.ClientUpload).where(models.ClientUpload.file_id == file_id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_uploads(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.ClientUpload, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_uploads(
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    applied: bool = False,
    all: bool = False,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_client_upload_count(q=q, applied=applied, all=all)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ClientUpload)
    if q:
        query = query.where(models.ClientUpload.status.ilike(f'%{q}%').__or__(
            models.ClientUpload.error_message.ilike(f'%{q}%')
        ).__or__(
            cast(models.ClientUpload.output, st.Text()).ilike(f'%{q}%')
        ))
    if not all:
        if applied:
            query = query.where(models.ClientUpload.status == models.ClientUploadStatus.APPLIED)
        else:
            query = query.where(models.ClientUpload.status != models.ClientUploadStatus.APPLIED)

    # Apply ordering
    if not hasattr(models.ClientUpload, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.ClientUpload, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    # Apply limit and offset to the main query
    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    # Execute the main query
    res = await session.execute(query)
    uploads = res.scalars().fetchall()

    # If we have uploads, fetch their attached objects
    if uploads:
        upload_ids = [upload.id for upload in uploads]

        # Query attached objects for these uploads
        attached_query = select(models.UploadAttachedObject).where(
            models.UploadAttachedObject.upload_id.in_(upload_ids)
        )
        attached_res = await session.execute(attached_query)
        attached_objects = attached_res.scalars().fetchall()

        # Group attached objects by upload_id
        attached_by_upload = {}
        for obj in attached_objects:
            if obj.upload_id not in attached_by_upload:
                attached_by_upload[obj.upload_id] = []
            attached_by_upload[obj.upload_id].append(obj)

        # Add attached_objects to each upload
        for upload in uploads:
            upload.attached_objects = attached_by_upload.get(upload.id, [])

    if count is not None:
        return uploads, count
    else:
        return uploads


@base.session_aware()
async def list_client_uploads_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientUpload)
    if ids:
        query = query.where(models.ClientUpload.id.in_(ids))

    res = await session.execute(query)
    uploads = res.scalars().fetchall()

    # If we have uploads, fetch their attached objects
    if uploads:
        upload_ids = [upload.id for upload in uploads]

        # Query attached objects for these uploads
        attached_query = select(models.UploadAttachedObject).where(
            models.UploadAttachedObject.upload_id.in_(upload_ids)
        )
        attached_res = await session.execute(attached_query)
        attached_objects = attached_res.scalars().fetchall()

        # Group attached objects by upload_id
        attached_by_upload = {}
        for obj in attached_objects:
            if obj.upload_id not in attached_by_upload:
                attached_by_upload[obj.upload_id] = []
            attached_by_upload[obj.upload_id].append(obj)

        # Add attached_objects to each upload
        for upload in uploads:
            upload.attached_objects = attached_by_upload.get(upload.id, [])

    return uploads


@base.session_aware()
async def get_client_upload_by_id(id: int, session=None):
    upload = await base.get_by_id(models.ClientUpload, id, session=session)

    if upload:
        # Fetch attached objects for this upload
        attached_query = select(models.UploadAttachedObject).where(
            models.UploadAttachedObject.upload_id == upload.id
        )
        attached_res = await session.execute(attached_query)
        attached_objects = attached_res.scalars().fetchall()

        # Add attached_objects to the upload
        upload.attached_objects = attached_objects

    return upload


@base.session_aware()
async def list_client_uploads_by_file_id(file_id: str, session=None):
    """Get all client uploads for a specific file"""
    query = select(models.ClientUpload).where(models.ClientUpload.file_id == file_id)
    res = await session.execute(query)
    uploads = res.scalars().fetchall()

    # If we have uploads, fetch their attached objects
    if uploads:
        upload_ids = [upload.id for upload in uploads]

        # Query attached objects for these uploads
        attached_query = select(models.UploadAttachedObject).where(
            models.UploadAttachedObject.upload_id.in_(upload_ids)
        )
        attached_res = await session.execute(attached_query)
        attached_objects = attached_res.scalars().fetchall()

        # Group attached objects by upload_id
        attached_by_upload = {}
        for obj in attached_objects:
            if obj.upload_id not in attached_by_upload:
                attached_by_upload[obj.upload_id] = []
            attached_by_upload[obj.upload_id].append(obj)

        # Add attached_objects to each upload
        for upload in uploads:
            upload.attached_objects = attached_by_upload.get(upload.id, [])

    return uploads


@base.session_aware()
async def get_client_upload_count(
    q: str = None,
    applied: bool = False,
    all: bool = False,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('status ILIKE :q OR error_message ILIKE :q OR output::text ILIKE :q')
        params['q'] = q

    if not all:
        if applied:
            where.append('status = :status')
            params['status'] = models.ClientUploadStatus.APPLIED
        else:
            where.append('status != :status')
            params['status'] = models.ClientUploadStatus.APPLIED

    raw = 'SELECT count(*) AS count FROM client_uploads'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
