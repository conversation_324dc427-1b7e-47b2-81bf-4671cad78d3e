from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.exceptions import NotFoundException
from records.utils import utils


@base.session_aware()
async def create_client(values, session=None):
    client = models.Client(**values)

    try:
        session.add(client)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Client: %s" % e
        )

    return client


@base.session_aware()
async def update_client(client: models.Client, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    new_values.pop("id", None)

    update_q = update(models.Client).where(models.Client.id == client.id)
    update_q = update_q.values(**new_values)

    await session.execute(update_q)

    client.update(new_values)

    return client


@base.session_aware()
async def delete_client(id: str, session=None):
    delete_q = delete(models.Client).where(models.Client.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_clients(
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    internal_draft_flag: bool = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_client_count(q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.Client)
    if q:
        query = query.where(
            models.Client.name.ilike(f'%{q}%')
            .__or__(models.Client.ein.ilike(f'%{q}%').__or__(models.Client.description.ilike(f'%{q}%')))
        )
    if internal_draft_flag is not None:
        query = query.where(models.Client.internal_draft_flag == internal_draft_flag)

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.Client, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.Client, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def list_clients_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.Client)
    if ids:
        query = query.where(models.Client.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_by_id(id: str, session=None):
    return await base.get_by_id(models.Client, id, session=session)


@base.session_aware()
async def client_select_for_update(id: str, session=None):
    sql = text('SELECT * FROM clients WHERE id = :id FOR UPDATE')
    params = {'id': id}
    res = await session.execute(sql, params)
    client = res.scalar()

    return client


@base.session_aware()
async def get_client_by(session=None, **kwargs):
    return await base.get_by_x(models.Client, session=session, **kwargs)


@base.session_aware()
async def get_client_by_ein(ein: str, session=None):
    # Use SELECT ein FROM clients WHERE REGEXP_REPLACE(:ein, '[^0-9]+', '') = :ein;
    sql = text("SELECT id FROM clients WHERE REGEXP_REPLACE(ein, '[^0-9]', '', 'g') = :ein")
    params = {'ein': ein}
    res = await session.execute(sql, params)
    id = res.scalar()

    if id:
        return await get_client_by_id(id, session=session)
    return None


@base.session_aware()
async def search_client_by_name(clean_name: str, notfoundok: bool = False, session=None):
    # Use SELECT ein FROM clients WHERE REGEXP_REPLACE(:ein, '[^0-9]+', '') = :ein;
    sql = text("SELECT id FROM clients WHERE UPPER(REGEXP_REPLACE(name, '[^0-9]', '', 'g')) = :name")
    params = {'name': clean_name}
    res = await session.execute(sql, params)
    id = res.scalar()

    if id:
        return await get_client_by_id(id, session=session)
    if not notfoundok:
        raise NotFoundException(f'Client not found for name: {clean_name}')
    return None


@base.session_aware()
async def list_clients_by_eins(eins: list[str], session=None):
    eins = [ein.replace('-', ''.strip()) for ein in eins]
    sql = text("SELECT id FROM clients WHERE REGEXP_REPLACE(ein, '[^0-9]', '', 'g') = ANY(:eins)")
    params = {'eins': eins}
    res = await session.execute(sql, params)
    ids = res.scalars().fetchall()

    if not ids:
        return []

    query = select(models.Client).where(models.Client.id.in_(ids))
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_count(
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('name ILIKE :q OR ein ILIKE :q OR description ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM clients'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
