from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_source(values, session=None):
    return await base.create_model(models.Source, values, session=session)


@base.session_aware()
async def update_source(source: models.Source, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.Source).where(models.Source.id == source.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    source.update(new_values)

    return source


@base.session_aware()
async def delete_source(id: str, session=None):
    delete_q = delete(models.Source).where(models.Source.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_sources(
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_source_count(q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.Source)
    if q:
        query = query.where(models.Source.title.ilike(f'%{q}%'))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.Source, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.Source, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def list_source_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.Source)
    if ids:
        query = query.where(models.Source.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_source_by_id(id: str, notfoundok: bool = False, session=None):
    if not id:
        return None
    return await base.get_by_id(models.Source, id, notfoundok=notfoundok, session=session)


@base.session_aware()
async def get_source_by(session=None, **kwargs):
    return await base.get_by_x(
        models.Source,
        notfoundok=True,
        session=session,
        **kwargs
    )


@base.session_aware()
async def get_source_count(
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('title ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM sources'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
