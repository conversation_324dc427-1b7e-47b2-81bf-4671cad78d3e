from typing import List

from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models


@base.session_aware()
async def create_client_upload_attached_object(values, session=None):
    attached_object = models.UploadAttachedObject(**values)

    try:
        session.add(attached_object)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientUploadAttachedObject: %s" % e
        )

    return attached_object


@base.session_aware()
async def update_client_upload_attached_object(attached_object: models.UploadAttachedObject, new_values: dict, session=None):
    update_q = update(models.UploadAttachedObject).where(models.UploadAttachedObject.id == attached_object.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    attached_object.update(new_values)

    return attached_object


@base.session_aware()
async def delete_client_upload_attached_object(id: int, session=None):
    delete_q = delete(models.UploadAttachedObject).where(models.UploadAttachedObject.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_upload_attached_objects_by_upload_id(upload_id: int, session=None):
    delete_q = delete(models.UploadAttachedObject).where(models.UploadAttachedObject.upload_id == upload_id)
    await session.execute(delete_q)


@base.session_aware()
async def list_upload_attached_objects_by_upload_id(upload_id: int, session=None):
    query = select(models.UploadAttachedObject).where(models.UploadAttachedObject.upload_id == upload_id)
    res = await session.execute(query)
    return res.scalars().fetchall()


@base.session_aware()
async def get_upload_attached_object_by_id(id: int, session=None):
    return await base.get_by_id(models.UploadAttachedObject, id, session=session)


@base.session_aware()
async def list_upload_attached_objects_by_object(object_type: str, object_id: str = None, object_ids: List[str] = None, session=None):
    """Get all upload attached objects for a specific object (e.g., all uploads attached to a client)"""
    query = select(models.UploadAttachedObject).where(models.UploadAttachedObject.object_type == object_type)
    if object_id:
        query = query.where(models.UploadAttachedObject.object_id == object_id)
    if object_ids:
        query = query.where(models.UploadAttachedObject.object_id.in_(object_ids))
    res = await session.execute(query)
    return res.scalars().fetchall()


@base.session_aware()
async def delete_upload_attached_objects_by_object(object_type: str, object_id: str, session=None):
    """Delete all upload attached objects for a specific object (e.g., all uploads attached to a tax report)"""
    delete_q = delete(models.UploadAttachedObject).where(
        models.UploadAttachedObject.object_type == object_type,
        models.UploadAttachedObject.object_id == object_id
    )
    await session.execute(delete_q)


@base.session_aware()
async def replace_upload_attached_objects(upload_id: int, attached_objects: list, session=None):
    """Replace all attached objects for a client upload with new ones"""
    with_ids = [ao for ao in attached_objects if ao.get('id')]
    without_ids = [ao for ao in attached_objects if not ao.get('id')]

    # Delete all attached objects with ids that are not in the new list
    ids_to_avoid_delete = [ao['id'] for ao in with_ids]
    delete_q = delete(models.UploadAttachedObject).where(
        models.UploadAttachedObject.upload_id == upload_id,
        ~models.UploadAttachedObject.id.in_(ids_to_avoid_delete)
    )
    await session.execute(delete_q)

    # Update all attached objects with ids
    for ao in with_ids:
        await update_client_upload_attached_object(
            await get_upload_attached_object_by_id(ao['id'], session=session),
            ao,
            session=session
        )

    # Create all attached objects without ids
    for ao in without_ids:
        await create_client_upload_attached_object(ao, session=session)
