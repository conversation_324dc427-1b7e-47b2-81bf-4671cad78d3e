from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_external_id(values, session=None):
    external_id = models.ExternalID(**values)

    try:
        session.add(external_id)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ExternalID: %s" % e
        )

    return external_id


@base.session_aware()
async def update_external_id(external_id: models.ExternalID, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ExternalID).where(models.ExternalID.id == external_id.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    external_id.update(new_values)

    return external_id


@base.session_aware()
async def delete_external_id(id: str, session=None):
    delete_q = delete(models.ExternalID).where(models.ExternalID.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_external_ids(
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_external_id_count(q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ExternalID)
    if q:
        query = query.where(models.ExternalID.name.ilike(f'%{q}%'))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.ExternalID, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.ExternalID, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def list_external_ids_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ExternalID)
    if ids:
        query = query.where(models.ExternalID.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_external_id_by_id(id: str, session=None):
    return await base.get_by_id(models.ExternalID, id, session=session)


@base.session_aware()
async def list_external_id_by(session=None, **kwargs):
    return await base.list_by_x(models.ExternalID, session=session, **kwargs)


@base.session_aware()
async def delete_external_id_by(session=None, **kwargs):
    return await base.delete_by_x(models.ExternalID, session=session, **kwargs)


@base.session_aware()
async def get_external_id_by(session=None, **kwargs):
    return await base.get_by_x(models.ExternalID, session=session, **kwargs)


@base.session_aware()
async def get_external_id_count(
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('name ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM external_ids'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
