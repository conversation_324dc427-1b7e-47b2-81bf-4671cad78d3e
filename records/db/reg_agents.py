from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_reg_agent(values, session=None):
    return await base.create_model(models.RegAgent, values, session=session)


@base.session_aware()
async def update_reg_agent(reg_agent: models.RegAgent, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.RegAgent).where(models.RegAgent.id == reg_agent.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    reg_agent.update(new_values)

    return reg_agent


@base.session_aware()
async def delete_reg_agent(id: str, session=None):
    delete_q = delete(models.RegAgent).where(models.RegAgent.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_reg_agents(
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_reg_agent_count(q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.RegAgent, models.Address)
    if q:
        query = query.where(models.RegAgent.title.ilike(f'%{q}%').__or__(models.RegAgent.nickname.ilike(f'%{q}%')))

    query = query.outerjoin(models.Address, models.RegAgent.address_id == models.Address.id)

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.RegAgent, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.RegAgent, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    joined_res = await session.execute(query)
    joined_res = joined_res.fetchall()

    reg_agents = []
    for reg_agent, address in joined_res:
        reg_agent.address = address
        reg_agents.append(reg_agent)

    return reg_agents, count


@base.session_aware()
async def list_reg_agent_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.RegAgent)
    if ids:
        query = query.where(models.RegAgent.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_reg_agent_by_id(id: str, session=None):
    return await base.get_by_id(models.RegAgent, id, session=session)


@base.session_aware()
async def get_reg_agent_by(title: str = None, nickname: str = None, session=None):
    return await base.get_by_x(
        models.RegAgent,
        title=title,
        nickname=nickname,
        notfoundok=True,
        session=session
    )


@base.session_aware()
async def get_reg_agent_count(
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('title ILIKE :q OR nickname ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM reg_agents'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
