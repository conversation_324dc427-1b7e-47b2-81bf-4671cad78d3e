from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models


@base.session_aware()
async def create_session(values, session=None):
    if isinstance(values, models.Session):
        sess = values
    else:
        sess = models.Session(**values)

    try:
        session.add(sess)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Session: %s" % e
        )

    return sess


@base.session_aware()
async def get_sessions(session=None, **kwargs):
    query = select(models.Session)
    query = query.filter_by(**kwargs)

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_session_by_id(id, session=None):
    # query = select(models.Session).where(models.Session.id == id)
    # res = await session.execute(query)
    res = await session.get(models.Session, id)

    return res


@base.session_aware()
async def update_session(sess, new_values, session=None):
    # new_values['updated_at'] = base.now()
    update_q = update(models.Session).where(models.Session.id == sess.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    sess.update(new_values)

    return sess


@base.session_aware()
async def delete_session(id, session=None):
    sess = await get_session_by_id(id)

    if not sess:
        raise RuntimeError('Session not found for name: %s' % id)

    await session.delete(sess)


@base.session_aware()
async def delete_sessions_for_user(user_id: int, session=None):
    delete_q = delete(models.Session).where(models.Session.user_id == user_id)
    await session.execute(delete_q)
