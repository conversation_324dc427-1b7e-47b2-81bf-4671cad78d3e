from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_extractor(values, session=None):
    client_extractor = models.ClientExtractor(**values)

    try:
        session.add(client_extractor)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientExtractor: %s" % e
        )

    return client_extractor


@base.session_aware()
async def update_client_extractor(client_extractor: models.ClientExtractor, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientExtractor).where(models.ClientExtractor.id == client_extractor.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_extractor.update(new_values)

    return client_extractor


@base.session_aware()
async def delete_client_extractor(id: str, session=None):
    delete_q = delete(models.ClientExtractor).where(models.ClientExtractor.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_client_extractors(
    client_id: str,
    session=None
):
    query = select(models.ClientExtractor).where(models.ClientExtractor.client_id == client_id)
    # if q:
    #     query = query.where(models.ClientExtractor.name.ilike(f'%{q}%'))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_client_extractors_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientExtractor)
    if ids:
        query = query.where(models.ClientExtractor.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_extractor_by_id(id: str, session=None):
    return await base.get_by_id(models.ClientExtractor, id, session=session)


@base.session_aware()
async def get_client_extractor_by_type(type: str, notfoundok: bool = False, session=None):
    return await base.get_by_x(models.ClientExtractor, notfoundok=notfoundok, type=type, session=session)


@base.session_aware()
async def get_client_extractor_by_client_and_type(client_id: str, type: str, notfoundok: bool = False, session=None):
    return await base.get_by_x(
        models.ClientExtractor,
        notfoundok=notfoundok,
        client_id=client_id,
        type=type,
        session=session
    )


@base.session_aware()
async def get_client_extractor_by_external_id(external_id: int, notfoundok: bool = False, session=None):
    return await base.get_by_x(
        models.ClientExtractor,
        notfoundok=notfoundok,
        external_id=external_id,
        session=session
    )


@base.session_aware()
async def get_client_extractor_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    if q:
        q = f'%{q}%'
        where.append('name ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM extractors'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
