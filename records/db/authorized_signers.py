from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_authorized_signer(values, session=None):
    authorized_signer = models.ClientAuthorizedSigner(**values)

    try:
        session.add(authorized_signer)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for AuthorizedSigner: %s" % e
        )

    return authorized_signer


@base.session_aware()
async def update_authorized_signer(authorized_signer: models.ClientAuthorizedSigner, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientAuthorizedSigner).where(models.ClientAuthorizedSigner.id == authorized_signer.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    authorized_signer.update(new_values)

    return authorized_signer


@base.session_aware()
async def delete_authorized_signer(id: int, session=None):
    delete_q = delete(models.ClientAuthorizedSigner).where(models.ClientAuthorizedSigner.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_authorized_signers(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.ClientAuthorizedSigner, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_authorized_signers(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    offset = None
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ClientAuthorizedSigner, models.Person).where(models.ClientAuthorizedSigner.client_id == client_id)
    query = query.outerjoin(models.Person, models.ClientAuthorizedSigner.client_person_id == models.Person.id)
    # if q:
    #     query = query.where(models.AuthorizedSigner.name.ilike(f'%{q}%'))

    if not hasattr(models.ClientAuthorizedSigner, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.ClientAuthorizedSigner, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    res = await session.execute(query)

    joined_res = res.fetchall()
    client_signers = []
    for client_signer, person in joined_res:
        client_signer.person = person
        client_signers.append(client_signer)

    return client_signers


@base.session_aware()
async def list_authorized_signers_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientAuthorizedSigner)
    if ids:
        query = query.where(models.ClientAuthorizedSigner.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_authorized_signer_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientAuthorizedSigner, id, session=session)


@base.session_aware()
async def get_authorized_signer_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM authorized_signers'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
