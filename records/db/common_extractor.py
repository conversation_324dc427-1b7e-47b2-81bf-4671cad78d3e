from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_common_extractor(values, session=None):
    common_extractor = models.CommonExtractor(**values)

    try:
        session.add(common_extractor)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for CommonExtractor: %s" % e
        )

    return common_extractor


@base.session_aware()
async def update_common_extractor(common_extractor: models.CommonExtractor, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.CommonExtractor).where(models.CommonExtractor.id == common_extractor.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    common_extractor.update(new_values)

    return common_extractor


@base.session_aware()
async def delete_common_extractor(id: str, session=None):
    delete_q = delete(models.CommonExtractor).where(models.CommonExtractor.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_common_extractors(
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_common_extractor_count(q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.CommonExtractor)
    if q:
        query = query.where(models.CommonExtractor.title.ilike(f'%{q}%'))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.CommonExtractor, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.CommonExtractor, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def list_common_extractors_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.CommonExtractor)
    if ids:
        query = query.where(models.CommonExtractor.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_common_extractor_by_id(id: str, session=None):
    return await base.get_by_id(models.CommonExtractor, id, session=session)


@base.session_aware()
async def get_common_extractor_first(session=None):
    query = select(models.CommonExtractor).order_by(models.CommonExtractor.id).limit(1)
    res = await session.execute(query)
    return res.scalar()


@base.session_aware()
async def get_common_extractor_count(
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('title ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM common_extractors'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
