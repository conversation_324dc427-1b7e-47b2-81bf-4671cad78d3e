from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_person(values, session=None):
    client_person = models.Person(**values)

    try:
        session.add(client_person)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for Person: %s" % e
        )

    return client_person


@base.session_aware()
async def update_person(client_person: models.Person, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.Person).where(models.Person.id == client_person.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_person.update(new_values)

    return client_person


@base.session_aware()
async def delete_person(id: str, session=None):
    delete_q = delete(models.Person).where(models.Person.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def list_persons(
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_person_count(q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.Person)
    if q:
        query = query.where(models.Person.full_title.ilike(f'%{q}%').__or__(
            models.Person.email.ilike(f'%{q}%')
        ))

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.Person, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.Person, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall(), count


@base.session_aware()
async def list_persons_by_full_title(
    full_title: list[str],
    session=None
):
    return await base.list_by_x(models.Person, full_title=full_title, session=session)


@base.session_aware()
async def list_persons_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.Person)
    if ids:
        query = query.where(models.Person.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_person_by_id(id: str, session=None):
    return await base.get_by_id(models.Person, id, session=session)


@base.session_aware()
async def get_person_by_first_last_name(firstname: str, lastname: str, session=None):
    return await base.get_by_x(models.Person, firstname=firstname, lastname=lastname, notfoundok=True, session=session)


@base.session_aware()
async def get_person_count(
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('full_title ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM client_persons'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
