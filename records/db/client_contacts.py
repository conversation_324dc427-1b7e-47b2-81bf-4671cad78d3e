from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_contact(values, session=None):
    return await base.create_model(models.ClientContact, values, session=session)


@base.session_aware()
async def update_client_contact(client_contact: models.ClientContact, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientContact).where(models.ClientContact.id == client_contact.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_contact.update(new_values)

    return client_contact


@base.session_aware()
async def delete_client_contact(id: int, session=None):
    delete_q = delete(models.ClientContact).where(models.ClientContact.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_contacts(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.ClientContact, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_contacts(
    client_id: str,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    session=None
):
    query = select(models.ClientContact, models.Person).where(models.ClientContact.client_id == client_id)
    query = query.outerjoin(models.Person, models.ClientContact.client_person_id == models.Person.id)
    if q:
        query = query.where(models.ClientContact.name.ilike(f'%{q}%'))

    if not hasattr(models.ClientContact, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.ClientContact, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)
    joined_res = res.fetchall()
    client_contacts = []
    for client_contact, client_person in joined_res:
        client_contact.person = client_person
        client_contacts.append(client_contact)

    return client_contacts


@base.session_aware()
async def list_client_contacts_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientContact)
    if ids:
        query = query.where(models.ClientContact.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_contact_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientContact, id, session=session)


@base.session_aware()
async def get_client_contact_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    if q:
        q = f'%{q}%'
        where.append('name ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM client_contacts'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
