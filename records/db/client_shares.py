from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_share(values, session=None):
    client_share_class = models.ClientShare(**values)

    try:
        session.add(client_share_class)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientShare: %s" % e
        )

    return client_share_class


@base.session_aware()
async def update_client_share(client_share_class: models.ClientShare, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientShare).where(models.ClientShare.id == client_share_class.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_share_class.update(new_values)

    return client_share_class


@base.session_aware()
async def delete_client_share(id: int, session=None):
    delete_q = delete(models.ClientShare).where(models.ClientShare.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_shares(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.ClientShare, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_shares(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ClientShare).where(models.ClientShare.client_id == client_id)
    # if q:
    #     query = query.where(models.ClientShareClass.name.ilike(f'%{q}%'))

    # if not hasattr(models.ClientShareClass, order):
    #     order = 'id'
    # order_col = getattr(models.ClientShareClass, order)

    # if desc:
    #     order_col = order_col.desc()
    # query = query.order_by(order_col)
    res = await session.execute(query)

    res = res.scalars().fetchall()
    return res


@base.session_aware()
async def list_client_shares_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientShare)
    if ids:
        query = query.where(models.ClientShare.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_share_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientShare, id, session=session)


@base.session_aware()
async def get_client_share_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM client_shares'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
