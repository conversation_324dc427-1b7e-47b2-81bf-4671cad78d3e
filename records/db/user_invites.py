from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.exceptions import NotFoundException


@base.session_aware()
async def create_user_invite(values, session=None):
    invite = models.UserInvite(**values)

    try:
        session.add(invite)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for UserInvite: %s" % e
        )

    return invite


@base.session_aware()
async def get_user_invite(id: str, session=None):
    query = select(models.UserInvite).where(models.UserInvite.id == id)
    res = await session.execute(query)
    res = res.scalar()

    if not res:
        raise NotFoundException(f'Invite not found for id: {id}')
    return res


@base.session_aware()
async def delete_user_invite(id, session=None):
    invite = await get_user_invite(id)

    if not invite:
        raise RuntimeError('Invite not found for id: %s' % id)

    await session.delete(invite)
