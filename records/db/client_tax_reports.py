from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models


@base.session_aware()
async def create_client_tax_report(values, session=None):
    client_tax_report = models.TaxReport(**values)

    try:
        session.add(client_tax_report)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for TaxReport: %s" % e
        )

    return client_tax_report


@base.session_aware()
async def update_client_tax_report(client_tax_report: models.TaxReport, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.TaxReport).where(models.TaxReport.id == client_tax_report.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_tax_report.update(new_values)

    return client_tax_report


@base.session_aware()
async def delete_client_tax_report(id: int, session=None):
    delete_q = delete(models.TaxReport).where(models.TaxReport.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_tax_reports(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.TaxReport, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_tax_reports(
    client_id: str,
    order: str = 'fiscal_year',
    desc: bool = False,
    q: str = None,
    session=None
):
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.TaxReport).where(models.TaxReport.client_id == client_id)
    if q:
        # type or form_info or fiscal_year or note
        query = query.where(models.TaxReport.type.ilike(f'%{q}%').__or__(
            models.TaxReport.form_info.ilike(f'%{q}%')
        ).__or__(
            models.TaxReport.fiscal_year.ilike(f'%{q}%')
        ).__or__(
            models.TaxReport.note.ilike(f'%{q}%')
        ))

    if not hasattr(models.TaxReport, order):
        order = 'id'
    order_col = getattr(models.TaxReport, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    res = res.scalars().fetchall()
    return res


@base.session_aware()
async def list_client_tax_reports_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.TaxReport)
    if ids:
        query = query.where(models.TaxReport.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_tax_report_by_id(id: int, session=None):
    return await base.get_by_id(models.TaxReport, id, session=session)


@base.session_aware()
async def get_client_tax_report_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM client_tax_reports'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count


@base.session_aware()
async def list_client_tax_reports_by_file_id(file_id: str, session=None):
    """Get all tax reports that reference a specific file"""
    query = select(models.TaxReport).where(models.TaxReport.file_id == file_id)
    res = await session.execute(query)
    return res.scalars().fetchall()


@base.session_aware()
async def is_file_referenced_by_tax_reports(file_id: str, session=None):
    """Check if a file is referenced by any tax reports"""
    query = select(models.TaxReport.id).where(models.TaxReport.file_id == file_id).limit(1)
    res = await session.execute(query)
    return res.scalar() is not None


@base.session_aware()
async def get_existing_tax_report_by_client_year_type(client_id: str, fiscal_year: str, type: str, session=None):
    """Check if a tax report already exists for the same client, fiscal year, and type"""
    query = select(models.TaxReport).where(
        models.TaxReport.client_id == client_id,
        models.TaxReport.fiscal_year == fiscal_year,
        models.TaxReport.type == type
    )
    res = await session.execute(query)
    return res.scalar()
