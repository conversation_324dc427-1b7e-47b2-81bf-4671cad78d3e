import collections

from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_bank_account(values, session=None):
    bank_account = models.BankAccount(**values)

    try:
        session.add(bank_account)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for BankAccount: %s" % e
        )

    return bank_account


@base.session_aware()
async def update_bank_account(bank_account: models.BankAccount, new_values: dict, session=None):
    # new_values['updated_at'] = base.now()
    update_q = update(models.BankAccount).where(models.BankAccount.id == bank_account.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    bank_account.update(new_values)

    return bank_account


@base.session_aware()
async def delete_bank_account(id: str, session=None):
    delete_q = delete(models.BankAccount).where(models.BankAccount.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_bank_accounts(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.BankAccount, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_bank_accounts(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    offset = None
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.BankAccount, models.BankAccountAuthorizedSigner, models.Person, models.BankCard).where(models.BankAccount.client_id == client_id)
    query = query.outerjoin(models.BankAccountAuthorizedSigner, models.BankAccount.id == models.BankAccountAuthorizedSigner.bank_account_id)
    query = query.outerjoin(models.Person, models.BankAccountAuthorizedSigner.client_person_id == models.Person.id)
    query = query.outerjoin(models.BankCard, models.BankCard.bank_account_id == models.BankAccount.id)
    # if q:
    #     query = query.where(models.BankAccount.name.ilike(f'%{q}%'))

    if not hasattr(models.BankAccount, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.BankAccount, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)

    joined_res = await session.execute(query)
    joined_res = joined_res.fetchall()

    bank_accounts = {}
    for bank_account, auth_signer, person, bank_card in joined_res:
        if bank_account.id not in bank_accounts:
            # First time seeing this bank account
            bank_accounts[bank_account.id] = bank_account
            bank_account.authorized_signers = []
            bank_account.bank_cards = []

            # Add the first authorized signer if it exists
            if auth_signer:
                auth_signer.person = person
                bank_account.authorized_signers.append(auth_signer)

            # Add the first bank card if it exists
            if bank_card:
                bank_account.bank_cards.append(bank_card)
        else:
            # Bank account already exists, get the stored reference
            stored_bank_account = bank_accounts[bank_account.id]

            # Add authorized signer if it exists and is not already added
            if auth_signer:
                auth_signer.person = person
                # Check for duplicates by ID
                existing_signer_ids = [s.id for s in stored_bank_account.authorized_signers if s.id]
                if auth_signer.id not in existing_signer_ids:
                    stored_bank_account.authorized_signers.append(auth_signer)

            # Add bank card if it exists and is not already added
            if bank_card:
                # Check for duplicates by ID
                existing_card_ids = [c.id for c in stored_bank_account.bank_cards if c.id]
                if bank_card.id not in existing_card_ids:
                    stored_bank_account.bank_cards.append(bank_card)

    return list(bank_accounts.values())


@base.session_aware()
async def list_bank_accounts_all(
    ids: list[str] = None,
    session=None
):
    query = select(models.BankAccount)
    if ids:
        query = query.where(models.BankAccount.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_bank_account_by_id(id: str, session=None):
    return await base.get_by_id(models.BankAccount, id, session=session)


@base.session_aware()
async def get_bank_account_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM bank_accounts'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
