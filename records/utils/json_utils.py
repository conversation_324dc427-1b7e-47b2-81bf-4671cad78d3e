import asyncio
import copy
import datetime
import io
import json
import logging
import re
import threading
from typing import Mu<PERSON><PERSON><PERSON><PERSON>, Literal, Dict, Any

import jsf
import jsondiff
import jsonschema
import pandas as pd
from fastapi import HTTPException
from jsonschema.exceptions import ValidationError
from langchain_core.language_models import BaseChatModel

from records.utils import llm_utils


# For async in sync
_loop = asyncio.new_event_loop()
_thread_async = threading.Thread(target=_loop.run_forever, daemon=True, name='async_runner')
logger = logging.getLogger(__name__)


EMPTY_VALUES = {"", None, 0}
NA_VALUES = {"N/A", "Not provided", "", None, 0}
MERGE_LISTS_PROMPT = """Merge two JSON lists intelligently to produce a consolidated JSON list. Follow these rules:

1. **Object Matching Strategy**: Determine if objects are the same using these criteria in order of priority:
   - **Unique IDs**: Match on id, external_id, uid, or other unique identifiers
   - **Business Keys**: Match on domain-specific unique combinations:
     * Person: firstname+lastname, full_name, or email (if substantial)
     * Address: street+city+state, or street+zip (normalize street variations like "St"/"Street")
     * Bank Account: account_number+routing_number, or account_number+bank_name
     * Registration: registration_number+state, or entity_name+state+legal_entity_type
     * Contact: type+value (email/phone), or email/phone if unique
     * Service/Subscription: title+price_type, or service_id
     * Task: title+client_id+due_date, or external task identifiers
     * Card: last_4_digits+card_holder_name, or card_number (if available)
   - **Fuzzy Matching**: If no exact match, consider objects with 70%+ meaningful field overlap
   - **Type Consistency**: Only match objects of the same logical type

2. **Field Value Selection**: When merging matched objects, choose the best value for each field:
   - **Prefer non-empty**: Choose non-empty over ("", null, 0, "N/A", "Not provided", "None")
   - **Prefer detailed**: Choose longer, more specific, or formatted values over generic ones
   - **Prefer from "two"**: When both values are equally valid, prefer the value from list "two"
   - **Preserve all fields**: Include all unique fields from both objects (union of fields)
   - **Preserve all ids**: Keep all "id", "***_id", "uid", or other unique identifiers
   - **Handle conflicts**: For conflicting non-empty values, prefer the more complete one from "two"

3. **Unmatched Objects**: Include all objects that have no match in the other list

4. **Output**: Return only the merged JSON list, no explanations

List one:
{one}

List two:
{two}

Merged JSON list:
"""


def flatten_json(json_obj, parent_key='', sep='::', level=0):
    items = []
    level += 1
    indent = ' ' * ((level - 1) // 2)
    # indent = ''
    for k, v in json_obj.items():
        # new_key = f"{parent_key}{sep}{k}" if parent_key else k
        new_key = f"{indent}{k}" if parent_key else k
        if isinstance(v, MutableMapping):
            items.append((new_key, ''))
            items.extend(flatten_json(v, new_key, sep=sep, level=level+1))
        elif isinstance(v, list):
            for i, sub_item in enumerate(v):
                if isinstance(sub_item, dict):
                    new_sub_key = f"{new_key}{sep}{i}"
                    items.extend(flatten_json(sub_item, new_sub_key, sep=sep, level=level+1))
                else:
                    items.append((new_key, sub_item))
        else:
            items.append((new_key, v))
    return items


def merge_jsons(jsons: list[dict], llm: BaseChatModel = None) -> dict:
    if not jsons:
        return {}
    if len(jsons) == 1:
        return jsons[0]

    result = jsons[0]
    mode = 'overwrite_append' if not llm else 'overwrite_llm'
    if llm is not None:
        llm = llm_utils.set_cheapest_model(llm)
    for obj in jsons[1:]:
        result = dict_merge(result, obj, mode=mode, llm=llm, verbose=True)

    return result


async def amerge_jsons(jsons: list[dict], llm: BaseChatModel = None) -> dict:
    if not jsons:
        return {}
    if len(jsons) == 1:
        return jsons[0]

    result = jsons[0]
    mode = 'overwrite_append' if not llm else 'overwrite_llm'
    if llm is not None:
        llm = llm_utils.set_cheapest_model(llm)
    for obj in jsons[1:]:
        result = await adict_merge(result, obj, mode=mode, llm=llm, verbose=True)

    return result


def generate_dummy_json(schema: dict, mode: Literal['null', 'default'] = 'default') -> dict:
    example = jsf.JSF(schema).generate()
    if not example:
        # Probably not schema
        dummy = _fill_json(schema, mode=mode)
    else:
        dummy = _fill_json(example, mode=mode)

    return _keep_1_list(dummy)


def compare_dict_structure(one: dict, two: dict) -> bool:
    if set(list(one.keys())) != set(list(two.keys())):
        return False

    all_compare = True
    for k, v in one.items():
        if isinstance(v, dict):
            all_compare = all_compare and compare_dict_structure(one[k], two[k])
        if isinstance(v, list):
            all_compare = all_compare and compare_list_structure(one[k], two[k])

    return all_compare


def validate_schema(data: dict, schema: dict, raise_exception: bool = True):
    """
    Validate data against a JSON schema.

    Args:
        data: The data to validate
        schema: The JSON schema to validate against
        raise_exception: Whether to raise an HTTPException on validation failure

    Returns:
        None if validation succeeds

    Raises:
        HTTPException: If validation fails and raise_exception is True
    """
    try:
        jsonschema.validate(data, schema)
        return None
    except Exception as e:
        if raise_exception:
            raise HTTPException(400, str(e))
        else:
            return e


def parse_validation_error(error: Exception) -> Dict[str, Any]:
    """
    Parse a JSON schema validation error and return a structured error object with
    both technical details and a human-readable message.

    Args:
        error: The exception raised during validation

    Returns:
        A dictionary containing:
        - valid: False (validation failed)
        - error: The original error message
        - message: A human-readable error message
        - details: Structured error details including field path and error type
    """
    error_str = str(error)
    result = {
        'valid': False,
        'error': error_str,
        'message': 'Client data is not valid',
        'details': []
    }

    # Default human-readable message
    human_message = 'There was an issue with the provided data.'

    # Try to parse the error message
    if isinstance(error, ValidationError):
        # Get the path to the field with the error
        path = [str(p) for p in error.path] if error.path else []
        field_path = '.'.join(path) if path else 'root'

        # Get the validation error type
        validator = error.validator

        # Create a structured error detail
        detail = {
            'field': field_path,
            'validator': validator,
            'message': error.message
        }

        # Add the error detail to the result
        result['details'].append(detail)

        # Generate a human-readable message based on the error type
        if validator == 'required':
            missing_fields = error.validator_value
            if isinstance(missing_fields, list) and missing_fields:
                fields_str = ', '.join(f'"{f}"' for f in missing_fields)
                human_message = f'Required field(s) {fields_str} missing.'
        elif validator == 'type':
            expected_type = error.validator_value
            human_message = f'Field "{field_path}" should be of type {expected_type}.'
        elif validator == 'format':
            format_type = error.validator_value
            human_message = f'Field "{field_path}" does not match the required format: {format_type}.'
        elif validator == 'pattern':
            human_message = f'Field "{field_path}" does not match the required pattern.'
        elif validator == 'enum':
            allowed_values = error.validator_value
            values_str = ', '.join(f'"{v}"' for v in allowed_values)
            human_message = f'Field "{field_path}" must be one of: {values_str}.'
        elif validator == 'minimum':
            min_value = error.validator_value
            human_message = f'Field "{field_path}" must be greater than or equal to {min_value}.'
        elif validator == 'maximum':
            max_value = error.validator_value
            human_message = f'Field "{field_path}" must be less than or equal to {max_value}.'
        elif validator == 'minLength':
            min_length = error.validator_value
            human_message = f'Field "{field_path}" must have at least {min_length} characters.'
        elif validator == 'maxLength':
            max_length = error.validator_value
            human_message = f'Field "{field_path}" must have at most {max_length} characters.'
        elif validator == 'minItems':
            min_items = error.validator_value
            human_message = f'Field "{field_path}" must have at least {min_items} items.'
        elif validator == 'maxItems':
            max_items = error.validator_value
            human_message = f'Field "{field_path}" must have at most {max_items} items.'
        else:
            # Generic message for other validators
            human_message = f'Field "{field_path}" validation failed: {error.message}'
    else:
        # Try to extract field information from the error message using regex
        field_match = re.search(r"'([^']+)'\s+is a required property", error_str)
        if field_match:
            field_name = field_match.group(1)
            human_message = f'Required field "{field_name}" is missing.'
            result['details'].append({
                'field': field_name,
                'validator': 'required',
                'message': f'"{field_name}" is a required property'
            })

        # Try to match type errors
        type_match = re.search(r"'([^']+)'\s+is not of type\s+'([^']+)'", error_str)
        if type_match:
            field_value = type_match.group(1)
            expected_type = type_match.group(2)
            human_message = f'Value "{field_value}" is not of the expected type "{expected_type}".'
            result['details'].append({
                'field': 'unknown',
                'validator': 'type',
                'message': f'"{field_value}" is not of type "{expected_type}"'
            })

    # Update the human-readable message
    result['message'] = human_message

    return result


def parse_pydantic_error(error):
    """
    Parse a Pydantic ValidationError and return a structured error object with
    both technical details and a human-readable message.

    Args:
        error: The Pydantic ValidationError exception

    Returns:
        A dictionary containing:
        - valid: False (validation failed)
        - error: The original error message
        - message: A human-readable error message
        - details: Structured error details including field path and error type
    """
    result = {
        'valid': False,
        'error': str(error),
        'message': 'Client data is not valid',
        'details': []
    }

    # Check if it's a Pydantic ValidationError
    if hasattr(error, 'errors') and callable(error.errors):
        # Get the errors from the ValidationError
        errors = error.errors()

        # Process each error
        for err in errors:
            # Extract location information
            loc = err.get('loc', ())
            field_path = '.'.join(str(item) for item in loc) if loc else 'root'

            # Extract error type and message
            error_type = err.get('type', 'unknown_error')
            error_msg = err.get('msg', 'Unknown error')

            # Create a structured error detail
            detail = {
                'field': field_path,
                'validator': error_type,
                'message': error_msg
            }

            # Add the error detail to the result
            result['details'].append(detail)

        # Generate a human-readable message based on the first error
        if errors:
            first_error = errors[0]
            loc = first_error.get('loc', ())
            field_path = '.'.join(str(item) for item in loc) if loc else 'root'
            error_msg = first_error.get('msg', 'Unknown error')
            result['message'] = f'Error in field "{field_path}": {error_msg}'

    return result


def date_validator(date: datetime.datetime | str | None):
    if not date:
        return None
    # if not isinstance(date, datetime.datetime) and len(date) > 0:
    #     raise ValueError("date is not an empty string and not a valid date")
    return date


def clean_keys(data: dict, keys: list[str]):
    data = data.copy()
    keys += ['created_at', 'updated_at', 'id', 'client_id']
    for key in keys:
        data.pop(key, None)
    return data


def compare_list_structure(one: list, two: list):
    if not one or not two:
        return True

    f_one = one[0]
    f_two = two[0]
    if isinstance(f_one, dict) and isinstance(f_two, dict):
        return compare_dict_structure(f_one, f_two)
    return True


def diff_json(source, target):
    return jsondiff.diff(source, target, marshal=True)


def diff_json_db(source, target, exclude: list[str] = None):
    if not source and not target:
        return True

    if not source or not target:
        return False

    source = source.copy()
    target = target.copy()
    # none_values = [k for k, v in source.items() if v is None]
    # for k in none_values:
    #     source.pop(k, None)

    exclude_fields = ['id', 'client_id', 'created_at', 'updated_at']
    if exclude:
        exclude_fields += exclude
    for k in exclude_fields:
        source.pop(k, None)
        target.pop(k, None)

    # Handle datetime / date
    for k, v in source.items():
        if isinstance(v, datetime.datetime):
            source[k] = datetime.datetime.strftime(v, '%Y-%m-%d %H:%M:%S')
        elif isinstance(v, datetime.date):
            source[k] = v.isoformat()

    for k, v in target.items():
        if isinstance(v, datetime.datetime):
            target[k] = datetime.datetime.strftime(v, '%Y-%m-%d %H:%M:%S')
        elif isinstance(v, datetime.date):
            target[k] = v.isoformat()

    return diff_json(source, target)


def is_json_db_different(source: dict, target: dict, exclude: list[str] = None) -> bool:
    return diff_json_db(source, target, exclude) != {}


def patch_json(source, patch):
    return jsondiff.patch(source, patch, marshal=True)


def set_history_item(one, two, diff, item, res) -> dict:
    for k in one:
        if isinstance(one[k], dict):
            if k in diff:
                res[k] = set_history_item(one[k], two[k], diff[k], item, res[k])
        # if isinstance(one[k], list):
            # Add new item

            # pass
        else:
            if k in diff:
                val_item = copy.copy(item)
                val_item['value'] = two[k]
                val_item['diff'] = copy.copy(diff[k])

                if isinstance(res[k], list) and len(res[k]) > 0 and 'point_id' in res[k][0]:
                    # Append history item
                    res[k].append(val_item)
                else:
                    # Create history item list
                    res[k] = [val_item]
    return res


def _keep_1_list(data: dict) -> dict:
    for k in data:
        if isinstance(data[k], dict):
            data[k] = _keep_1_list(data[k])
        if isinstance(data[k], list):
            l = data[k]
            if len(l) >= 1:
                data[k] = [l[0]]
            else:
                continue

            if isinstance(l[0], dict):
                data[k][0] = _keep_1_list(data[k][0])

    return data


def _fill_json(val: dict | list, mode: Literal['null', 'default'] = 'default') -> dict:
    if isinstance(val, dict):
        for k in val:
            if isinstance(val[k], dict):
                val[k] = _fill_json(val[k])
            elif isinstance(val[k], (list, tuple)):
                val[k] = _fill_json(list(val[k]))
            else:
                val[k] = None if mode == 'null' else type(val[k])()
    else:
        for k in range(len(val)):
            if isinstance(val[k], dict):
                val[k] = _fill_json(val[k])
            elif isinstance(val[k], (list, tuple)):
                val[k] = _fill_json(list(val[k]))
            else:
                val[k] = None if mode == 'null' else type(val[k])()

    return val


def extract_json(text):
    match = re.search(r"({.*?})", text, re.DOTALL)
    if match:
        json_str = match.group(1).strip()
        # TODO: seems like it breaks special symbols including ó or á in translations
        # json_str = json_str.replace("\\", "\\\\")
        try:
            json_obj = json.loads(json_str)
            return json_obj
        except json.JSONDecodeError:
            json_str = text[text.index('{'):text.rindex('}')+1]
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                return None
    else:
        print("No JSON block found in the input text")
        return None


def extract_json_list(text):
    match = re.search(r"(\[.*?\])", text, re.DOTALL)
    if match:
        json_str = match.group(1).strip()
        # TODO: seems like it breaks special symbols including ó or á in translations
        # json_str = json_str.replace("\\", "\\\\")
        try:
            json_obj = json.loads(json_str)
            return json_obj
        except json.JSONDecodeError:
            return json.loads(text[text.index('['):text.rindex(']')+1])
    else:
        logger.warning("No JSON list block found in the input text")
        return None


def json_to_csv(json_data):
    flattened_data = flatten_json(json_data)

    df = pd.DataFrame(flattened_data, columns=['Name', 'Value'])
    csv_data = df.to_csv(index=False, sep=';')
    return csv_data


def json_to_xlsx(json_data):
    flattened_data = flatten_json(json_data)

    df = pd.DataFrame(flattened_data, columns=['Name', 'Value'])

    buf = io.BytesIO()
    # df.to_excel(buf, sheet_name='data', engine='xlsxwriter', index=False)
    writer = pd.ExcelWriter(buf)
    sheet_name = 'Data'
    df.to_excel(writer, sheet_name=sheet_name, index=False, engine='xlsxwriter')

    for column in df:
        column_width = max(df[column].astype(str).map(len).max(), len(column))
        col_idx = df.columns.get_loc(column)
        writer.sheets[sheet_name].set_column(col_idx, col_idx, column_width)

    writer.close()

    return buf.getvalue()


async def _merge_list_with_llm(
    llm: BaseChatModel,
    left_list: list,
    right_list: list,
    verbose: bool,
    task_key: str = None,
) -> list:
    # Optimize for empty lists
    if not left_list:
        return right_list
    if not right_list:
        return left_list
    prompt = MERGE_LISTS_PROMPT.format(
        one=json.dumps(left_list, indent=2),
        two=json.dumps(right_list, indent=2),
    )
    if verbose:
        logger.info(f"[MERGE] Prompt: {prompt}")
    llm_output = await llm.ainvoke(prompt)
    if verbose:
        logger.info(f"[MERGE] Output: {llm_output.content}")

    return extract_json_list(llm_output.content)


def run_async_func(coro):
    if not _thread_async.is_alive():
        _thread_async.start()
    future = asyncio.run_coroutine_threadsafe(coro, _loop)
    return future.result()


def dict_merge(
    dct_left: dict,
    merge_dct: dict,
    mode: str = 'overwrite',
    check_keys: bool = False,
    copy_data: bool = True,
    llm: BaseChatModel = None,
    verbose: bool = False,
) -> dict:
    return run_async_func(
        adict_merge(
            dct_left,
            merge_dct,
            mode=mode,
            check_keys=check_keys,
            copy_data=copy_data,
            llm=llm,
            verbose=verbose,
        )
    )


async def adict_merge(
    dct_left: dict,
    merge_dct: dict,
    mode: str = 'overwrite',
    check_keys: bool = False,
    copy_data: bool = True,
    llm: BaseChatModel = None,
    verbose: bool = False,
) -> dict:
    """
    Recursive dict merge. The method recurse down into dicts nested
    to an arbitrary depth, updating keys. The ``merge_dct`` is merged into ``dct_left``.

    Parameters
    ----------
    dct_left: dict
        Dictionary onto which the merge is executed

    merge_dct:dict
        Dictionary merged into dct

    mode:str, optional, default='safe'
        3 modes are allowed: "safe" or "overwrite" or "overwrite_append" or 'overwrite_llm'.
        "safe" will raise error if the 2 dicts have the same key and different values
        while "overwrite" will overwrite ``dct`` with the value of ``merge_dct``

    check_keys: bool
        Should the method check if keys from ``merge_dct`` are present in ``dct_left``
        and throw an error in case they are not

    copy_data: copy the source dict before editing or not. Default is True.
    llm: Use llm for smart list merging.
    verbose: log LLM usage or not

    Returns
    -------
    dict
        Merged dict
    """

    if not merge_dct:
        return dct_left

    dct = copy.deepcopy(dct_left) if copy_data else dct_left

    if mode not in ("safe", "overwrite", "overwrite_append", "overwrite_llm"):
        raise ValueError(f"dict_merge mode '{mode}' is not supported")

    # collect llm list-merge tasks
    llm_tasks = []
    # to remember where to store results: (key, parent_dict, task_index)
    llm_targets = []

    for k, v in merge_dct.items():

        if k not in dct.keys() and check_keys:
            raise Exception(f"Cannot overlay non existing config item '{k}'")

        if k in dct and isinstance(dct[k], dict) and isinstance(merge_dct[k], dict):
            dct[k] = await adict_merge(dct[k], merge_dct[k], mode, copy_data=False, check_keys=check_keys, llm=llm, verbose=verbose)
        elif k not in dct and isinstance(merge_dct[k], dict):
            dct[k] = merge_dct[k]
        elif mode == 'overwrite_llm' and k in dct and isinstance(dct[k], list) and isinstance(merge_dct[k], list):
            if llm is None:
                raise RuntimeError("LLM required for overwrite_llm mode")
            task = asyncio.create_task(_merge_list_with_llm(llm, dct.get(k), merge_dct[k], verbose, k))
            llm_targets.append((k, dct, len(llm_tasks)))
            llm_tasks.append(task)
        elif mode in ("overwrite", "overwrite_append", "overwrite_llm") and k in dct and isinstance(dct[k], list) and isinstance(merge_dct[k], list):
            if len(dct[k]) > 0:
                exceptions = list(NA_VALUES)
                need_replace = isinstance(dct[k][0], dict) and all(dct[k][0][kk] in exceptions for kk in dct[k][0])
                need_append = True
                if len(merge_dct[k]) > 0 and isinstance(merge_dct[k][0], dict) and isinstance(dct[k][0], dict):
                    # Use the keys from merge_dct[k][0] to check values, not dct[k][0]
                    need_append = not all(merge_dct[k][0].get(kk) in exceptions for kk in merge_dct[k][0])

                if dct[k][0] is None or need_replace:
                    dct[k] = merge_dct[k]
                else:
                    if need_append or not isinstance(merge_dct[k][0], dict):
                        dct[k] = dct[k] + merge_dct[k]
            else:
                dct[k] = dct[k] + merge_dct[k]
        elif mode in ("overwrite", "overwrite_append", "overwrite_llm") and k in dct and isinstance(dct[k], list) and not isinstance(merge_dct[k], list):
            # Disallow overwrite list by a scalar
            pass
        else:
            if mode == "safe":

                if k in dct and dct[k] is not None and dct[k] != merge_dct[k]:
                    raise Exception(
                        f"Trying to overwrite parameter '{k}' of value '{dct[k]}' "
                        f"with value '{merge_dct[k]}'. Operation not allowed in 'safe' mode"
                    )
                else:
                    dct[k] = merge_dct[k]

            if mode in ("overwrite", "overwrite_append", "overwrite_llm"):
                is_list_right = isinstance(merge_dct.get(k), list)
                is_list_left = isinstance(dct.get(k), list)

                if is_list_left and is_list_right:
                    dct[k] = merge_dct[k]
                    continue

                # Check if value is a dict before doing membership test with NA_VALUES
                is_merge_val_dict = isinstance(merge_dct.get(k), dict)
                is_dct_val_dict = isinstance(dct.get(k), dict)

                # For dictionaries, we consider them "not in NA_VALUES" by default
                merge_val_not_in_na = is_merge_val_dict or (not is_list_right and merge_dct.get(k) not in NA_VALUES)
                dct_val_in_empty = is_dct_val_dict or (not is_list_left and dct.get(k) in EMPTY_VALUES)

                if merge_val_not_in_na or k not in dct or dct_val_in_empty:
                    if merge_dct[k] is not None or k not in dct:
                        dct[k] = merge_dct[k]

    # await all LLM list-merge tasks and assign back
    if llm_tasks:
        results = await asyncio.gather(*llm_tasks)
        for key, parent, idx in llm_targets:
            parent[key] = results[idx]

    return dct


def _sync_list_merge(left: list, right: list) -> list:
    """
    Your original synchronous logic for 'overwrite' and 'overwrite_append'.
    Pulled out here for clarity.
    """
    if left:
        exceptions = list(NA_VALUES)
        need_replace = isinstance(left[0], dict) and all(left[0].get(k) in exceptions for k in left[0])
        need_append = True
        if right and isinstance(left[0], dict) and isinstance(right[0], dict):
            need_append = not all(right[0].get(k) in exceptions for k in right[0])

        if left[0] is None or need_replace:
            return right
        else:
            return left + right if need_append or not isinstance(right[0], dict) else left
    else:
        return left + right
