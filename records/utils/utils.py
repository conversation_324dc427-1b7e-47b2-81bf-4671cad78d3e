import hashlib
import json
import logging
import os
import uuid

import ulid
import yaml
from fastapi import HTTPException

logger = logging.getLogger(__name__)


def setup_logging():
    level = logging.INFO
    if os.getenv('DEBUG') == 'true':
        level = logging.DEBUG
    if os.getenv('DEBUG_NEMOGUARDRAILS') != 'true':
        logging.getLogger('nemoguardrails').setLevel(logging.WARNING)
    logging.basicConfig(
        format='%(asctime)s %(levelname)-5s %(name)-10s [-] %(message)s',
        level=level
    )
    logging.root.setLevel(level)


def load_json(filename):
    with open(filename, 'r') as f:
        data = f.read()
    return json.loads(data)


def load_yaml(filename):
    with open(filename, 'r') as f:
        data = f.read()
    return yaml.safe_load(data)


def validate_dataset_name(name):
    if '/' in name:
        raise HTTPException(422, 'Dataset name cannot contain "/"')


def boolean_string(s):
    if isinstance(s, bool):
        return s
    if s is None:
        return False

    s = s.lower()
    if not s:
        return False
    if s not in {'false', 'true'}:
        raise ValueError('Not a valid boolean string')
    return s == 'true'


def generate_unicode_uuid():
    return str(uuid.uuid4())


def generate_unicode_ulid() -> str:
    return str(ulid.ULID().to_uuid4())


def generate_pass_from_uuid(uuid: str):
    u = uuid.replace('-', '')
    base = u[1:17:2]
    return ''.join([a.upper() if i % 2 == 0 else a for i, a in enumerate(base)])


def uuid_to_integer(uuid: str) -> int:
    return int(uuid.replace('-', '')[20:], 16)


def generate_password():
    return generate_pass_from_uuid(generate_unicode_uuid())


def get_limit_offset(limit, page):
    offset = limit * (page - 1)
    return limit, offset


def generate_connection_string(connector='asyncpg'):
    db_type = os.environ.get('DB_TYPE', 'postgresql')
    user = os.environ.get('PGUSER')
    database = os.environ.get('PGDATABASE')
    password = os.environ.get('PGPASSWORD')
    host = os.environ.get('PGHOST')
    port = os.environ.get('PGPORT', 5432)

    return f'{db_type}+{connector}://{user}:{password}@{host}:{port}/{database}'


def get_base_url(req):
    global_base = get_global_base_url()
    req_base = str(req.base_url).removesuffix('/')
    if global_base.startswith('https'):
        req_base = req_base.replace('http', 'https', 1)
    return req_base


def get_global_base_url():
    return os.environ.get('BASE_URL', '')


def get_global_schema():
    return get_global_base_url().split(':')[0]


def get_schema(req):
    return req.base_url.scheme


def get_provided_answer_filename(ws, chat_id):
    return f'provided_answers_ws_{ws.name}.txt'


async def log_exceptions(awaitable):
    try:
        await awaitable
    except Exception as e:
        logger.exception(str(e))


def build_status(status, msg=None, string=False) -> dict | str:
    st = {"status": status, "message": msg}
    return json.dumps(st) if string else st


def maybe_dict(v: str | None, default=None):
    if not v:
        return default
    # noinspection PyBroadException
    try:
        return json.loads(v)
    except:
        return v


def build_full_title(first, last):
    return f"{first} {last}"


def clean_ein(ein: str):
    if not ein:
        return ein
    return ''.join(c for c in ein if c.isdigit())


def clean_name(name: str):
    if not name:
        return name
    return name.strip().replace(
        '  ', ' '
    ).replace(
        ' ,', ','
    ).replace(
        ', ', ','
    ).replace(
        '.', ''
    ).replace(
        ',', ''
    ).upper()


def reformat_ein(ein):
    ein = clean_ein(ein)
    if len(ein) > 2:
        return f"{ein[:2]}-{ein[2:]}"
    return ein


def date_to_human_str(date):
    month_map = {
        1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr',
        5: 'May', 6: 'Jun', 7: 'Jul', 8: 'Aug',
        9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec',
    }
    return f"{date.day} {month_map[date.month]} {date.year}"


def hash_sha256(data: str | bytes) -> str:
    if isinstance(data, str):
        data = data.encode()
    return hashlib.sha256(data).hexdigest()
