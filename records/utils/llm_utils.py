import os

# from langchain_anthropic import ChatAnthropic
from langchain_core.language_models import BaseChatModel
# from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI, AzureChatOpenAI

cheap_models = {
    'openai': 'gpt-4o-mini',
    'azure_openai': 'gpt-4o-mini',
    'anthropic': 'claude-3-haiku-20240307',
    'google': 'models/gemini-1.5-flash-latest',
}
tool_models = {
    'openai': 'gpt-4o-mini',
    'azure_openai': 'gpt-4o-mini',
    'anthropic': 'claude-3-haiku-20240307',
    'google': 'gemini-1.5-flash-latest',
}


def get_llm_params(org_config: dict):
    llm_type = org_config.get('llm_type', 'openai')
    params = {'llm_type': llm_type}
    if llm_type == 'openai':
        if org_config and org_config.get('openai') and org_config['openai'].get('api_key'):
            params.update(org_config.get('openai', {}))
        else:
            params.update({'api_key': os.getenv('OPENAI_API_KEY')})
        return params
    if llm_type == 'google' and not org_config.get(llm_type):
        llm_type = 'gemini'
    params.update(org_config.get(llm_type, {}))
    # raise ValueError(f'Only supporting OpenAI, Ollama, Gemini, Anthropic and MistralAI for now, got {llm_type}')

    return params


def set_cheapest_model(
    llm: BaseChatModel,
    custom_model: str = None,
    custom_tags: list[str] = None,
    custom_metadata: dict = None,
) -> BaseChatModel:

    # Set cheap/faster model here and re-init for avoid intersection in another threads
    if isinstance(llm, ChatOpenAI):
        class_dict = llm.__dict__.copy()
        class_dict.pop('client', None)
        class_dict.pop('async_client', None)
        class_dict.pop('root_client', None)
        class_dict.pop('root_async_client', None)
        class_dict.pop('_serialized', None)

        class_dict['model_name'] = custom_model if custom_model else cheap_models['openai']
        llm = llm.__class__(**class_dict)
    if isinstance(llm, AzureChatOpenAI):
        class_dict = llm.__dict__.copy()
        class_dict.pop('client', None)
        class_dict.pop('async_client', None)
        class_dict.pop('root_client', None)
        class_dict.pop('root_async_client', None)
        class_dict.pop('_serialized', None)

        class_dict['model_name'] = custom_model if custom_model else cheap_models['azure_openai']
        class_dict['azure_deployment'] = custom_model if custom_model else cheap_models['azure_openai']
        llm = llm.__class__(**class_dict)
    # if isinstance(llm, ChatAnthropic):
    #     class_dict = llm.__dict__.copy()
    #     class_dict.pop('_client', None)
    #     class_dict.pop('_async_client', None)
    #     class_dict.pop('_serialized', None)
    #
    #     class_dict['model'] = custom_model if custom_model else cheap_models['anthropic']
    #     llm = llm.__class__(**class_dict)
    # if isinstance(llm, ChatGoogleGenerativeAI):
    #     class_dict = llm.__dict__.copy()
    #     class_dict.pop('client', None)
    #     class_dict.pop('async_client', None)
    #     class_dict.pop('_serialized', None)
    #
    #     llm = llm.__class__(**class_dict)
    #     set_model = custom_model if custom_model else cheap_models['google']
    #     set_model = f'models/{set_model}' if not set_model.startswith('models/') else set_model
    #     llm.model = set_model

    if custom_tags:
        llm.custom_tags = custom_tags
    if custom_metadata:
        llm.metadata.update(custom_metadata)

    return llm


def get_model_name(llm: BaseChatModel):
    if hasattr(llm, 'model_name'):
        return llm.model_name
    elif hasattr(llm, 'model'):
        return llm.model

    return None
