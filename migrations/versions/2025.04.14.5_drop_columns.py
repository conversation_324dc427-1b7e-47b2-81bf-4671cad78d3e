"""drop columns

Revision ID: 2025.04.14.5
Revises: 2025.04.14.4
Create Date: 2025-04-14 16:42:49.234227

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.14.5'
down_revision: Union[str, None] = '2025.04.14.4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('clients', 'none_banks')
    op.drop_column('clients', 'account')
    op.drop_column('clients', 'account_add')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clients', sa.Column('account_add', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('clients', sa.Column('account', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('clients', sa.Column('none_banks', sa.BOOLEAN(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
