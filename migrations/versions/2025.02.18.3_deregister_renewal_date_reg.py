"""deregister renewal date reg

Revision ID: 2025.02.18.3
Revises: 2025.02.18.2
Create Date: 2025-02-18 19:25:46.847339

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.18.3'
down_revision: Union[str, None] = '2025.02.18.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_registrations', sa.Column('deregister_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_registrations', sa.Column('last_renewal_date', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_registrations', 'last_renewal_date')
    op.drop_column('client_registrations', 'deregister_date')
    # ### end Alembic commands ###
