"""delete control_by

Revision ID: 2025.04.14.8
Revises: 2025.04.14.7
Create Date: 2025-04-14 21:52:33.540409

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

from records.utils import utils

# revision identifiers, used by Alembic.
revision: str = '2025.04.14.8'
down_revision: Union[str, None] = '2025.04.14.7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()

    clients_bank_diff = conn.execute(sa.text(
        "select clients.id as client_id, control_by, ba.controlled_by, ba.id as ba_id from clients "
        "JOIN bank_accounts ba on ba.client_id = clients.id where controlled_by is NULL and control_by is not NULL;"
    ))
    for diff in clients_bank_diff.fetchall():
        client_id, client_control_by, ba_controlled_by, ba_id = diff
        conn.execute(sa.text("UPDATE bank_accounts SET controlled_by = :controlled_by WHERE id = :id"),
                     parameters={'controlled_by': client_control_by, 'id': ba_id})

    op.drop_column('clients', 'control_by')

    bank_accounts = conn.execute(
        sa.text("select ba.id, ba.client_id from bank_accounts ba where length(ba.id) < 36;")).fetchall()
    for bank_account in bank_accounts:
        new_id = utils.generate_unicode_ulid()
        conn.execute(sa.text("UPDATE bank_accounts SET id = :new_id WHERE id = :id"),
                     parameters={'id': bank_account[0], 'new_id': new_id})
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clients', sa.Column('control_by', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
