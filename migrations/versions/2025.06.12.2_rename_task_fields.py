"""rename task fields

Revision ID: 2025.06.12.2
Revises: 2025.06.12.1
Create Date: 2025-06-12 17:46:26.940585

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.12.2'
down_revision: Union[str, None] = '2025.06.12.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'client_tasks', 'task',
        new_column_name='name',
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'client_tasks', 'name',
        new_column_name='task',
    )
    # ### end Alembic commands ###
