"""crm base tables

Revision ID: 2025.01.17.0
Revises: 2025.01.15.0
Create Date: 2025-01-17 13:38:53.242625

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.01.17.0'
down_revision: Union[str, None] = '2025.01.15.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('addresses',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('uid', sa.String(length=255), nullable=False),
    sa.Column('title', sa.String(length=50), nullable=False),
    sa.Column('street', sa.String(length=255), nullable=True),
    sa.Column('pobox', sa.String(length=50), nullable=True),
    sa.Column('city', sa.String(length=100), nullable=False),
    sa.Column('state', sa.CHAR(length=2), nullable=False),
    sa.Column('zip', sa.String(length=10), nullable=False),
    sa.Column('country', sa.String(length=100), nullable=False),
    sa.Column('client_id', sa.Integer(), nullable=True),
    sa.Column('client_person_id', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uid')
    )
    op.create_index(op.f('ix_addresses_client_id'), 'addresses', ['client_id'], unique=False)
    op.create_index(op.f('ix_addresses_client_person_id'), 'addresses', ['client_person_id'], unique=False)
    op.create_table('bank_accounts',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('client_id', sa.Integer(), nullable=True),
    sa.Column('client_person_id', sa.Integer(), nullable=True),
    sa.Column('aba_number', sa.String(length=9), nullable=False),
    sa.Column('account_number', sa.String(length=20), nullable=False),
    sa.Column('bank_name', sa.String(length=255), nullable=False),
    sa.Column('account_type', sa.String(length=30), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bank_accounts_client_id'), 'bank_accounts', ['client_id'], unique=False)
    op.create_table('catalogs',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('options', postgresql.JSON(astext_type=sa.Text()), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_persons',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('uid', sa.String(length=255), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('firstname', sa.String(length=100), nullable=True),
    sa.Column('lastname', sa.String(length=100), nullable=True),
    sa.Column('legal_full_name', sa.String(length=255), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('pcm', sa.String(length=255), nullable=True),
    sa.Column('citizenship', sa.String(length=100), nullable=True),
    sa.Column('companies', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('uid')
    )
    op.create_table('clients',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('uid', sa.String(length=255), nullable=False),
    sa.Column('date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('changed', sa.DateTime(timezone=True), nullable=True),
    sa.Column('client_name', sa.String(length=255), nullable=False),
    sa.Column('status', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('active_since', sa.Date(), nullable=True),
    sa.Column('source', sa.String(length=50), nullable=True),
    sa.Column('total_shares', sa.Integer(), nullable=True),
    sa.Column('optional', sa.Integer(), nullable=True),
    sa.Column('naicscode', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uid')
    )
    op.create_table('cp575',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('content', postgresql.JSON(astext_type=sa.Text()), nullable=True),
    sa.Column('client_id', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_cp575_client_id'), 'cp575', ['client_id'], unique=False)
    op.create_table('files',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('uid', sa.String(length=255), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('file_type', sa.String(length=50), nullable=False),
    sa.Column('doc_type', sa.String(length=255), nullable=True),
    sa.Column('client_id', sa.Integer(), nullable=False),
    sa.Column('url', sa.Text(), nullable=True),
    sa.Column('status', postgresql.JSON(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uid')
    )
    op.create_index(op.f('ix_files_client_id'), 'files', ['client_id'], unique=False)
    op.create_table('form_ss4',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('client_id', sa.Integer(), nullable=False),
    sa.Column('mailing_address', sa.String(length=255), nullable=False),
    sa.Column('legal_type', sa.String(length=50), nullable=False),
    sa.Column('closing_month', sa.String(length=10), nullable=False),
    sa.Column('principal_activity', sa.String(length=255), nullable=False),
    sa.Column('products_or_services', sa.String(length=255), nullable=False),
    sa.Column('tax_id_ein', sa.String(length=50), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tax_id_ein')
    )
    op.create_index(op.f('ix_form_ss4_client_id'), 'form_ss4', ['client_id'], unique=False)
    op.create_table('legal_corporate_documents',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('client_id', sa.Integer(), nullable=False),
    sa.Column('company_name', sa.String(length=255), nullable=False),
    sa.Column('legal_entity_type', sa.String(length=50), nullable=False),
    sa.Column('state_of_registration', sa.CHAR(length=2), nullable=False),
    sa.Column('file_number', sa.String(length=50), nullable=False),
    sa.Column('filed_date', sa.Date(), nullable=False),
    sa.Column('registered_office_address', sa.String(length=255), nullable=False),
    sa.Column('registered_agent_name', sa.String(length=255), nullable=False),
    sa.Column('authorized_shares', sa.Integer(), nullable=True),
    sa.Column('par_value_per_share', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_legal_corporate_documents_client_id'), 'legal_corporate_documents', ['client_id'], unique=False)
    op.create_table('personal_documents',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('client_person_id', sa.Integer(), nullable=True),
    sa.Column('document_type', sa.String(length=200), nullable=False),
    sa.Column('content', postgresql.JSON(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_personal_documents_client_person_id'), 'personal_documents', ['client_person_id'], unique=False)
    op.create_table('reg_agents',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('uid', sa.String(length=255), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('nickname', sa.String(length=255), nullable=True),
    sa.Column('address', sa.String(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uid')
    )
    op.create_table('services',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('uid', sa.String(length=255), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('price', sa.Double(), nullable=False),
    sa.Column('price_type', sa.String(length=30), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uid')
    )
    op.create_table('sources',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('sources')
    op.drop_table('services')
    op.drop_table('reg_agents')
    op.drop_index(op.f('ix_personal_documents_client_person_id'), table_name='personal_documents')
    op.drop_table('personal_documents')
    op.drop_index(op.f('ix_legal_corporate_documents_client_id'), table_name='legal_corporate_documents')
    op.drop_table('legal_corporate_documents')
    op.drop_index(op.f('ix_form_ss4_client_id'), table_name='form_ss4')
    op.drop_table('form_ss4')
    op.drop_index(op.f('ix_files_client_id'), table_name='files')
    op.drop_table('files')
    op.drop_index(op.f('ix_cp575_client_id'), table_name='cp575')
    op.drop_table('cp575')
    op.drop_table('clients')
    op.drop_table('client_persons')
    op.drop_table('catalogs')
    op.drop_index(op.f('ix_bank_accounts_client_id'), table_name='bank_accounts')
    op.drop_table('bank_accounts')
    op.drop_index(op.f('ix_addresses_client_person_id'), table_name='addresses')
    op.drop_index(op.f('ix_addresses_client_id'), table_name='addresses')
    op.drop_table('addresses')
    # ### end Alembic commands ###
