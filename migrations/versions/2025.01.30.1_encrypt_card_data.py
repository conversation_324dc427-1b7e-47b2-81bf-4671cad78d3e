"""encrypt card data

Revision ID: 2025.01.30.1
Revises: 2025.01.30.0
Create Date: 2025-01-30 16:54:37.033643

"""
from typing import Sequence, Union

import inspect
from alembic import op
import sqlalchemy as sa

from records.db import models
from records.utils import blowfish_utils

# revision identifiers, used by Alembic.
revision: str = '2025.01.30.1'
down_revision: Union[str, None] = '2025.01.30.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # conn = op.get_bind()
    # model_classes = [getattr(models, model) for model in dir(models) if inspect.isclass(getattr(models, model))]
    # for model_class in model_classes:
    #     if hasattr(model_class, '__encrypt_columns__'):
    #         table_name = model_class.__tablename__
    #         columns = model_class.__encrypt_columns__
    #         if not columns:
    #             continue
    #         columns = columns
    #         res = conn.execute(sa.text(f"SELECT id, {", ".join(columns)} FROM {table_name}"))
    #         results = res.fetchall()
    #         # results = [{columns[i]: o[i] for i in range(len(columns))} for o in results]
    #         for result in results:
    #             item = dict(zip(['id'] + columns, result))
    #             for column in columns:
    #                 if item[column] is None:
    #                     continue
    #                 try:
    #                     blowfish_utils.decrypt_string(item[column])
    #                 except:
    #                     # Not encrypted, encrypt
    #                     print(f'val "{item[column]}"')
    #                     encrypted = blowfish_utils.encrypt_string(item[column])
    #                     # Save
    #                     conn.execute(
    #                         sa.text(f"UPDATE {table_name} SET {column} = :{column} WHERE id = :id"),
    #                         parameters={column: encrypted, 'id': item['id']}
    #                     )
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
