"""full to full title

Revision ID: 2025.03.03.0
Revises: 2025.02.18.3
Create Date: 2025-03-03 16:28:57.413031

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.03.0'
down_revision: Union[str, None] = '2025.02.18.3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('addresses', 'full_address', new_column_name='full_title')
    op.alter_column('client_persons', 'full_name', new_column_name='full_title')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_persons', 'full_title', new_column_name='full_name')
    op.alter_column('addresses', 'full_title', new_column_name='full_address')
    # ### end Alembic commands ###
