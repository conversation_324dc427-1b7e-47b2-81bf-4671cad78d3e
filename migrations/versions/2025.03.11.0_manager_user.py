"""manager user

Revision ID: 2025.03.11.0
Revises: 2025.03.03.0
Create Date: 2025-03-11 12:10:15.963879

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '2025.03.11.0'
down_revision: Union[str, None] = '2025.03.03.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE managers SET userid = NULL")
    op.alter_column(
        'managers', 'userid',
        existing_type=sa.VARCHAR(),
        type_=sa.BigInteger(),
        existing_nullable=True,
        postgresql_using='userid::bigint'
    )
    op.alter_column('managers', 'userid',
                    new_column_name='user_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('managers', 'user_id',
                    new_column_name='userid')
    op.alter_column('managers', 'userid',
                    existing_type=sa.BigInteger(),
                    type_=sa.VARCHAR(),
                    existing_nullable=True)
    # ### end Alembic commands ###
