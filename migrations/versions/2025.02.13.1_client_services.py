"""client_services

Revision ID: 2025.02.13.1
Revises: 2025.02.13.0
Create Date: 2025-02-13 16:14:38.217305

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.13.1'
down_revision: Union[str, None] = '2025.02.13.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('client_services_service_uid_fkey', 'client_services', type_='foreignkey')
    op.drop_constraint('services_uid_key', 'services', type_='unique')
    op.drop_column('services', 'id')

    op.alter_column('client_services', 'service_uid', new_column_name='service_id')
    op.alter_column('services', 'uid', new_column_name='id')
    op.create_primary_key(None, 'services', ['id'])
    op.create_foreign_key(None, 'client_services', 'services', ['service_id'], ['id'])

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('client_services_service_id_fkey', 'client_services', type_='foreignkey')
    op.alter_column('services', 'id', new_column_name='uid')
    op.alter_column('client_services', 'service_id', new_column_name='service_uid')

    op.add_column('services', sa.Column('id', sa.Integer, primary_key=True, autoincrement=True))
    op.create_unique_constraint('services_uid_key', 'services', ['uid'])
    op.create_foreign_key('client_services_service_uid_fkey', 'client_services', 'services', ['service_uid'], ['uid'])
    # ### end Alembic commands ###
