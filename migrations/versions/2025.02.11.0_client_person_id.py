"""client person id

Revision ID: 2025.02.11.0
Revises: 2025.02.10.0
Create Date: 2025-02-11 17:29:01.822170

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.11.0'
down_revision: Union[str, None] = '2025.02.10.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE bank_accounts DROP CONSTRAINT bank_accounts_client_person_id_fkey;")
    op.execute("ALTER TABLE client_persons DROP CONSTRAINT client_persons_pkey;")
    op.execute("ALTER TABLE client_persons DROP CONSTRAINT client_persons_uid_key;")
    op.execute("ALTER TABLE client_persons DROP COLUMN id;")
    op.execute("ALTER TABLE client_persons ADD CONSTRAINT client_persons_pkey PRIMARY KEY (uid)")
    op.execute("ALTER TABLE bank_accounts ADD CONSTRAINT bank_accounts_client_person_id_fkey FOREIGN KEY (client_person_id) REFERENCES client_persons(uid);")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE bank_accounts DROP CONSTRAINT bank_accounts_client_person_id_fkey;")
    op.execute("ALTER TABLE client_persons DROP CONSTRAINT client_persons_pkey;")
    op.execute("ALTER TABLE client_persons ADD COLUMN id SERIAL;")
    op.execute("ALTER TABLE client_persons ADD CONSTRAINT client_persons_uid_key UNIQUE (uid);")
    op.execute("ALTER TABLE client_persons ADD CONSTRAINT client_persons_pkey PRIMARY KEY (id);")
    op.execute("ALTER TABLE bank_accounts ADD CONSTRAINT bank_accounts_client_person_id_fkey FOREIGN KEY (client_person_id) REFERENCES client_persons(id);")
    # ### end Alembic commands ###
