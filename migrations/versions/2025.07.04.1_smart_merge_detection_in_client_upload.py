"""smart merge detection in client upload

Revision ID: 2025.07.04.1
Revises: 2025.07.04.0
Create Date: 2025-07-04 18:54:59.026982

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.04.1'
down_revision: Union[str, None] = '2025.07.04.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_uploads', sa.Column('smart_merge_detection_id', sa.Integer(), nullable=True))
    op.add_column('client_uploads', sa.Column('smart_merge_detection_item_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_uploads', 'smart_merge_detection_item_id')
    op.drop_column('client_uploads', 'smart_merge_detection_id')
    # ### end Alembic commands ###
