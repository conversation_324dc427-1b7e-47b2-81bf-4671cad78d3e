"""client_services_ids

Revision ID: 2025.02.13.2
Revises: 2025.02.13.1
Create Date: 2025-02-13 16:37:01.345652

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.13.2'
down_revision: Union[str, None] = '2025.02.13.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('services', 'id',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.alter_column('client_services', 'client_id',
                    existing_type=sa.VARCHAR(length=255),
                    type_=sa.String(length=36),
                    existing_nullable=False)
    op.alter_column('client_services', 'service_id',
                    existing_type=sa.VARCHAR(length=255),
                    type_=sa.String(length=36),
                    existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('services', 'id',
               existing_type=sa.String(length=36),
               type_=sa.VARCHAR(length=255),
               existing_nullable=False)
    # ### end Alembic commands ###
