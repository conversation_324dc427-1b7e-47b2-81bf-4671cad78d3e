"""client authorized signer created updated

Revision ID: 2025.04.18.5
Revises: 2025.04.18.4
Create Date: 2025-04-18 17:33:57.078231

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.18.5'
down_revision: Union[str, None] = '2025.04.18.4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_authorized_signers', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_authorized_signers', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_authorized_signers', 'updated_at')
    op.drop_column('client_authorized_signers', 'created_at')
    # ### end Alembic commands ###
