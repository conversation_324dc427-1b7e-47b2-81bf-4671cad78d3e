"""optional share count

Revision ID: 2025.04.14.1
Revises: 2025.04.14.0
Create Date: 2025-04-14 15:29:46.384428

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '2025.04.14.1'
down_revision: Union[str, None] = '2025.04.14.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'clients', 'optional',
        new_column_name='optional_share_count')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'clients', 'optional_share_count',
        new_column_name='optional')
    # ### end Alembic commands ###
