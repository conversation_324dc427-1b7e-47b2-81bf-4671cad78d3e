"""tax report model

Revision ID: 2025.07.11.0
Revises: 2025.07.10.0
Create Date: 2025-07-11 14:14:57.831514

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.11.0'
down_revision: Union[str, None] = '2025.07.10.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("DELETE FROM tax_reports;")
    op.add_column('tax_reports', sa.Column('form_info', sa.String(), nullable=False))
    op.add_column('tax_reports', sa.Column('filed_by', sa.String(), nullable=False))
    op.add_column('tax_reports', sa.Column('filed_date', sa.DateTime(timezone=True), nullable=True))
    op.drop_column('tax_reports', 'is_reporting_1099')
    op.drop_column('tax_reports', 'file_name')

    # rename name to type
    op.alter_column('tax_reports', 'name', new_column_name='type')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tax_reports', sa.Column('file_name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('tax_reports', sa.Column('is_reporting_1099', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.drop_column('tax_reports', 'filed_date')
    op.drop_column('tax_reports', 'filed_by')
    op.drop_column('tax_reports', 'form_info')
    op.alter_column('tax_reports', 'type', new_column_name='name')
    # ### end Alembic commands ###
