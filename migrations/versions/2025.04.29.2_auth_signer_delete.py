"""auth signer delete

Revision ID: 2025.04.29.2
Revises: 2025.04.29.1
Create Date: 2025-04-29 16:01:17.705218

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.29.2'
down_revision: Union[str, None] = '2025.04.29.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('bank_accounts_authorized_signer_person_id_fkey', 'bank_accounts', type_='foreignkey')
    op.drop_column('bank_accounts', 'authorized_signer_person_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bank_accounts', sa.Column('authorized_signer_person_id', sa.VARCHAR(length=36), autoincrement=False, nullable=True))
    op.create_foreign_key('bank_accounts_authorized_signer_person_id_fkey', 'bank_accounts', 'client_persons', ['authorized_signer_person_id'], ['id'])
    # ### end Alembic commands ###
