"""rename tables

Revision ID: 2025.06.19.0
Revises: 2025.06.18.2
Create Date: 2025-06-19 12:48:50.404061

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.19.0'
down_revision: Union[str, None] = '2025.06.18.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # client_shareholders -> client_llc_shareholders
    # client_share_classes -> client_shares
    op.rename_table('client_shareholders', 'client_llc_shareholders')
    op.rename_table('client_share_classes', 'client_shares')
    # rename connected seqs
    op.execute("ALTER SEQUENCE client_share_classes_id_seq RENAME TO client_shares_id_seq")

    # Add is_managing_member
    op.add_column('client_llc_shareholders', sa.Column('is_managing_member', sa.<PERSON><PERSON><PERSON>(), nullable=True))


def downgrade() -> None:
    op.rename_table('client_llc_shareholders', 'client_shareholders')
    op.rename_table('client_shares', 'client_share_classes')
    op.execute("ALTER SEQUENCE client_llc_shareholders_id_seq RENAME TO client_shareholders_id_seq")
    op.execute("ALTER SEQUENCE client_shares_id_seq RENAME TO client_share_classes_id_seq")
