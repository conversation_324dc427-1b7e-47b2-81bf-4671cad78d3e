"""delete disable secreg

Revision ID: 2025.04.14.7
Revises: 2025.04.14.6
Create Date: 2025-04-14 17:39:12.055553

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.14.7'
down_revision: Union[str, None] = '2025.04.14.6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('clients', 'disable_secreg')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clients', sa.Column('disable_secreg', sa.BOOLEAN(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
