"""file updated index

Revision ID: 2025.07.14.0
Revises: 2025.07.11.0
Create Date: 2025-07-14 16:03:37.838507

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.14.0'
down_revision: Union[str, None] = '2025.07.11.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_files_updated_at'), 'files', ['updated_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_files_updated_at'), table_name='files')
    # ### end Alembic commands ###
