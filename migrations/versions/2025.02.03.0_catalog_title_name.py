"""catalog title name

Revision ID: 2025.02.03.0
Revises: 2025.01.30.2
Create Date: 2025-02-03 22:10:04.489447

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.03.0'
down_revision: Union[str, None] = '2025.01.30.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('catalogs', 'title', new_column_name='name')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('catalogs', 'name', new_column_name='title')
    # ### end Alembic commands ###
