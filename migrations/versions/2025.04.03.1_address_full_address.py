"""address full address

Revision ID: 2025.04.03.1
Revises: 2025.04.03.0
Create Date: 2025-04-03 13:55:19.640405

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.03.1'
down_revision: Union[str, None] = '2025.04.03.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('addresses', 'full_title', new_column_name='full_address')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('addresses', 'full_address', new_column_name='full_title')
    # ### end Alembic commands ###
