"""bank account id string

Revision ID: 2025.01.29.1
Revises: 2025.01.29.0
Create Date: 2025-01-29 12:55:50.330191

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.01.29.1'
down_revision: Union[str, None] = '2025.01.29.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('bank_accounts', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.drop_column('bank_accounts', 'uid')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bank_accounts', sa.Column('uid', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.alter_column('bank_accounts', 'id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               existing_nullable=False)
    # ### end Alembic commands ###
