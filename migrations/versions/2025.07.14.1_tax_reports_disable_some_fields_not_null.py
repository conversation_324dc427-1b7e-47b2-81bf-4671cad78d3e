"""tax reports disable some fields not null

Revision ID: 2025.07.14.1
Revises: 2025.07.14.0
Create Date: 2025-07-14 19:47:03.895476

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.14.1'
down_revision: Union[str, None] = '2025.07.14.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('tax_reports', 'form_info',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('tax_reports', 'filed_by',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('tax_reports', 'filed_by',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('tax_reports', 'form_info',
               existing_type=sa.VARCHAR(),
               nullable=False)
    # ### end Alembic commands ###
