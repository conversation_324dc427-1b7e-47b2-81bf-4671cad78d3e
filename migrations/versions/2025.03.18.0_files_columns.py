"""files columns

Revision ID: 2025.03.18.0
Revises: 2025.03.13.0
Create Date: 2025-03-18 18:38:59.147191

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.18.0'
down_revision: Union[str, None] = '2025.03.13.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('files', sa.Column('hash', sa.String(length=64), nullable=True))
    op.add_column('files', sa.Column('manager_id', sa.String(length=36), nullable=True))
    op.create_index(op.f('ix_files_hash'), 'files', ['hash'], unique=False)
    op.create_index(op.f('ix_files_name'), 'files', ['name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_files_name'), table_name='files')
    op.drop_index(op.f('ix_files_hash'), table_name='files')
    op.drop_column('files', 'manager_id')
    op.drop_column('files', 'hash')
    # ### end Alembic commands ###
