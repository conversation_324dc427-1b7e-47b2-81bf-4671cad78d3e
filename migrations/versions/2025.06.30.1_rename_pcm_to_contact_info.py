"""rename pcm to contact_info

Revision ID: 2025.06.30.1
Revises: 2025.06.30.0
Create Date: 2025-06-30 15:54:10.718479

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.30.1'
down_revision: Union[str, None] = '2025.06.30.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_persons', 'pcm', new_column_name='contact_info')
    op.alter_column('client_contacts', 'pcm', new_column_name='contact_info')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_persons', 'contact_info', new_column_name='pcm')
    op.alter_column('client_contacts', 'contact_info', new_column_name='pcm')
    # ### end Alembic commands ###
