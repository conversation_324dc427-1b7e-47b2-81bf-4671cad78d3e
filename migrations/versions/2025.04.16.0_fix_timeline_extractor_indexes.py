"""fix timeline/extractor indexes

Revision ID: 2025.04.16.0
Revises: 2025.04.15.1
Create Date: 2025-04-16 18:54:38.807448

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.16.0'
down_revision: Union[str, None] = '2025.04.15.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('client_timelines_unique', 'client_timelines', ['client_id', 'type'])
    op.execute("ALTER TABLE client_extractors drop constraint IF EXISTS client_extractors_type_key;")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('client_timelines_unique', 'client_timelines', type_='unique')
    # ### end Alembic commands ###
