"""remove vesting col

Revision ID: 2025.06.19.4
Revises: 2025.06.19.3
Create Date: 2025-06-19 22:23:11.877932

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.19.4'
down_revision: Union[str, None] = '2025.06.19.3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_capitalizations', 'vesting')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_capitalizations', sa.Column('vesting', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
