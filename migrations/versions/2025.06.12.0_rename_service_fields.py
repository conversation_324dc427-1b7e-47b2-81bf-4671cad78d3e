"""rename service fields

Revision ID: 2025.06.12.0
Revises: 2025.06.11.1
Create Date: 2025-06-12 17:23:06.442382

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '2025.06.12.0'
down_revision: Union[str, None] = '2025.06.11.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'client_services', 'active_until',
        new_column_name='end_date',
    )
    op.alter_column(
        'client_services', 'active_since',
        new_column_name='start_date',
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'client_services', 'end_date',
        new_column_name='active_until',
    )
    op.alter_column(
        'client_services', 'start_date',
        new_column_name='active_since',
    )
    # ### end Alembic commands ###
