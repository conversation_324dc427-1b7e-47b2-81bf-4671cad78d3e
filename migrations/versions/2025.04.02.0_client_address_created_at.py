"""client address created at

Revision ID: 2025.04.02.0
Revises: 2025.03.26.1
Create Date: 2025-04-02 16:00:57.002490

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.02.0'
down_revision: Union[str, None] = '2025.03.26.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE client_addresses SET created_at = '2025-02-10 15:34:00' WHERE created_at IS NULL")
    op.execute("UPDATE client_addresses SET updated_at = '2025-02-10 15:34:00' WHERE updated_at IS NULL")
    op.execute("UPDATE addresses SET created_at = '2025-02-10 15:34:00' WHERE created_at IS NULL")
    op.execute("UPDATE addresses SET updated_at = '2025-02-10 15:34:00' WHERE updated_at IS NULL")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
