"""main contact flag

Revision ID: 2025.06.18.1
Revises: 2025.06.18.0
Create Date: 2025-06-18 13:32:06.443178

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.18.1'
down_revision: Union[str, None] = '2025.06.18.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_contacts', sa.Column('is_main', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_contacts', 'is_main')
    # ### end Alembic commands ###
