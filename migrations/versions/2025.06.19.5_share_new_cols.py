"""share new cols

Revision ID: 2025.06.19.5
Revises: 2025.06.19.4
Create Date: 2025-06-19 22:49:04.802317

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.19.5'
down_revision: Union[str, None] = '2025.06.19.4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_shares', sa.Column('date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_shares', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_shares', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_shares', 'updated_at')
    op.drop_column('client_shares', 'created_at')
    op.drop_column('client_shares', 'date')
    # ### end Alembic commands ###
