"""drop login and pass

Revision ID: 2025.06.30.0
Revises: 2025.06.27.0
Create Date: 2025-06-30 15:46:39.807800

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.30.0'
down_revision: Union[str, None] = '2025.06.27.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('clients', 'login')
    op.drop_column('clients', 'password')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clients', sa.Column('password', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('clients', sa.Column('login', sa.<PERSON>RCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
