"""rename detection fields for consistency

Revision ID: 2025.07.21.0
Revises: 2025.07.20.1
Create Date: 2025-07-21 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.21.0'
down_revision: Union[str, None] = '2025.07.20.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Rename client_init_detection_xxx to client_data_detection_xxx
    op.alter_column('client_uploads', 'client_init_detection_id', new_column_name='client_data_detection_id')
    op.alter_column('client_uploads', 'client_init_detection_item_id', new_column_name='client_data_detection_item_id')
    
    # Rename metadata_detection_xxx to metadata_extraction_detection_xxx
    op.alter_column('client_uploads', 'metadata_detection_id', new_column_name='metadata_extraction_detection_id')
    op.alter_column('client_uploads', 'metadata_detection_item_id', new_column_name='metadata_extraction_detection_item_id')


def downgrade() -> None:
    # Revert client_data_detection_xxx to client_init_detection_xxx
    op.alter_column('client_uploads', 'client_data_detection_id', new_column_name='client_init_detection_id')
    op.alter_column('client_uploads', 'client_data_detection_item_id', new_column_name='client_init_detection_item_id')
    
    # Revert metadata_extraction_detection_xxx to metadata_detection_xxx
    op.alter_column('client_uploads', 'metadata_extraction_detection_id', new_column_name='metadata_detection_id')
    op.alter_column('client_uploads', 'metadata_extraction_detection_item_id', new_column_name='metadata_detection_item_id')
