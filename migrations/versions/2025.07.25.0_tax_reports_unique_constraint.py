"""tax reports unique constraint

Revision ID: 2025.07.25.0
Revises: 2025.07.24.0
Create Date: 2025-07-25 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.25.0'
down_revision: Union[str, None] = '2025.07.24.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add unique constraint to prevent duplicate tax reports for same client, fiscal year, and type
    op.create_unique_constraint(
        'uq_tax_reports_client_year_type',
        'tax_reports',
        ['client_id', 'fiscal_year', 'type']
    )


def downgrade() -> None:
    # Remove the unique constraint
    op.drop_constraint('uq_tax_reports_client_year_type', 'tax_reports', type_='unique')
