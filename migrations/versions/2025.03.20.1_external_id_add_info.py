"""external id add info

Revision ID: 2025.03.20.1
Revises: 2025.03.20.0
Create Date: 2025-03-20 15:28:53.851995

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.03.20.1'
down_revision: Union[str, None] = '2025.03.20.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('external_ids', sa.Column('additional_info', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('external_ids', 'additional_info')
    # ### end Alembic commands ###
