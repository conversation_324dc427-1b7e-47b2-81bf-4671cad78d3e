"""add business model

Revision ID: 2025.06.17.0
Revises: 2025.06.16.7
Create Date: 2025-06-17 13:56:59.206450

"""
import json
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.17.0'
down_revision: Union[str, None] = '2025.06.16.7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clients', sa.Column('business_model', sa.String(length=63), nullable=True))
    to_add_in_catalog = [
        'Mobile App',
        'SaaS',
        'E-Commerce',
        'S/W outsourcing',
        'Custom S/W development',
        'Hardware',
        'Other',
    ]
    conn = op.get_bind()
    conn.execute(
        sa.text("INSERT INTO catalogs (name, options) VALUES ('business_model', :options)"),
        parameters={'options': json.dumps(to_add_in_catalog)}
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('clients', 'business_model')
    # ### end Alembic commands ###
