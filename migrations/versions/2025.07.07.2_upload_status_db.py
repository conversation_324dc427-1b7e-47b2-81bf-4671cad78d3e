"""upload status db

Revision ID: 2025.07.07.2
Revises: 2025.07.07.1
Create Date: 2025-07-07 18:01:40.788649

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.07.2'
down_revision: Union[str, None] = '2025.07.07.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_uploads', sa.Column('status', sa.String(length=20), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_uploads', 'status')
    # ### end Alembic commands ###
