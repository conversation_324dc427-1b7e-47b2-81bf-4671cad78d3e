"""add change records

Revision ID: 2025.05.14.0
Revises: 2025.04.29.2
Create Date: 2025-05-14 17:23:57.820315

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.05.14.0'
down_revision: Union[str, None] = '2025.04.29.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('change_records',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('manager_id', sa.String(length=36), nullable=True),
    sa.Column('old_state', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('new_state', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('type', sa.String(length=20), nullable=True),
    sa.Column('object_id', sa.String(length=36), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.ForeignKeyConstraint(['manager_id'], ['managers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_change_records_client_id'), 'change_records', ['client_id'], unique=False)
    op.create_index(op.f('ix_change_records_created_at'), 'change_records', ['created_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_change_records_created_at'), table_name='change_records')
    op.drop_index(op.f('ix_change_records_client_id'), table_name='change_records')
    op.drop_table('change_records')
    # ### end Alembic commands ###
