"""capitalization table

Revision ID: 2025.06.19.2
Revises: 2025.06.19.1
Create Date: 2025-06-19 14:14:14.321300

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.19.2'
down_revision: Union[str, None] = '2025.06.19.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('client_capitalizations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('person_id', sa.String(length=36), nullable=True),
    sa.Column('share_id', sa.Integer(), nullable=True),
    sa.Column('share_amount', sa.Integer(), nullable=True),
    sa.Column('date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('vesting', sa.String(), nullable=True),
    sa.Column('issued_percentage', sa.Float(), nullable=True),
    sa.Column('authorized_percentage', sa.Float(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.ForeignKeyConstraint(['person_id'], ['client_persons.id'], ),
    sa.ForeignKeyConstraint(['share_id'], ['client_shares.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('client_capitalizations')
    # ### end Alembic commands ###
