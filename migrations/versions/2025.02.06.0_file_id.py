"""file id

Revision ID: 2025.02.06.0
Revises: 2025.02.03.0
Create Date: 2025-02-06 17:08:24.046893

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.06.0'
down_revision: Union[str, None] = '2025.02.03.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('files', 'uid',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.String(length=36),
               nullable=False
                    )
    op.drop_column('files', 'id')
    op.execute('ALTER TABLE files ADD PRIMARY KEY (uid);')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('files_pkey', 'files', type_='primary')
    op.add_column('files', sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False))
    op.alter_column('files', 'uid',
               existing_type=sa.String(length=36),
               type_=sa.VARCHAR(length=255),
               nullable=True)
    # ### end Alembic commands ###
