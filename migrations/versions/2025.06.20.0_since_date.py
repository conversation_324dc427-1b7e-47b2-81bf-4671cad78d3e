"""since date

Revision ID: 2025.06.20.0
Revises: 2025.06.19.5
Create Date: 2025-06-20 11:24:24.459867

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.20.0'
down_revision: Union[str, None] = '2025.06.19.5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_addresses', sa.Column('since_date', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_addresses', 'since_date')
    # ### end Alembic commands ###
