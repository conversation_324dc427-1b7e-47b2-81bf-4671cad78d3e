"""reg_agent_ids

Revision ID: 2025.02.18.0
Revises: 2025.02.13.2
Create Date: 2025-02-18 16:37:01.345652

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '2025.02.18.0'
down_revision: Union[str, None] = '2025.02.13.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('client_registrations_reg_agent_uid_fkey', 'client_registrations', type_='foreignkey')
    op.drop_constraint('reg_agents_uid_key', 'reg_agents', type_='unique')
    op.drop_column('reg_agents', 'id')

    op.alter_column('client_registrations', 'reg_agent_uid', new_column_name='reg_agent_id')
    op.alter_column('reg_agents', 'uid', new_column_name='id')
    op.create_primary_key(None, 'reg_agents', ['id'])
    op.create_foreign_key(None, 'client_registrations', 'reg_agents', ['reg_agent_id'], ['id'])
    op.alter_column(
        'reg_agents', 'id',
        existing_type=sa.VARCHAR(length=255),
        type_=sa.String(length=36),
    )
    op.alter_column(
        'client_registrations', 'reg_agent_id',
        existing_type=sa.VARCHAR(length=255),
        type_=sa.String(length=36),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('services', 'id',
                    existing_type=sa.String(length=36),
                    type_=sa.VARCHAR(length=255),
                    existing_nullable=False)
    # ### end Alembic commands ###
