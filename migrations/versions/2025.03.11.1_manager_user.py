"""manager user

Revision ID: 2025.03.11.1
Revises: 2025.03.11.0
Create Date: 2025-03-11 12:15:42.637984

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.11.1'
down_revision: Union[str, None] = '2025.03.11.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('managers', sa.Column('person_id', sa.String(length=36), nullable=True))
    op.add_column('managers', sa.Column('permissions', sa.BigInteger(), nullable=True))
    op.add_column('managers', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('managers', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    op.create_foreign_key(None, 'managers', 'client_persons', ['person_id'], ['id'])
    op.execute("UPDATE managers SET created_at = now()")
    op.execute("UPDATE managers SET updated_at = now()")
    op.execute("UPDATE managers SET permissions = 1")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'managers', type_='foreignkey')
    op.drop_column('managers', 'updated_at')
    op.drop_column('managers', 'created_at')
    op.drop_column('managers', 'permissions')
    op.drop_column('managers', 'person_id')
    # ### end Alembic commands ###
