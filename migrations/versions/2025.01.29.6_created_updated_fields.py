"""created updated fields

Revision ID: 2025.01.29.6
Revises: 2025.01.29.5
Create Date: 2025-01-29 14:21:46.886422

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.01.29.6'
down_revision: Union[str, None] = '2025.01.29.5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('addresses', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('addresses', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_addresses', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_addresses', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_persons', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_persons', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('clients', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('clients', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('files', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('files', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('files', 'updated_at')
    op.drop_column('files', 'created_at')
    op.drop_column('clients', 'updated_at')
    op.drop_column('clients', 'created_at')
    op.drop_column('client_persons', 'updated_at')
    op.drop_column('client_persons', 'created_at')
    op.drop_column('client_addresses', 'updated_at')
    op.drop_column('client_addresses', 'created_at')
    op.drop_column('addresses', 'updated_at')
    op.drop_column('addresses', 'created_at')
    # ### end Alembic commands ###
