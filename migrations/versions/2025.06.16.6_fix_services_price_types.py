"""fix services price_types

Revision ID: 2025.06.16.6
Revises: 2025.06.16.5
Create Date: 2025-06-16 22:15:04.250828

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.16.6'
down_revision: Union[str, None] = '2025.06.16.5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_service_components', 'custom_price')
    op.execute("UPDATE services SET price_type = 'One-time' WHERE price_type IS NULL;")
    op.execute("UPDATE services SET price_type = 'Monthly' WHERE price_type = 'monthly';")
    op.execute("UPDATE services SET price = 0 WHERE price IS NULL;")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_service_components', sa.Column('custom_price', sa.VARCHAR(length=12), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
