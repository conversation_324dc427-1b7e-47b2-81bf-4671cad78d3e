"""card create update

Revision ID: 2025.01.30.2
Revises: 2025.01.30.1
Create Date: 2025-01-30 17:22:16.948136

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.01.30.2'
down_revision: Union[str, None] = '2025.01.30.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('debit_cards', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('debit_cards', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('debit_cards', 'updated_at')
    op.drop_column('debit_cards', 'created_at')
    # ### end Alembic commands ###
