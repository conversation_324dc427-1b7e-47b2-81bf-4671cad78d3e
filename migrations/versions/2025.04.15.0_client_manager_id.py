"""client manager id

Revision ID: 2025.04.15.0
Revises: 2025.04.14.8
Create Date: 2025-04-15 12:06:59.778131

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.15.0'
down_revision: Union[str, None] = '2025.04.14.8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clients', sa.Column('manager_id', sa.String(length=36), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('clients', 'manager_id')
    # ### end Alembic commands ###
