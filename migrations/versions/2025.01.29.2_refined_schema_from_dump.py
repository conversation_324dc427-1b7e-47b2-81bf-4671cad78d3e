"""refined schema from dump

Revision ID: 2025.01.29.2
Revises: 2025.01.29.1
Create Date: 2025-01-29 13:44:58.570405

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.01.29.2'
down_revision: Union[str, None] = '2025.01.29.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('authorized_signers',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('signer_name', sa.String(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('managers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('uid', sa.String(), nullable=True),
    sa.Column('userid', sa.String(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_addresses',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('address_type', sa.String(), nullable=True),
    sa.Column('address_id', sa.String(length=36), nullable=True),
    sa.Column('renewal_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('paid_by', sa.String(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_authorized_signers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('signer_id', sa.String(length=36), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.ForeignKeyConstraint(['signer_id'], ['authorized_signers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_contacts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('client_person_uid', sa.String(), nullable=True),
    sa.Column('position', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('pcm', sa.String(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_registrations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('reg_agent_uid', sa.String(), nullable=True),
    sa.Column('reg_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('reg_state', sa.String(), nullable=True),
    sa.Column('reg_pay_by', sa.String(), nullable=True),
    sa.Column('last_soi_filed', sa.DateTime(timezone=True), nullable=True),
    sa.Column('state_entity', sa.String(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.ForeignKeyConstraint(['reg_agent_uid'], ['reg_agents.uid'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_services',
    sa.Column('client_id', sa.String(), nullable=True),
    sa.Column('service_uid', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.ForeignKeyConstraint(['service_uid'], ['services.uid'], )
    )
    op.create_table('client_share_classes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('stock_authorized', sa.Integer(), nullable=True),
    sa.Column('stock_issued', sa.Integer(), nullable=True),
    sa.Column('shares_authorized_preferred', sa.String(), nullable=True),
    sa.Column('shares_issued_preferred', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_shareholders',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('client_person_uid', sa.String(), nullable=True),
    sa.Column('position', sa.String(), nullable=True),
    sa.Column('ownership', sa.String(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_tasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('task', sa.String(), nullable=True),
    sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('manager', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('debit_cards',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('debit_card', sa.String(), nullable=True),
    sa.Column('last_4_digits', sa.String(), nullable=True),
    sa.Column('expired_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('cid', sa.String(), nullable=True),
    sa.Column('linked_to', sa.String(), nullable=True),
    sa.Column('card_holder', sa.String(), nullable=True),
    sa.Column('exp', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('payment_systems',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('payment_system', sa.String(), nullable=True),
    sa.Column('date_opened', sa.DateTime(timezone=True), nullable=True),
    sa.Column('opened_by', sa.String(), nullable=True),
    sa.Column('email_connected', sa.String(), nullable=True),
    sa.Column('responsible_person', sa.String(), nullable=True),
    sa.Column('login_pass', sa.String(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('tax_reporting',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('year', sa.String(), nullable=True),
    sa.Column('reporting_1099', sa.String(), nullable=True),
    sa.Column('tax_return_by', sa.String(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.Column('files', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('addresses', sa.Column('full_address', sa.String(), nullable=True))
    op.alter_column('addresses', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.alter_column('addresses', 'city',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.alter_column('addresses', 'state',
               existing_type=sa.CHAR(length=2),
               type_=sa.String(),
               nullable=True)
    op.alter_column('addresses', 'zip',
               existing_type=sa.VARCHAR(length=10),
               nullable=True)
    op.alter_column('addresses', 'country',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.drop_constraint('addresses_uid_key', 'addresses', type_='unique')
    op.drop_index('ix_addresses_client_id', table_name='addresses')
    op.drop_index('ix_addresses_client_person_id', table_name='addresses')
    op.drop_column('addresses', 'uid')
    op.drop_column('addresses', 'client_person_id')
    op.drop_column('addresses', 'client_id')
    op.drop_column('addresses', 'title')
    op.add_column('bank_accounts', sa.Column('bank_contact', sa.String(), nullable=True))
    op.add_column('bank_accounts', sa.Column('date_opened', sa.DateTime(timezone=True), nullable=True))
    op.add_column('bank_accounts', sa.Column('last_renewal', sa.DateTime(timezone=True), nullable=True))
    op.add_column('bank_accounts', sa.Column('notes', sa.Text(), nullable=True))
    op.alter_column('bank_accounts', 'client_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=36),
               existing_nullable=True)
    op.alter_column('bank_accounts', 'client_person_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('bank_accounts', 'bank_name',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('bank_accounts', 'aba_number',
               existing_type=sa.VARCHAR(length=9),
               type_=sa.String(length=16),
               nullable=True)
    op.drop_index('ix_bank_accounts_client_id', table_name='bank_accounts')
    op.create_foreign_key(None, 'bank_accounts', 'clients', ['client_id'], ['id'])
    op.create_foreign_key(None, 'bank_accounts', 'client_persons', ['client_person_id'], ['uid'])
    op.drop_column('bank_accounts', 'created_at')
    op.drop_column('bank_accounts', 'account_type')
    op.drop_column('bank_accounts', 'account_number')
    op.alter_column('catalogs', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('catalogs', 'options',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=True)
    op.add_column('client_persons', sa.Column('address', sa.Text(), nullable=True))
    op.alter_column('client_persons', 'uid',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('client_persons', 'email',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.drop_constraint('client_persons_email_key', 'client_persons', type_='unique')
    op.drop_column('client_persons', 'created_at')
    op.drop_column('client_persons', 'updated_at')
    op.add_column('clients', sa.Column('account', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('account_add', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('accounting_method', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('agr_signed', sa.DateTime(timezone=True), nullable=True))
    op.add_column('clients', sa.Column('agreement_sum', sa.Float(), nullable=True))
    op.add_column('clients', sa.Column('billing_method', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('bookkeeping', sa.Boolean(), nullable=True))
    op.add_column('clients', sa.Column('client', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('company_phone', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('control_by', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('cpa', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('disable_secreg', sa.Boolean(), nullable=True))
    op.add_column('clients', sa.Column('dissolution_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('clients', sa.Column('ein', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('fedtaxforms', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('fye', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('fye_for_subsidiary', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('incorp_by', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('legal_ent_type', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('login', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('manager', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('monthly_bill', sa.Float(), nullable=True))
    op.add_column('clients', sa.Column('none_banks', sa.Boolean(), nullable=True))
    op.add_column('clients', sa.Column('notes_accounting', sa.Text(), nullable=True))
    op.add_column('clients', sa.Column('notes_address', sa.Text(), nullable=True))
    op.add_column('clients', sa.Column('notes_agreement', sa.Text(), nullable=True))
    op.add_column('clients', sa.Column('notes_contacts', sa.Text(), nullable=True))
    op.add_column('clients', sa.Column('notes_main', sa.Text(), nullable=True))
    op.add_column('clients', sa.Column('notes_shareholders', sa.Text(), nullable=True))
    op.add_column('clients', sa.Column('paid_by', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('paid_by_mail', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('password', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('payroll', sa.Boolean(), nullable=True))
    op.add_column('clients', sa.Column('renewal_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('clients', sa.Column('renewal_date_mail', sa.DateTime(timezone=True), nullable=True))
    op.add_column('clients', sa.Column('since', sa.DateTime(timezone=True), nullable=True))
    op.add_column('clients', sa.Column('source_id', sa.String(length=36), nullable=True))
    op.add_column('clients', sa.Column('statetaxforms', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('subjurisd', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('subsidiary_legal_entity_type', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('subsidiary_to_consolidate', sa.String(), nullable=True))
    op.add_column('clients', sa.Column('withdrawal_date', sa.DateTime(timezone=True), nullable=True))
    op.alter_column('clients', 'active_since',
               existing_type=sa.DATE(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('clients', 'date',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True)
    op.alter_column('clients', 'status',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.drop_column('clients', 'created_at')
    op.drop_column('clients', 'source')
    op.drop_column('clients', 'client_name')
    op.drop_column('clients', 'updated_at')
    op.alter_column('cp575', 'client_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=36),
               existing_nullable=True)
    op.add_column('files', sa.Column('name', sa.String(), nullable=True))
    op.add_column('files', sa.Column('client_person_id', sa.String(length=36), nullable=True))
    op.add_column('files', sa.Column('manager', sa.String(), nullable=True))
    op.add_column('files', sa.Column('uploaded', sa.Boolean(), nullable=True))
    op.alter_column('files', 'date',
               existing_type=sa.DATE(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('files', 'uid',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('files', 'file_type',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('files', 'client_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=36),
               nullable=True)
    op.alter_column('files', 'url',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.drop_constraint('files_uid_key', 'files', type_='unique')
    op.drop_index('ix_files_client_id', table_name='files')
    op.drop_column('files', 'created_at')
    op.drop_column('files', 'updated_at')
    op.drop_column('files', 'deleted_at')
    op.drop_column('files', 'title')
    op.drop_column('files', 'status')
    op.alter_column('form_ss4', 'client_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.alter_column('legal_corporate_documents', 'client_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.alter_column('personal_documents', 'client_person_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=36),
               existing_nullable=True)
    op.alter_column('reg_agents', 'uid',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('reg_agents', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('reg_agents', 'address',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('services', 'uid',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('services', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('services', 'price',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
    op.alter_column('services', 'price_type',
               existing_type=sa.VARCHAR(length=30),
               nullable=True)
    op.alter_column('sources', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('sources', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('services', 'price_type',
               existing_type=sa.VARCHAR(length=30),
               nullable=False)
    op.alter_column('services', 'price',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
    op.alter_column('services', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('services', 'uid',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('reg_agents', 'address',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('reg_agents', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('reg_agents', 'uid',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('personal_documents', 'client_person_id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('legal_corporate_documents', 'client_id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('form_ss4', 'client_id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.add_column('files', sa.Column('status', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('files', sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.add_column('files', sa.Column('deleted_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.add_column('files', sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.add_column('files', sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.create_index('ix_files_client_id', 'files', ['client_id'], unique=False)
    op.create_unique_constraint('files_uid_key', 'files', ['uid'])
    op.alter_column('files', 'url',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('files', 'client_id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               nullable=False)
    op.alter_column('files', 'file_type',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.alter_column('files', 'uid',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('files', 'date',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATE(),
               nullable=False)
    op.drop_column('files', 'uploaded')
    op.drop_column('files', 'manager')
    op.drop_column('files', 'client_person_id')
    op.drop_column('files', 'name')
    op.alter_column('cp575', 'client_id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.add_column('clients', sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.add_column('clients', sa.Column('client_name', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.add_column('clients', sa.Column('source', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.add_column('clients', sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.alter_column('clients', 'status',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('clients', 'date',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False)
    op.alter_column('clients', 'active_since',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATE(),
               existing_nullable=True)
    op.drop_column('clients', 'withdrawal_date')
    op.drop_column('clients', 'subsidiary_to_consolidate')
    op.drop_column('clients', 'subsidiary_legal_entity_type')
    op.drop_column('clients', 'subjurisd')
    op.drop_column('clients', 'statetaxforms')
    op.drop_column('clients', 'source_id')
    op.drop_column('clients', 'since')
    op.drop_column('clients', 'renewal_date_mail')
    op.drop_column('clients', 'renewal_date')
    op.drop_column('clients', 'payroll')
    op.drop_column('clients', 'password')
    op.drop_column('clients', 'paid_by_mail')
    op.drop_column('clients', 'paid_by')
    op.drop_column('clients', 'notes_shareholders')
    op.drop_column('clients', 'notes_main')
    op.drop_column('clients', 'notes_contacts')
    op.drop_column('clients', 'notes_agreement')
    op.drop_column('clients', 'notes_address')
    op.drop_column('clients', 'notes_accounting')
    op.drop_column('clients', 'none_banks')
    op.drop_column('clients', 'monthly_bill')
    op.drop_column('clients', 'manager')
    op.drop_column('clients', 'login')
    op.drop_column('clients', 'legal_ent_type')
    op.drop_column('clients', 'incorp_by')
    op.drop_column('clients', 'fye_for_subsidiary')
    op.drop_column('clients', 'fye')
    op.drop_column('clients', 'fedtaxforms')
    op.drop_column('clients', 'ein')
    op.drop_column('clients', 'dissolution_date')
    op.drop_column('clients', 'disable_secreg')
    op.drop_column('clients', 'cpa')
    op.drop_column('clients', 'control_by')
    op.drop_column('clients', 'company_phone')
    op.drop_column('clients', 'client')
    op.drop_column('clients', 'bookkeeping')
    op.drop_column('clients', 'billing_method')
    op.drop_column('clients', 'agreement_sum')
    op.drop_column('clients', 'agr_signed')
    op.drop_column('clients', 'accounting_method')
    op.drop_column('clients', 'account_add')
    op.drop_column('clients', 'account')
    op.add_column('client_persons', sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.add_column('client_persons', sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.create_unique_constraint('client_persons_email_key', 'client_persons', ['email'])
    op.alter_column('client_persons', 'email',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('client_persons', 'uid',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.drop_column('client_persons', 'address')
    op.alter_column('catalogs', 'options',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('catalogs', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.add_column('bank_accounts', sa.Column('account_number', sa.VARCHAR(length=20), autoincrement=False, nullable=False))
    op.add_column('bank_accounts', sa.Column('account_type', sa.VARCHAR(length=30), autoincrement=False, nullable=False))
    op.add_column('bank_accounts', sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'bank_accounts', type_='foreignkey')
    op.drop_constraint(None, 'bank_accounts', type_='foreignkey')
    op.create_index('ix_bank_accounts_client_id', 'bank_accounts', ['client_id'], unique=False)
    op.alter_column('bank_accounts', 'aba_number',
               existing_type=sa.String(length=16),
               type_=sa.VARCHAR(length=9),
               nullable=False)
    op.alter_column('bank_accounts', 'bank_name',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('bank_accounts', 'client_person_id',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('bank_accounts', 'client_id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.drop_column('bank_accounts', 'notes')
    op.drop_column('bank_accounts', 'last_renewal')
    op.drop_column('bank_accounts', 'date_opened')
    op.drop_column('bank_accounts', 'bank_contact')
    op.add_column('addresses', sa.Column('title', sa.VARCHAR(length=50), autoincrement=False, nullable=False))
    op.add_column('addresses', sa.Column('client_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('addresses', sa.Column('client_person_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('addresses', sa.Column('uid', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.create_index('ix_addresses_client_person_id', 'addresses', ['client_person_id'], unique=False)
    op.create_index('ix_addresses_client_id', 'addresses', ['client_id'], unique=False)
    op.create_unique_constraint('addresses_uid_key', 'addresses', ['uid'])
    op.alter_column('addresses', 'country',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.alter_column('addresses', 'zip',
               existing_type=sa.VARCHAR(length=10),
               nullable=False)
    op.alter_column('addresses', 'state',
               existing_type=sa.String(),
               type_=sa.CHAR(length=2),
               nullable=False)
    op.alter_column('addresses', 'city',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.alter_column('addresses', 'id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.drop_column('addresses', 'full_address')
    op.drop_table('tax_reporting')
    op.drop_table('payment_systems')
    op.drop_table('debit_cards')
    op.drop_table('client_tasks')
    op.drop_table('client_shareholders')
    op.drop_table('client_share_classes')
    op.drop_table('client_services')
    op.drop_table('client_registrations')
    op.drop_table('client_contacts')
    op.drop_table('client_authorized_signers')
    op.drop_table('client_addresses')
    op.drop_table('managers')
    op.drop_table('authorized_signers')
    # ### end Alembic commands ###
