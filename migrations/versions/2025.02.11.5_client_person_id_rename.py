"""client person id

Revision ID: 2025.02.11.5
Revises: 2025.02.11.4
Create Date: 2025-02-11 18:34:17.340344

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.11.5'
down_revision: Union[str, None] = '2025.02.11.4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_contacts', 'client_person_uid', new_column_name='client_person_id')
    op.alter_column('client_shareholders', 'client_person_uid', new_column_name='client_person_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_contacts', 'client_person_id', new_column_name='client_person_uid')
    op.alter_column('client_shareholders', 'client_person_id', new_column_name='client_person_uid')
    # ### end Alembic commands ###
