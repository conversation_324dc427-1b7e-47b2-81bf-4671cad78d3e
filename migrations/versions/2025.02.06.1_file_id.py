"""file id

Revision ID: 2025.02.06.1
Revises: 2025.02.06.0
Create Date: 2025-02-06 17:12:05.923474

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.06.1'
down_revision: Union[str, None] = '2025.02.06.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'common_extractors',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('external_id', sa.Integer(), nullable=True),
        sa.Column('title', sa.String(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_table(
        'external_ids',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('internal_id', sa.String(length=36), nullable=True),
        sa.Column('internal_type', sa.String(length=100), nullable=True),
        sa.Column('external_app_type', sa.String(length=100), nullable=True),
        sa.Column('external_app_id', sa.String(length=36), nullable=True),
        sa.Column('external_item_id', sa.String(length=36), nullable=True),
        sa.Column('external_item_type', sa.String(length=100), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_table(
        'client_timelines',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('external_id', sa.Integer(), nullable=True),
        sa.Column('client_id', sa.String(length=36), nullable=True),
        sa.Column('type', sa.String(length=100), nullable=True),
        sa.Column('note', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.add_column('clients', sa.Column('extractor_id', sa.Integer(), nullable=True))
    op.alter_column('files', 'uid', new_column_name='id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('files', 'id', new_column_name='uid')
    op.drop_column('clients', 'extractor_id')
    op.drop_table('client_timelines')
    op.drop_table('external_ids')
    op.drop_table('common_extractors')
    # ### end Alembic commands ###
