"""add main contact in catalog

Revision ID: 2025.06.17.2
Revises: 2025.06.17.1
Create Date: 2025-06-17 14:43:53.394696

"""
import json
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.17.2'
down_revision: Union[str, None] = '2025.06.17.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    catalog_name = "position"
    # Fetch existing options from the catalog
    existing_options = conn.execute(
        sa.text("SELECT options FROM catalogs WHERE name = :catalog_name"),
        parameters={'catalog_name': catalog_name}
    ).fetchall()
    if existing_options:
        existing_options = existing_options[0][0]
    else:
        existing_options = []

    existing_options.insert(0, "Main contact")
    conn.execute(
        sa.text("UPDATE catalogs SET options = :options WHERE name = :catalog_name"),
        parameters={'options': json.dumps(existing_options), 'catalog_name': catalog_name}
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
