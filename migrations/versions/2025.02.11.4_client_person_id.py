"""client person id

Revision ID: 2025.02.11.4
Revises: 2025.02.11.3
Create Date: 2025-02-11 18:34:17.340344

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.11.4'
down_revision: Union[str, None] = '2025.02.11.3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('bank_accounts_client_person_id_fkey', 'bank_accounts', type_='foreignkey')
    op.alter_column('client_persons', 'uid', new_column_name='id')
    op.create_foreign_key(None, 'bank_accounts', 'client_persons', ['client_person_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('bank_accounts_client_person_id_fkey', 'bank_accounts', type_='foreignkey')
    op.alter_column('client_persons', 'id', new_column_name='uid')
    op.create_foreign_key('bank_accounts_client_person_id_fkey', 'bank_accounts', 'client_persons', ['client_person_id'], ['uid'])
    # ### end Alembic commands ###
