"""delete manager

Revision ID: 2025.04.15.1
Revises: 2025.04.15.0
Create Date: 2025-04-15 12:19:14.296236

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.15.1'
down_revision: Union[str, None] = '2025.04.15.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('clients', 'manager')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clients', sa.Column('manager', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
