"""timeline and client extractors

Revision ID: 2025.03.11.2
Revises: 2025.03.11.1
Create Date: 2025-03-11 17:24:11.330855

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.11.2'
down_revision: Union[str, None] = '2025.03.11.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('client_extractors',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('external_id', sa.Integer(), nullable=True),
    sa.Column('type', sa.String(length=100), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('client_id', 'type', name='client_extractor_unique'),
    sa.UniqueConstraint('type')
    )
    op.drop_column('clients', 'extractor_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clients', sa.Column('extractor_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_table('client_extractors')
    # ### end Alembic commands ###
