"""file created at

Revision ID: 2025.03.19.0
Revises: 2025.03.18.0
Create Date: 2025-03-19 21:34:30.604856

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.19.0'
down_revision: Union[str, None] = '2025.03.18.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE files SET created_at = now() WHERE created_at IS NULL")
    op.execute("UPDATE files SET updated_at = now() WHERE updated_at IS NULL")
    op.add_column('files', sa.Column('description', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
