"""delete client task manager

Revision ID: 2025.04.18.3
Revises: 2025.04.18.2
Create Date: 2025-04-18 16:42:34.539297

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.18.3'
down_revision: Union[str, None] = '2025.04.18.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_tasks', 'manager')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_tasks', sa.Column('manager', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
