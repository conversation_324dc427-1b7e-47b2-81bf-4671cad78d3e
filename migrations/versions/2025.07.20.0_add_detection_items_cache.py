"""add detection items cache

Revision ID: 2025.07.20.0
Revises: 2025.07.18.2
Create Date: 2025-07-20 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON as PostgresJSON


# revision identifiers, used by Alembic.
revision: str = '2025.07.20.0'
down_revision: Union[str, None] = '2025.07.18.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add single caching field for all detection item data
    # Structure: {"client_init": {...}, "metadata": {...}, "smart_merge": {...}}
    op.add_column('client_uploads', sa.Column('detection_items_cache', PostgresJSON, nullable=True))


def downgrade() -> None:
    # Remove caching field
    op.drop_column('client_uploads', 'detection_items_cache')
