"""file meta from upload

Revision ID: 2025.07.08.0
Revises: 2025.07.07.3
Create Date: 2025-07-08 17:44:17.578694

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.07.08.0'
down_revision: Union[str, None] = '2025.07.07.3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('files', sa.Column('meta', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('files', sa.Column('from_upload', sa.<PERSON>(), nullable=True))
    op.execute("UPDATE files SET from_upload = true WHERE client_id IS NULL")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('files', 'from_upload')
    op.drop_column('files', 'meta')
    # ### end Alembic commands ###
