"""rename fye

Revision ID: 2025.04.14.2
Revises: 2025.04.14.1
Create Date: 2025-04-14 15:39:19.503356

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.14.2'
down_revision: Union[str, None] = '2025.04.14.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Rename fye to financial_year_end
    op.alter_column('clients', 'fye', new_column_name='financial_year_end')
    # Rename fye_for_subsidiary to financial_year_end_for_subsidiary
    op.alter_column('clients', 'fye_for_subsidiary', new_column_name='financial_year_end_for_subsidiary')


def downgrade() -> None:
    # Revert financial_year_end back to fye
    op.alter_column('clients', 'financial_year_end', new_column_name='fye')
    # Revert financial_year_end_for_subsidiary back to fye_for_subsidiary
    op.alter_column('clients', 'financial_year_end_for_subsidiary', new_column_name='fye_for_subsidiary')

