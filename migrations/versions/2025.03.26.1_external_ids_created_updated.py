"""external ids created updated

Revision ID: 2025.03.26.1
Revises: 2025.03.26.0
Create Date: 2025-03-26 21:49:31.384794

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.26.1'
down_revision: Union[str, None] = '2025.03.26.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('external_ids', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('external_ids', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('external_ids', 'updated_at')
    op.drop_column('external_ids', 'created_at')
    # ### end Alembic commands ###
