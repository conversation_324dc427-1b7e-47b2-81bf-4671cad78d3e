"""svc components and subscriptions

Revision ID: 2025.06.26.0
Revises: 2025.06.23.0
Create Date: 2025-06-26 18:18:53.179741

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.06.26.0'
down_revision: Union[str, None] = '2025.06.23.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('subscriptions',
    sa.Column('id', sa.Integer(), sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('price_type', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_subscriptions',
    sa.Column('id', sa.Integer(), sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=False),
    sa.Column('subscription_id', sa.Integer(), nullable=False),
    sa.Column('start_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('stop_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('total_price', sa.Float(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('discount_amount', sa.Float(), nullable=True),
    sa.Column('discount_percent', sa.Float(), nullable=True),
    sa.Column('cancellation_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('cancellation_reason', sa.Text(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.ForeignKeyConstraint(['subscription_id'], ['subscriptions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_client_subscriptions_client_id'), 'client_subscriptions', ['client_id'], unique=False)
    op.create_table('subscription_services',
    sa.Column('id', sa.Integer(), sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1), nullable=False),
    sa.Column('subscription_id', sa.Integer(), nullable=False),
    sa.Column('service_id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['service_id'], ['services.id'], ),
    sa.ForeignKeyConstraint(['subscription_id'], ['subscriptions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_subscription_services',
    sa.Column('id', sa.Integer(), sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1), nullable=False),
    sa.Column('client_subscription_id', sa.Integer(), nullable=False),
    sa.Column('service_id', sa.String(length=36), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('custom_price', sa.Float(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['client_subscription_id'], ['client_subscriptions.id'], ),
    sa.ForeignKeyConstraint(['service_id'], ['services.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('service_components')
    op.drop_table('client_service_components')
    op.drop_column('services', 'composite_flag')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('services', sa.Column('composite_flag', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.create_table('client_service_components',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('sequence', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('service_component_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('client_service_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('price_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['client_service_id'], ['client_services.id'], name='client_service_components_client_service_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='client_service_components_pkey')
    )
    op.create_table('service_components',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('service_id', sa.VARCHAR(length=36), autoincrement=False, nullable=False),
    sa.Column('sequence', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('price_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['service_id'], ['services.id'], name='service_components_service_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='service_components_pkey')
    )
    op.drop_table('client_subscription_services')
    op.drop_table('subscription_services')
    op.drop_index(op.f('ix_client_subscriptions_client_id'), table_name='client_subscriptions')
    op.drop_table('client_subscriptions')
    op.drop_table('subscriptions')
    # ### end Alembic commands ###
