"""add smart merge det id

Revision ID: 2025.07.05.0
Revises: 2025.07.04.1
Create Date: 2025-07-05 21:35:32.169321

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

from records.config.shared_config import SharedConfig
from records.db import models
from records.services.detections import get_client_smart_merge_detection_config

# revision identifiers, used by Alembic.
revision: str = '2025.07.05.0'
down_revision: Union[str, None] = '2025.07.04.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    ira_client = SharedConfig().ira_client
    # Smart merge detection
    conn = op.get_bind()
    detection_merge_id = conn.execute(
        sa.text("SELECT external_app_id FROM external_ids WHERE internal_type = :internal_type AND external_app_type = 'detection'"),
        parameters={'internal_type': models.DetectionType.SMART_MERGE}
    ).fetchone()
    detection_merge_id = detection_merge_id[0] if detection_merge_id else None
    if not detection_merge_id:
        detection_ira = ira_client.detection_create(
            title=models.DetectionName.SMART_MERGE,
            type='smart_merge',
            description='Detection for client data smart merge',
            config=get_client_smart_merge_detection_config()
        )
        conn.execute(
            sa.text("INSERT INTO external_ids (internal_type, external_app_type, external_app_id) VALUES (:internal_type, 'detection', :external_app_id)"),
            parameters={'internal_type': models.DetectionType.SMART_MERGE, 'external_app_id': str(detection_ira['id'])}
        )
        detection_merge_id = detection_ira['id']

    smart_merge_detection_id = detection_merge_id
    # Update all client uploads with smart merge detection id
    conn.execute(
        sa.text("UPDATE client_uploads SET smart_merge_detection_id = :smart_merge_detection_id"),
        parameters={'smart_merge_detection_id': smart_merge_detection_id}
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
