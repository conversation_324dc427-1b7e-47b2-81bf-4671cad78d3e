"""service columns

Revision ID: 2025.06.10.0
Revises: 2025.06.09.0
Create Date: 2025-06-10 18:17:08.073751

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.10.0'
down_revision: Union[str, None] = '2025.06.09.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_services', sa.Column('active_since', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_services', sa.Column('active_until', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_services', sa.Column('note', sa.Text(), nullable=True))
    op.add_column('client_services', sa.Column('discount_percent', sa.Integer(), nullable=True))
    op.add_column('client_services', sa.Column('discount_amount', sa.String(12), nullable=True))
    op.add_column('client_services', sa.Column('total', sa.String(12), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_services', 'total')
    op.drop_column('client_services', 'discount_amount')
    op.drop_column('client_services', 'discount_percent')
    op.drop_column('client_services', 'note')
    op.drop_column('client_services', 'active_until')
    op.drop_column('client_services', 'active_since')
    # ### end Alembic commands ###
