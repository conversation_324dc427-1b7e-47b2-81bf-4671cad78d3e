"""initial db

Revision ID: 2025.01.13.0
Revises: 
Create Date: 2025-01-13 14:22:07.566525

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.01.13.0'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sessions',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('ttl', sa.DateTime(timezone=True), nullable=True),
    sa.Column('admin', sa.<PERSON>an(), nullable=True),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('extra', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sessions_user_id'), 'sessions', ['user_id'], unique=False)
    op.create_table('tokens',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('owner_id', sa.BigInteger(), nullable=True),
    sa.Column('object_id', sa.BigInteger(), nullable=True),
    sa.Column('object_type', sa.String(length=30), nullable=True),
    sa.Column('permissions', sa.BigInteger(), nullable=True),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expiry_time', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tokens_object_id'), 'tokens', ['object_id'], unique=False)
    op.create_index(op.f('ix_tokens_object_type'), 'tokens', ['object_type'], unique=False)
    op.create_table('user_confirms',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expiry_time', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_invites',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('from_user_id', sa.BigInteger(), nullable=True),
    sa.Column('to_user_id', sa.BigInteger(), nullable=True),
    sa.Column('to_user_login', sa.String(length=200), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expiry_time', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_service_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=True),
    sa.Column('service', sa.String(length=200), nullable=True),
    sa.Column('service_id', sa.String(length=200), nullable=True),
    sa.Column('service_name', sa.String(length=200), nullable=True),
    sa.Column('service_picture', sa.String(length=255), nullable=True),
    sa.Column('token', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_service_accounts_service'), 'user_service_accounts', ['service'], unique=False)
    op.create_index(op.f('ix_user_service_accounts_user_id'), 'user_service_accounts', ['user_id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('name', sa.String(length=200), nullable=True),
    sa.Column('info', sa.Text(), nullable=True),
    sa.Column('login', sa.String(length=200), nullable=True),
    sa.Column('password', sa.String(length=200), nullable=True),
    sa.Column('confirmed', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_seen_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_activity_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('users')
    op.drop_index(op.f('ix_user_service_accounts_user_id'), table_name='user_service_accounts')
    op.drop_index(op.f('ix_user_service_accounts_service'), table_name='user_service_accounts')
    op.drop_table('user_service_accounts')
    op.drop_table('user_invites')
    op.drop_table('user_confirms')
    op.drop_index(op.f('ix_tokens_object_type'), table_name='tokens')
    op.drop_index(op.f('ix_tokens_object_id'), table_name='tokens')
    op.drop_table('tokens')
    op.drop_index(op.f('ix_sessions_user_id'), table_name='sessions')
    op.drop_table('sessions')
    # ### end Alembic commands ###
