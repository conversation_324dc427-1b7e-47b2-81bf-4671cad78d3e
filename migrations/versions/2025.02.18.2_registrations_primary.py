"""registrations primary

Revision ID: 2025.02.18.2
Revises: 2025.02.18.1
Create Date: 2025-02-18 19:03:03.934540

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.18.2'
down_revision: Union[str, None] = '2025.02.18.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_registrations', sa.Column('is_primary', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_registrations', 'is_primary')
    # ### end Alembic commands ###
