"""address id

Revision ID: 2025.02.12.0
Revises: 2025.02.11.5
Create Date: 2025-02-12 09:08:17.230399

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.12.0'
down_revision: Union[str, None] = '2025.02.11.5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('addresses', 'uid',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_column('addresses', 'id')
    op.alter_column('addresses', 'uid', new_column_name='id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('addresses', 'id', new_column_name='uid')
    op.add_column('addresses', sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False))
    op.alter_column('addresses', 'uid',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###
