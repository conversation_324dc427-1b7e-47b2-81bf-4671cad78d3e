"""migrate auth signers

Revision ID: 2025.04.17.0
Revises: 2025.04.16.0
Create Date: 2025-04-17 14:46:52.683153

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.17.0'
down_revision: Union[str, None] = '2025.04.16.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Merge signers
    op.execute(
        "UPDATE client_authorized_signers set signer_id = '8b7e9460-5930-4497-9f05-31acc03b6b81' "
        "where signer_id in ('ae8b6583-aa1c-446f-ac38-3f1d4c3b6831', 'b4dc3e11-1b7b-4021-9a9b-3b279d68da19', "
        "'c8386bbd-6900-4cd6-92e8-bc235ec8e04a', 'cd5f3c45-1f74-4b41-8ab7-ddbb2025f029', 'b042509f-7872-4fdf-bafe-2023ecde82da', "
        "'dcd38744-ff99-4392-ad71-1dc8d44c0184');"
    )
    op.execute(
        "UPDATE client_authorized_signers SET signer_id = '6abfd3d3-b3c8-4677-99c6-91765778b46d' "
        "where signer_id in ('992eb7a1-aba2-41ee-97d5-c2e3e08b3a24');"
    )
    op.execute(
        "UPDATE bank_accounts SET authorized_signer_id = '8b7e9460-5930-4497-9f05-31acc03b6b81' "
        "where authorized_signer_id in ('ae8b6583-aa1c-446f-ac38-3f1d4c3b6831', 'b4dc3e11-1b7b-4021-9a9b-3b279d68da19',"
        " 'c8386bbd-6900-4cd6-92e8-bc235ec8e04a', 'cd5f3c45-1f74-4b41-8ab7-ddbb2025f029', "
        "'b042509f-7872-4fdf-bafe-2023ecde82da', 'dcd38744-ff99-4392-ad71-1dc8d44c0184')"
    )
    op.execute(
        "UPDATE bank_accounts SET authorized_signer_id = '6abfd3d3-b3c8-4677-99c6-91765778b46d' "
        "where authorized_signer_id in ('992eb7a1-aba2-41ee-97d5-c2e3e08b3a24');"
    )

    op.execute(
        "DELETE FROM authorized_signers "
        "WHERE id in ('ae8b6583-aa1c-446f-ac38-3f1d4c3b6831', 'b4dc3e11-1b7b-4021-9a9b-3b279d68da19',"
        " 'c8386bbd-6900-4cd6-92e8-bc235ec8e04a', 'cd5f3c45-1f74-4b41-8ab7-ddbb2025f029', "
        "'b042509f-7872-4fdf-bafe-2023ecde82da', 'dcd38744-ff99-4392-ad71-1dc8d44c0184', '992eb7a1-aba2-41ee-97d5-c2e3e08b3a24');"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
