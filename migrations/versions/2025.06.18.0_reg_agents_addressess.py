"""reg agents addressess

Revision ID: 2025.06.18.0
Revises: 2025.06.17.2
Create Date: 2025-06-18 11:23:56.079898

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.18.0'
down_revision: Union[str, None] = '2025.06.17.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reg_agents', sa.Column('address_id', sa.String(length=36), nullable=True))
    op.create_foreign_key(None, 'reg_agents', 'addresses', ['address_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'reg_agents', type_='foreignkey')
    op.drop_column('reg_agents', 'address_id')
    # ### end Alembic commands ###
