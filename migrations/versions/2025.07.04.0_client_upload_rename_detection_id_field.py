"""client upload rename detection id field

Revision ID: 2025.07.04.0
Revises: 2025.07.01.0
Create Date: 2025-07-04 18:50:20.124048

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.04.0'
down_revision: Union[str, None] = '2025.07.01.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_uploads', 'detection_id', new_column_name='client_init_detection_id')
    op.alter_column('client_uploads', 'detection_item_id', new_column_name='client_init_detection_item_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
