"""client uploads output type

Revision ID: 2025.06.30.4
Revises: 2025.06.30.3
Create Date: 2025-06-30 17:59:51.545475

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.06.30.4'
down_revision: Union[str, None] = '2025.06.30.3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'client_uploads', 'output',
        existing_type=sa.TEXT(),
        type_=postgresql.JSON(astext_type=sa.Text()),
        existing_nullable=True,
        postgresql_using='output::json'
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_uploads', 'output',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               type_=sa.TEXT(),
               existing_nullable=True)
    # ### end Alembic commands ###
