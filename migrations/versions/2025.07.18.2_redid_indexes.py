"""redid indexes

Revision ID: 2025.07.18.2
Revises: 2025.07.18.1
Create Date: 2025-07-18 18:58:52.020830

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.18.2'
down_revision: Union[str, None] = '2025.07.18.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_client_upload_attached_objects_client_upload_id', table_name='upload_attached_objects')
    op.drop_index('ix_client_upload_attached_objects_object_type_id', table_name='upload_attached_objects')
    op.drop_constraint('uq_client_upload_attached_objects', 'upload_attached_objects', type_='unique')
    op.create_index('ix_upload_attached_objects_object_type_id', 'upload_attached_objects', ['object_type', 'object_id'], unique=False)
    op.create_index(op.f('ix_upload_attached_objects_upload_id'), 'upload_attached_objects', ['upload_id'], unique=False)
    op.create_unique_constraint('uq_upload_attached_objects', 'upload_attached_objects', ['upload_id', 'object_type', 'object_id'])
    op.drop_constraint('client_upload_attached_objects_client_upload_id_fkey', 'upload_attached_objects', type_='foreignkey')
    op.create_foreign_key(None, 'upload_attached_objects', 'client_uploads', ['upload_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'upload_attached_objects', type_='foreignkey')
    op.create_foreign_key('client_upload_attached_objects_client_upload_id_fkey', 'upload_attached_objects', 'client_uploads', ['upload_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('uq_upload_attached_objects', 'upload_attached_objects', type_='unique')
    op.drop_index(op.f('ix_upload_attached_objects_upload_id'), table_name='upload_attached_objects')
    op.drop_index('ix_upload_attached_objects_object_type_id', table_name='upload_attached_objects')
    op.create_unique_constraint('uq_client_upload_attached_objects', 'upload_attached_objects', ['upload_id', 'object_type', 'object_id'])
    op.create_index('ix_client_upload_attached_objects_object_type_id', 'upload_attached_objects', ['object_type', 'object_id'], unique=False)
    op.create_index('ix_client_upload_attached_objects_client_upload_id', 'upload_attached_objects', ['upload_id'], unique=False)
    # ### end Alembic commands ###
