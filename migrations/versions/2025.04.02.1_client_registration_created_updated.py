"""client registration created updated

Revision ID: 2025.04.02.1
Revises: 2025.04.02.0
Create Date: 2025-04-02 19:36:50.255257

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.02.1'
down_revision: Union[str, None] = '2025.04.02.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_registrations', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_registrations', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('reg_agents', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('reg_agents', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))

    op.execute("UPDATE client_registrations SET created_at = '2025-02-10 15:34:00', updated_at = '2025-02-10 15:34:00'")
    op.execute("UPDATE reg_agents SET created_at = '2025-02-10 15:34:00', updated_at = '2025-02-10 15:34:00'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('reg_agents', 'updated_at')
    op.drop_column('reg_agents', 'created_at')
    op.drop_column('client_registrations', 'updated_at')
    op.drop_column('client_registrations', 'created_at')
    # ### end Alembic commands ###
