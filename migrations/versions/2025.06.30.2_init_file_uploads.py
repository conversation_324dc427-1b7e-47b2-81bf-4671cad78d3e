"""init file uploads

Revision ID: 2025.06.30.2
Revises: 2025.06.30.1
Create Date: 2025-06-30 17:13:40.634780

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.30.2'
down_revision: Union[str, None] = '2025.06.30.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('client_init_uploads',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('manager_id', sa.String(length=36), nullable=True),
    sa.Column('detection_id', sa.Integer(), nullable=True),
    sa.Column('detection_item_id', sa.Integer(), nullable=True),
    sa.Column('output', sa.Text(), nullable=True),
    sa.Column('file_id', sa.String(length=36), nullable=True),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['file_id'], ['files.id'], ),
    sa.ForeignKeyConstraint(['manager_id'], ['managers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('files', sa.Column('size', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('files', 'size')
    op.drop_table('client_init_uploads')
    # ### end Alembic commands ###
