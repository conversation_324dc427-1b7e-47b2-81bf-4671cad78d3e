"""rename table payment service

Revision ID: 2025.04.14.0
Revises: 2025.04.03.1
Create Date: 2025-04-14 15:17:41.267631

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.04.14.0'
down_revision: Union[str, None] = '2025.04.03.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.rename_table('payment_systems', 'payment_services')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.rename_table('payment_services', 'payment_systems')
    # ### end Alembic commands ###
