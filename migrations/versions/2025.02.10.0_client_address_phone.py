"""client_address phone

Revision ID: 2025.02.10.0
Revises: 2025.02.06.1
Create Date: 2025-02-10 22:40:48.449575

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.10.0'
down_revision: Union[str, None] = '2025.02.06.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_addresses', sa.Column('phone', sa.String(length=20), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_addresses', 'phone')
    # ### end Alembic commands ###
