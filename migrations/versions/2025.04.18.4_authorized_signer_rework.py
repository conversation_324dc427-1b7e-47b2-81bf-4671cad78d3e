"""authorized signer rework

Revision ID: 2025.04.18.4
Revises: 2025.04.18.3
Create Date: 2025-04-18 16:54:50.774849

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.18.4'
down_revision: Union[str, None] = '2025.04.18.3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    op.add_column('bank_accounts', sa.Column('authorized_signer_person_id', sa.String(length=36), nullable=True))
    op.create_foreign_key(None, 'bank_accounts', 'client_persons', ['authorized_signer_person_id'], ['id'])

    op.execute(""" 
    UPDATE bank_accounts ba SET authorized_signer_person_id = cp.id 
      FROM authorized_signers sg, client_persons cp 
      WHERE ba.authorized_signer_id = sg.id 
      AND cp.full_title = sg.signer_name; 
    """)

    op.drop_constraint('bank_accounts_authorized_signer_id_fkey', 'bank_accounts', type_='foreignkey')
    op.drop_column('bank_accounts', 'authorized_signer_id')

    op.add_column('client_authorized_signers', sa.Column('note', sa.Text(), nullable=True))
    op.add_column('client_authorized_signers', sa.Column('client_person_id', sa.String(length=36), nullable=True))
    op.create_foreign_key(None, 'client_authorized_signers', 'client_persons', ['client_person_id'], ['id'])

    op.execute("""
    UPDATE client_authorized_signers cas SET client_person_id = cp.id
        FROM authorized_signers sg, client_persons cp
        WHERE cas.signer_id = sg.id
        AND cp.full_title = sg.signer_name;
    """)

    op.drop_constraint('client_authorized_signers_signer_id_fkey', 'client_authorized_signers', type_='foreignkey')
    op.drop_column('client_authorized_signers', 'signer_id')
    op.drop_table('authorized_signers')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_authorized_signers', sa.Column('signer_id', sa.VARCHAR(length=36), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'client_authorized_signers', type_='foreignkey')
    op.create_foreign_key('client_authorized_signers_signer_id_fkey', 'client_authorized_signers', 'authorized_signers', ['signer_id'], ['id'])
    op.drop_column('client_authorized_signers', 'note')
    op.drop_column('client_authorized_signers', 'client_person_id')
    op.add_column('bank_accounts', sa.Column('authorized_signer_id', sa.VARCHAR(length=36), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'bank_accounts', type_='foreignkey')
    op.create_foreign_key('bank_accounts_authorized_signer_id_fkey', 'bank_accounts', 'authorized_signers', ['authorized_signer_id'], ['id'])
    op.drop_column('bank_accounts', 'authorized_signer_person_id')
    op.create_table('authorized_signers',
    sa.Column('id', sa.VARCHAR(length=36), autoincrement=False, nullable=False),
    sa.Column('signer_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('note', sa.TEXT(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='authorized_signers_pkey')
    )
    # ### end Alembic commands ###
