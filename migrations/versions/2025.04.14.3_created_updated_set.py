"""created updated set

Revision ID: 2025.04.14.3
Revises: 2025.04.14.2
Create Date: 2025-04-14 15:44:42.087416

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.14.3'
down_revision: Union[str, None] = '2025.04.14.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE clients SET created_at = date, updated_at = changed")
    op.drop_column('clients', 'changed')
    op.drop_column('clients', 'date')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
