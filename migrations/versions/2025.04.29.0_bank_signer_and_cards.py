"""bank signer and cards

Revision ID: 2025.04.29.0
Revises: 2025.04.18.5
Create Date: 2025-04-29 15:49:12.035838

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '2025.04.29.0'
down_revision: Union[str, None] = '2025.04.18.5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'bank_account_authorized_signers',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('bank_account_id', sa.String(length=36), nullable=True),
        sa.Column('client_person_id', sa.String(length=36), nullable=True),
        sa.ForeignKeyConstraint(['bank_account_id'], ['bank_accounts.id'], ),
        sa.ForeignKeyConstraint(['client_person_id'], ['client_persons.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_table(
        'bank_cards',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('client_id', sa.String(length=36), nullable=True),
        sa.Column('bank_account_id', sa.String(length=36), nullable=True),
        sa.Column('card_number', sa.String(), nullable=True),
        sa.Column('last_4_digits', sa.String(), nullable=True),
        sa.Column('cvv', sa.String(), nullable=True),
        sa.Column('card_holder_name', sa.String(), nullable=True),
        sa.Column('valid_through', sa.String(), nullable=True),
        sa.Column('expired_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['bank_account_id'], ['bank_accounts.id'], ),
        sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('bank_cards')
    op.drop_table('bank_account_authorized_signers')
    # ### end Alembic commands ###
