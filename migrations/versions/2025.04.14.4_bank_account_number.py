"""bank account number

Revision ID: 2025.04.14.4
Revises: 2025.04.14.3
Create Date: 2025-04-14 15:56:35.097358

"""
import collections
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.14.4'
down_revision: Union[str, None] = '2025.04.14.3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bank_accounts', sa.Column('account_number', sa.String(length=32), nullable=True))
    conn = op.get_bind()
    clients = {}
    for client in conn.execute(sa.text("SELECT id, account, account_add FROM clients")).fetchall():
        clients[client[0]] = {'id': client[0], 'account': client[1], 'account_add': client[2]}

    all_bank_accounts = collections.defaultdict(list)
    for bank_account in conn.execute(sa.text("SELECT id, client_id, aba_number, bank_name, bank_contact, date_opened, last_renewal, notes FROM bank_accounts")).fetchall():
        all_bank_accounts[bank_account[1]].append({
            'id': bank_account[0], 'aba_number': bank_account[2], 'bank_name': bank_account[3],
            'bank_contact': bank_account[4], 'date_opened': bank_account[5],
            'last_renewal': bank_account[6], 'notes': bank_account[7]
        })

    clients_updated = set()
    for client_id, bank_accounts in all_bank_accounts.items():
        client = clients[client_id]
        if client['account'] and len(bank_accounts) == 1:
            conn.execute(
                sa.text("UPDATE bank_accounts SET account_number = :account_number WHERE id = :id"),
                parameters={'account_number': client['account'], 'id': bank_accounts[0]['id']}
            )
            clients_updated.add(client_id)
            account_add = client['account_add']
            if account_add:
                bank_account = bank_accounts[0]
                # Copy all fields from bank_account and add new account_number
                conn.execute(sa.text(
                    "INSERT INTO bank_accounts (client_id, account_number, aba_number, bank_name, bank_contact, date_opened, last_renewal, notes) "
                    "VALUES (:client_id, :account_number, :aba_number, :bank_name, :bank_contact, :date_opened, :last_renewal, :notes)"),
                    parameters={
                        'client_id': client_id,
                        'account_number': account_add,
                        'aba_number': bank_account['aba_number'],
                        'bank_name': bank_account['bank_name'],
                        'bank_contact': bank_account['bank_contact'],
                        'date_opened': bank_account['date_opened'],
                        'last_renewal': bank_account['last_renewal'],
                        'notes': bank_account['notes']
                    }
                )
            continue
        if client['account'] and len(bank_accounts) == 2:
            account = client['account']
            ba_bank1 = bank_accounts[0]['bank_name'].strip()
            ba_bank2 = bank_accounts[1]['bank_name'].strip()
            numbers = sum(c.isdigit() for c in account)
            if numbers == 12:
                index = 0 if ba_bank1 == 'Bank Of America' else None
                if index is None:
                    index = 1 if ba_bank2 == 'Bank Of America' else None
                if index is None:
                    index = 0
                conn.execute(
                    sa.text("UPDATE bank_accounts SET account_number = :account_number WHERE id = :id"),
                    parameters={'account_number': account, 'id': bank_accounts[index]['id']}
                )
                clients_updated.add(client_id)
                continue
            elif numbers == 9:
                index = 0 if ba_bank1 == 'CHASE' else None
                if index is None:
                    index = 1 if ba_bank2 == 'CHASE' else None
                if index is None:
                    index = 0
                conn.execute(
                    sa.text("UPDATE bank_accounts SET account_number = :account_number WHERE id = :id"),
                    parameters={'account_number': account, 'id': bank_accounts[index]['id']}
                )
                clients_updated.add(client_id)
                continue
            else:
                conn.execute(
                    sa.text("UPDATE bank_accounts SET account_number = :account_number WHERE id = :id"),
                    parameters={'account_number': client['account_add'], 'id': bank_accounts[0]['id']}
                )
                clients_updated.add(client_id)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bank_accounts', 'account_number')
    # ### end Alembic commands ###
