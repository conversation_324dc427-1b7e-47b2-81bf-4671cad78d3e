"""client service id

Revision ID: 2025.04.18.0
Revises: 2025.04.17.0
Create Date: 2025-04-18 15:47:09.591274

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.18.0'
down_revision: Union[str, None] = '2025.04.17.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_contacts', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_contacts', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))

    op.execute("ALTER TABLE client_services ADD COLUMN id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY;")

    op.alter_column('client_services', 'client_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=True)
    op.alter_column('client_services', 'service_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=True)
    op.create_index(op.f('ix_client_services_client_id'), 'client_services', ['client_id'], unique=False)
    op.add_column('client_tasks', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_tasks', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('tax_reporting', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('tax_reporting', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tax_reporting', 'updated_at')
    op.drop_column('tax_reporting', 'created_at')
    op.drop_column('client_tasks', 'updated_at')
    op.drop_column('client_tasks', 'created_at')
    op.drop_index(op.f('ix_client_services_client_id'), table_name='client_services')
    op.alter_column('client_services', 'service_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=False)
    op.alter_column('client_services', 'client_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=False)
    op.drop_column('client_services', 'id')
    op.drop_column('client_contacts', 'updated_at')
    op.drop_column('client_contacts', 'created_at')
    # ### end Alembic commands ###
