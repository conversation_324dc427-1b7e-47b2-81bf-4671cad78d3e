"""reg agent drop address

Revision ID: 2025.06.18.2
Revises: 2025.06.18.1
Create Date: 2025-06-18 23:08:18.133444

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.18.2'
down_revision: Union[str, None] = '2025.06.18.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('reg_agents', 'address')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reg_agents', sa.Column('address', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
