"""remove bank account person

Revision ID: 2025.06.09.0
Revises: 2025.05.14.0
Create Date: 2025-06-09 13:53:28.962959

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.09.0'
down_revision: Union[str, None] = '2025.05.14.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('bank_accounts_client_person_id_fkey', 'bank_accounts', type_='foreignkey')
    op.drop_column('bank_accounts', 'client_person_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bank_accounts', sa.Column('client_person_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.create_foreign_key('bank_accounts_client_person_id_fkey', 'bank_accounts', 'client_persons', ['client_person_id'], ['id'])
    # ### end Alembic commands ###
