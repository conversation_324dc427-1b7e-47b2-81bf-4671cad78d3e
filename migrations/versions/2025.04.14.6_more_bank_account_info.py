"""more bank account info

Revision ID: 2025.04.14.6
Revises: 2025.04.14.5
Create Date: 2025-04-14 17:20:27.763945

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.14.6'
down_revision: Union[str, None] = '2025.04.14.5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bank_accounts', sa.Column('controlled_by', sa.String(), nullable=True))
    op.add_column('bank_accounts', sa.Column('authorized_signer_id', sa.String(length=36), nullable=True))
    op.create_foreign_key(None, 'bank_accounts', 'authorized_signers', ['authorized_signer_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'bank_accounts', type_='foreignkey')
    op.drop_column('bank_accounts', 'authorized_signer_id')
    op.drop_column('bank_accounts', 'controlled_by')
    # ### end Alembic commands ###
