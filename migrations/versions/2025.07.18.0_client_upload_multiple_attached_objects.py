"""client upload multiple attached objects

Revision ID: 2025.07.18.0
Revises: 2025.07.15.0
Create Date: 2025-07-18 17:05:05.098420

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.18.0'
down_revision: Union[str, None] = '2025.07.15.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create the new junction table for multiple attached objects
    op.create_table(
        'client_upload_attached_objects',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('client_upload_id', sa.Integer(), nullable=False),
        sa.Column('object_type', sa.String(length=20), nullable=False),
        sa.Column('object_id', sa.String(length=36), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True, server_default=sa.text('NOW()')),
        sa.ForeignKeyConstraint(['client_upload_id'], ['client_uploads.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('client_upload_id', 'object_type', 'object_id', name='uq_client_upload_attached_objects')
    )

    # Create indexes for better performance
    op.create_index('ix_client_upload_attached_objects_client_upload_id', 'client_upload_attached_objects', ['client_upload_id'])
    op.create_index('ix_client_upload_attached_objects_object_type_id', 'client_upload_attached_objects', ['object_type', 'object_id'])

    # Migrate existing data from the old columns to the new table
    conn = op.get_bind()

    # Insert existing attach_object_id and attach_object_type data into the new table
    conn.execute(sa.text("""
        INSERT INTO client_upload_attached_objects (client_upload_id, object_type, object_id, created_at)
        SELECT id, attach_object_type, attach_object_id, COALESCE(updated_at, created_at, NOW())
        FROM client_uploads
        WHERE attach_object_id IS NOT NULL AND attach_object_type IS NOT NULL
    """))

    # Drop the old columns
    op.drop_column('client_uploads', 'attach_object_type')
    op.drop_column('client_uploads', 'attach_object_id')


def downgrade() -> None:
    # Add back the old columns
    op.add_column('client_uploads', sa.Column('attach_object_id', sa.String(length=36), nullable=True))
    op.add_column('client_uploads', sa.Column('attach_object_type', sa.String(length=20), nullable=True))

    # Migrate data back from the junction table (only the first attached object per upload)
    conn = op.get_bind()
    conn.execute(sa.text("""
        UPDATE client_uploads
        SET attach_object_id = cuao.object_id, attach_object_type = cuao.object_type
        FROM (
            SELECT DISTINCT ON (client_upload_id) client_upload_id, object_type, object_id
            FROM client_upload_attached_objects
            ORDER BY client_upload_id, created_at
        ) cuao
        WHERE client_uploads.id = cuao.client_upload_id
    """))

    # Drop the junction table
    op.drop_index('ix_client_upload_attached_objects_object_type_id', table_name='client_upload_attached_objects')
    op.drop_index('ix_client_upload_attached_objects_client_upload_id', table_name='client_upload_attached_objects')
    op.drop_table('client_upload_attached_objects')
