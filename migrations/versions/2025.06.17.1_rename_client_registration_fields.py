"""rename client registration fields

Revision ID: 2025.06.17.1
Revises: 2025.06.17.0
Create Date: 2025-06-17 14:16:04.181243

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.17.1'
down_revision: Union[str, None] = '2025.06.17.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Rename client_registrations columns
    op.alter_column('client_registrations', 'reg_state', new_column_name='state_of_incorporation')
    op.alter_column('client_registrations', 'reg_date', new_column_name='registered_date')
    op.alter_column('client_registrations', 'deregister_date', new_column_name='terminated_date')
    op.alter_column('client_registrations', 'reg_agent_id', new_column_name='registered_agent_id')
    op.alter_column('client_registrations', 'reg_pay_by', new_column_name='billed_to')
    op.add_column('client_registrations', sa.Column('annual_compliance_due_date', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Revert client_registrations column renames
    op.alter_column('client_registrations', 'billed_to', new_column_name='reg_pay_by')
    op.alter_column('client_registrations', 'registered_agent_id', new_column_name='reg_agent_id')
    op.drop_column('client_registrations', 'annual_compliance_due_date')
    op.alter_column('client_registrations', 'terminated_date', new_column_name='deregister_date')
    op.alter_column('client_registrations', 'registered_date', new_column_name='reg_date')
    op.alter_column('client_registrations', 'state_of_incorporation', new_column_name='reg_state')
    # ### end Alembic commands ###
