"""manager role name

Revision ID: 2025.03.13.0
Revises: 2025.03.12.1
Create Date: 2025-03-13 12:51:56.682628

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

from records.policies import policies

# revision identifiers, used by Alembic.
revision: str = '2025.03.13.0'
down_revision: Union[str, None] = '2025.03.12.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('managers', sa.Column('role_name', sa.String(length=100), nullable=True))
    roles = policies.get_roles()
    role_map = {role['permissions']: role['name'] for role in roles}
    connection = op.get_bind()
    for manager in connection.execute(sa.text('SELECT id, permissions FROM managers')):
        id, permissions = manager
        role_name = role_map.get(permissions, 'user')
        connection.execute(sa.text('UPDATE managers SET role_name = :role_name WHERE id = :id'), {'role_name': role_name, 'id': id})
    # ### end Alembic commands ###
    op.execute('UPDATE managers SET email = users.login FROM users WHERE managers.user_id = users.id')


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('managers', 'role_name')
    # ### end Alembic commands ###
