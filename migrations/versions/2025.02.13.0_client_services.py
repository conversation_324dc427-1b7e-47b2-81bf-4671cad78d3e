"""client_services

Revision ID: 2025.02.13.0
Revises: 2025.02.12.0
Create Date: 2025-02-13 16:13:02.652874

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.13.0'
down_revision: Union[str, None] = '2025.02.12.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_services', 'client_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('client_services', 'service_uid',
               existing_type=sa.VARCHAR(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_services', 'service_uid',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('client_services', 'client_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###
