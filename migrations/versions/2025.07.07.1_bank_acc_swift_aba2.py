"""bank acc swift aba2

Revision ID: 2025.07.07.1
Revises: 2025.07.07.0
Create Date: 2025-07-07 17:53:58.642487

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.07.1'
down_revision: Union[str, None] = '2025.07.07.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bank_accounts', sa.Column('aba_wire_number', sa.String(length=16), nullable=True))
    op.add_column('bank_accounts', sa.Column('swift_code', sa.String(length=16), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bank_accounts', 'swift_code')
    op.drop_column('bank_accounts', 'aba_wire_number')
    # ### end Alembic commands ###
