"""reg_agents

Revision ID: 2025.02.18.1
Revises: 2025.02.18.0
Create Date: 2025-02-18 12:52:44.976654

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.18.1'
down_revision: Union[str, None] = '2025.02.18.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_persons', 'id',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.String(length=36),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('client_persons', 'id',
               existing_type=sa.String(length=36),
               type_=sa.VARCHAR(length=255),
               existing_nullable=False)
    # ### end Alembic commands ###
