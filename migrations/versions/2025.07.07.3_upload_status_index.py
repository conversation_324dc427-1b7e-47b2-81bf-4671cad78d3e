"""upload status index

Revision ID: 2025.07.07.3
Revises: 2025.07.07.2
Create Date: 2025-07-07 18:06:41.379436

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.07.3'
down_revision: Union[str, None] = '2025.07.07.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_client_uploads_status'), 'client_uploads', ['status'], unique=False)
    op.execute("UPDATE client_uploads SET status = 'REVIEW' WHERE status IS NULL")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_client_uploads_status'), table_name='client_uploads')
    # ### end Alembic commands ###
