"""upload attach object

Revision ID: 2025.07.10.0
Revises: 2025.07.09.0
Create Date: 2025-07-10 18:18:20.741489

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.10.0'
down_revision: Union[str, None] = '2025.07.09.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_uploads', sa.Column('attach_object_id', sa.String(length=36), nullable=True))
    op.add_column('client_uploads', sa.Column('attach_object_type', sa.String(length=20), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_uploads', 'attach_object_type')
    op.drop_column('client_uploads', 'attach_object_id')
    # ### end Alembic commands ###
