"""task manager

Revision ID: 2025.04.18.2
Revises: 2025.04.18.1
Create Date: 2025-04-18 16:38:19.043395

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.18.2'
down_revision: Union[str, None] = '2025.04.18.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_tasks', sa.Column('manager_id', sa.String(length=36), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_tasks', 'manager_id')
    # ### end Alembic commands ###
