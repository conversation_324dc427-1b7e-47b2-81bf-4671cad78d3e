"""users as managers

Revision ID: 2025.03.12.0
Revises: 2025.03.11.2
Create Date: 2025-03-12 17:43:09.657135

"""
import uuid
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.12.0'
down_revision: Union[str, None] = '2025.03.11.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    res = conn.execute(sa.text(
        "SELECT id, name, login FROM users;"
    ))
    results = res.fetchall()
    users = [
        {'id': o[0], 'name': o[1], 'login': o[2]}
        for o in results
    ]
    for user in users:
        new_uuid = str(uuid.uuid4())
        res = conn.execute(sa.text(
            "INSERT INTO managers (uid, user_id, title, email, permissions, created_at, updated_at) "
            "VALUES (:uid, :user_id, :title, :email, :permissions, now(), now());"), {
                'uid': new_uuid,
                'user_id': user['id'],
                'title': user['name'],
                'email': user['login'],
                'permissions': 1,
            })

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
