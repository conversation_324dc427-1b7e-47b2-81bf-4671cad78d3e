"""files remove columns

Revision ID: 2025.03.26.0
Revises: 2025.03.20.1
Create Date: 2025-03-26 09:22:51.756146

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.26.0'
down_revision: Union[str, None] = '2025.03.20.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('files', 'url')
    op.drop_column('files', 'manager')
    op.drop_column('files', 'uploaded')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('files', sa.Column('uploaded', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('files', sa.Column('manager', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('files', sa.Column('url', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
