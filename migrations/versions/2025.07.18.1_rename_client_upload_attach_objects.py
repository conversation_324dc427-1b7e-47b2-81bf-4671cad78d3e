"""rename client upload attach objects

Revision ID: 2025.07.18.1
Revises: 2025.07.18.0
Create Date: 2025-07-18 18:55:43.649203

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.07.18.1'
down_revision: Union[str, None] = '2025.07.18.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.rename_table('client_upload_attached_objects', 'upload_attached_objects')
    op.alter_column('upload_attached_objects', 'client_upload_id', new_column_name='upload_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('upload_attached_objects', 'upload_id', new_column_name='client_upload_id')
    op.rename_table('upload_attached_objects', 'client_upload_attached_objects')
    # ### end Alembic commands ###
