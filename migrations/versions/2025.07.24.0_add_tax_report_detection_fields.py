"""add tax report detection fields to client uploads

Revision ID: 2025.07.24.0
Revises: 2025.07.21.0
Create Date: 2025-07-24 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.24.0'
down_revision: Union[str, None] = '2025.07.21.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add tax report detection fields to client_uploads table
    op.add_column('client_uploads', sa.Column('tax_report_detection_id', sa.Integer(), nullable=True))
    op.add_column('client_uploads', sa.Column('tax_report_detection_item_id', sa.Integer(), nullable=True))


def downgrade() -> None:
    # Remove tax report detection fields from client_uploads table
    op.drop_column('client_uploads', 'tax_report_detection_item_id')
    op.drop_column('client_uploads', 'tax_report_detection_id')
