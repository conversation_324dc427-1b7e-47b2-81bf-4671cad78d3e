"""signers migrate

Revision ID: 2025.04.29.1
Revises: 2025.04.29.0
Create Date: 2025-04-29 15:55:05.995234

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.29.1'
down_revision: Union[str, None] = '2025.04.29.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    bank_accounts = conn.execute(sa.text("SELECT id, authorized_signer_person_id FROM bank_accounts")).fetchall()
    for bank_account in bank_accounts:
        account_id, authorized_signer_person_id = bank_account
        if authorized_signer_person_id:
            conn.execute(
                sa.text("INSERT INTO bank_account_authorized_signers (bank_account_id, client_person_id) VALUES"
                        " (:account_id, :authorized_signer_person_id)"),
                parameters={
                    'account_id': account_id,
                    'authorized_signer_person_id': authorized_signer_person_id
                }
            )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
