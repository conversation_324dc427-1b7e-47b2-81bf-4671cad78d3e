"""upload fields

Revision ID: 2025.07.07.0
Revises: 2025.07.05.0
Create Date: 2025-07-07 16:56:28.236573

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.07.07.0'
down_revision: Union[str, None] = '2025.07.05.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_uploads', sa.Column('client', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('client_uploads', sa.Column('client_output', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.drop_column('client_uploads', 'message')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_uploads', sa.Column('message', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_column('client_uploads', 'client_output')
    op.drop_column('client_uploads', 'client')
    # ### end Alembic commands ###
