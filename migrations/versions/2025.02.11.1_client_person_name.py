"""client person id

Revision ID: 2025.02.11.1
Revises: 2025.02.11.0
Create Date: 2025-02-11 17:29:01.822170

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.02.11.1'
down_revision: Union[str, None] = '2025.02.11.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE client_persons RENAME column title to name;")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE client_persons RENAME column name to title;")
    # ### end Alembic commands ###
