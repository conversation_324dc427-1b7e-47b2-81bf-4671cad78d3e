"""managers id

Revision ID: 2025.03.12.1
Revises: 2025.03.12.0
Create Date: 2025-03-12 18:14:03.281428

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.12.1'
down_revision: Union[str, None] = '2025.03.12.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE managers DROP CONSTRAINT managers_pkey;")
    op.execute("ALTER TABLE managers DROP COLUMN id;")
    op.execute("ALTER TABLE managers RENAME COLUMN uid TO id;")
    op.execute("ALTER TABLE managers ADD CONSTRAINT managers_pkey PRIMARY KEY (id)")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE managers DROP CONSTRAINT managers_pkey;")
    op.execute("ALTER TABLE managers DROP COLUMN id;")
    op.execute("ALTER TABLE managers RENAME COLUMN id TO uid;")
    op.execute("ALTER TABLE managers ADD COLUMN id SERIAL;")
    op.execute("ALTER TABLE managers ADD CONSTRAINT managers_pkey PRIMARY KEY (id)")
    # ### end Alembic commands ###
