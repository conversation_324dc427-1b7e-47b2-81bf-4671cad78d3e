"""add share type to cap table

Revision ID: 2025.07.20.1
Revises: 2025.07.20.0
Create Date: 2025-07-20 23:46:51.417839

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.07.20.1'
down_revision: Union[str, None] = '2025.07.20.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_capitalizations', sa.Column('share_type', sa.String(length=20), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_capitalizations', 'share_type')
    # ### end Alembic commands ###
