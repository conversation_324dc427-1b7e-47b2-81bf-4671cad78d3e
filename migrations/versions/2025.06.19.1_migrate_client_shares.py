"""migrate client_shares

Revision ID: 2025.06.19.1
Revises: 2025.06.19.0
Create Date: 2025-06-19 13:31:36.408397

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.19.1'
down_revision: Union[str, None] = '2025.06.19.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_shares', sa.Column('type', sa.String(length=20), nullable=True))
    conn = op.get_bind()
    all_client_shares = conn.execute(sa.text(
        "SELECT id, client_id, stock_authorized, stock_issued, shares_authorized_preferred, shares_issued_preferred, notes FROM client_shares"
    )).fetchall()
    all_client_shares = [
        {
            'id': client_share[0], 'client_id': client_share[1], 'stock_authorized': client_share[2],
            'stock_issued': client_share[3], 'shares_authorized_preferred': client_share[4],
            'shares_issued_preferred': client_share[5], 'notes': client_share[6]
        }
        for client_share in all_client_shares
    ]
    # Delete all
    conn.execute(sa.text("DELETE FROM client_shares"))
    print(f'Deleted {len(all_client_shares)} client shares records')
    # stock_issued / stock_authorized => type=Common
    # shares_issued_preferred / shares_authorized_preferred => type=Preferred

    for client_share in all_client_shares:
        conn.execute(
            sa.text(
                "INSERT INTO client_shares (client_id, stock_authorized, stock_issued, notes, type) VALUES "
                "(:client_id, :stock_authorized, :stock_issued, :notes, 'Common')"
            ),
            parameters={
                'client_id': client_share['client_id'], 'stock_authorized': client_share['stock_authorized'],
                'stock_issued': client_share['stock_issued'], 'notes': client_share['notes']
            }
        )

        conn.execute(
            sa.text(
                "INSERT INTO client_shares (client_id, stock_authorized, stock_issued, notes, type) VALUES "
                "(:client_id, :shares_authorized_preferred, :shares_issued_preferred, :notes, 'Preferred')"
            ),
            parameters={
                'client_id': client_share['client_id'], 'shares_authorized_preferred': int(client_share['shares_authorized_preferred'] or 0),
                'shares_issued_preferred': client_share['shares_issued_preferred'], 'notes': client_share['notes']
            }
        )

    print(f'Created {len(all_client_shares) * 2} client shares records')
    # Drop preferred columns
    op.drop_column('client_shares', 'shares_authorized_preferred')
    op.drop_column('client_shares', 'shares_issued_preferred')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('client_shares', 'type')
    # ### end Alembic commands ###
