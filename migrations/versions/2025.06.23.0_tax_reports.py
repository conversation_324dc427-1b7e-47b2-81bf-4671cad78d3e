"""tax reports

Revision ID: 2025.06.23.0
Revises: 2025.06.22.1
Create Date: 2025-06-23 16:48:03.500790

"""
import datetime
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.06.23.0'
down_revision: Union[str, None] = '2025.06.22.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Move all tax reporting to tax_reports
    # Select all tax_reporting
    conn = op.get_bind()
    op.execute("DELETE FROM tax_reporting WHERE year IS NULL;")
    op.add_column('tax_reports', sa.Column('is_reporting_1099', sa.<PERSON>(), nullable=True))

    all_tax_reporting = conn.execute(sa.text(
        "SELECT id, client_id, year, reporting_1099, tax_return_by, note, files, created_at, updated_at FROM tax_reporting"
    )).fetchall()
    all_tax_reporting = [
        {
            'id': tax_reporting[0], 'client_id': tax_reporting[1], 'year': tax_reporting[2],
            'reporting_1099': tax_reporting[3], 'tax_return_by': tax_reporting[4], 'note': tax_reporting[5],
            'files': tax_reporting[6], 'created_at': tax_reporting[7], 'updated_at': tax_reporting[8]
        }
        for tax_reporting in all_tax_reporting
    ]
    for tax_reporting in all_tax_reporting:
        year = tax_reporting['year']
        # set due date to 15 April next year
        due_date = datetime.datetime(int(year) + 1, 4, 15, tzinfo=datetime.timezone.utc)
        conn.execute(
            sa.text(
                "INSERT INTO tax_reports ("
                "client_id, name, fiscal_year, assignee_manager_id, status, note, file_id, file_name, is_reporting_1099, "
                "is_burning, notification_triggered_at, notification_dismissed_at, due_date, created_at, updated_at) VALUES "
                "(:client_id, :name, :fiscal_year, :assignee_manager_id, :status, :note, :file_id, :file_name, :is_reporting_1099, "
                ":is_burning, :notification_triggered_at, :notification_dismissed_at, :due_date, :created_at, :updated_at)"
            ),
            parameters={
                'client_id': tax_reporting['client_id'], 'name': f'Annual Tax Report {tax_reporting["year"]}',
                'fiscal_year': tax_reporting['year'], 'assignee_manager_id': None, 'status': 'COMPLETED',
                'note': tax_reporting['note'], 'file_id': None, 'file_name': None, 'is_reporting_1099': tax_reporting['reporting_1099'] == 'Yes',
                'is_burning': False, 'notification_triggered_at': None, 'notification_dismissed_at': None, 'due_date': due_date,
                'created_at': datetime.datetime.now(datetime.timezone.utc), 'updated_at': datetime.datetime.now(datetime.timezone.utc)
            }
        )

    op.drop_table('tax_reporting')


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tax_reporting',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('client_id', sa.VARCHAR(length=36), autoincrement=False, nullable=True),
    sa.Column('year', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('reporting_1099', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('tax_return_by', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('note', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('files', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], name='tax_reporting_client_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='tax_reporting_pkey')
    )
    op.drop_column('tax_reports', 'is_reporting_1099')
    # ### end Alembic commands ###
