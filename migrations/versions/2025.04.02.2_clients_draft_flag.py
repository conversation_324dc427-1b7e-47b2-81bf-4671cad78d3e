"""clients draft flag

Revision ID: 2025.04.02.2
Revises: 2025.04.02.1
Create Date: 2025-04-02 21:49:13.914712

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.04.02.2'
down_revision: Union[str, None] = '2025.04.02.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clients', sa.Column('internal_draft_flag', sa.<PERSON>(), nullable=True))
    op.execute('UPDATE clients SET internal_draft_flag = FALSE')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('clients', 'internal_draft_flag')
    # ### end Alembic commands ###
