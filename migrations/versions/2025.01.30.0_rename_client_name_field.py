"""rename client name field

Revision ID: 2025.01.30.0
Revises: 2025.01.29.6
Create Date: 2025-01-30 12:14:35.706115

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.01.30.0'
down_revision: Union[str, None] = '2025.01.29.6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('clients', 'client', new_column_name='name')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('clients', 'name', new_column_name='client')
    # ### end Alembic commands ###
