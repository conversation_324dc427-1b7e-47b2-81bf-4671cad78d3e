"""files desc

Revision ID: 2025.03.20.0
Revises: 2025.03.19.0
Create Date: 2025-03-20 13:28:39.613584

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.03.20.0'
down_revision: Union[str, None] = '2025.03.19.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE files ADD COLUMN IF NOT EXISTS description TEXT")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('files', 'description')
    # ### end Alembic commands ###
