"""address dump

Revision ID: 2025.01.29.3
Revises: 2025.01.29.2
Create Date: 2025-01-29 13:52:42.406846

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.01.29.3'
down_revision: Union[str, None] = '2025.01.29.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('addresses', sa.Column('uid', sa.String(), nullable=True))
    op.alter_column('addresses', 'id',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('addresses_id_seq'::regclass)"),
            postgresql_using='cast(id as integer)'
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('addresses', 'id',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('addresses_id_seq'::regclass)"))
    op.drop_column('addresses', 'uid')
    # ### end Alembic commands ###
