"""rename client_init_uploads to client_uploads

Revision ID: 2025.06.30.3
Revises: 2025.06.30.2
Create Date: 2025-06-30 17:27:35.490266

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.06.30.3'
down_revision: Union[str, None] = '2025.06.30.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.rename_table('client_init_uploads', 'client_uploads')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.rename_table('client_uploads', 'client_init_uploads')
    # ### end Alembic commands ###
